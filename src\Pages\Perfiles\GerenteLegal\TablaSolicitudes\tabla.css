
/*MODAL ASIGNAR*/
.modal-aceptar {
    position: absolute;
    top: 37%;
    right: 28rem;
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
    border-radius: 1.5rem;
    z-index: 10;
    width: 20%;
    border: 0.0625rem solid #ccc;
  }
  .modal-tags{
    position: absolute;
    top: 37%;
    right: 23.125rem;
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
    border-radius: 1.5rem;
    z-index: 10;
    width: 30%;
    border: 0.0625rem solid #ccc;
  }
  .modal-aprobadores{
    position: absolute;
    top: 30%;
    right: 0;
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
    border-radius:  1.5rem 0 0 1.5rem  ;
    z-index: 10;
    width: 25%;
    border: 0.0625rem solid #ccc;
  
  }
  .titulo-modal-aprobadores{
    font-size: 1.5rem;
    padding: 0.9375rem;
    display: flex;
    flex-direction: column;
    align-items:start;
    justify-content: center;
  }
  .subtitulo-modal-aprobadores{
    font-size: 1.3rem;
    padding: 0.9375rem;
    display: flex;
    flex-direction: column;
    align-items:start;
    justify-content: center;
    margin-top: 1.3rem;
  }
  .lista-solicitante-gestor-aprobantes{
    margin-left: 0.9375rem;
    display: flex;
    flex-direction: column;
    gap: 0.3125rem;
  }
  .lista-solicitante-gestor-aprobantes li{
    list-style: none;
    font-size: 1rem
    
  }
  .lista-solicitante-gestor-aprobantes > li > span{
    list-style: none;
    font-size: 1rem;
    color: #294FCF;
  }

  /*MODAL DE FILTROS*/
.modal-filtros-gestor {
    position: absolute;
    top: 28%;
    left: 5rem;
    background: white;
    padding: 1rem 2.5rem;
    box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
    border-radius: 1.5rem;
    z-index: 10;
    width: 25%;
    border : 0.0625rem solid #ccc;

  }
  .lista-aprobadores-checkbox{
    padding: 0.625rem 1.9rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    gap: 8px;
  }

  .orden-seleccionado{
    margin-left: 0.625rem;
    border-radius: 1.3rem;
    padding: 3px 8px;
    color: white;
    background: #294FCF;
    font-size: 0.75rem;
  }
  .form-check-input{
    appearance: none;
  }
  .botones-aprobadores{
    width: 100%;
    display: flex;
    gap: 0.625rem;
    justify-content: right;
    align-items: center;
    margin-top: 0.9375rem;
    margin-bottom: 0.9375rem;

  }
  .container-botones {
    width: 60%;
    display: flex;
    gap: 0.625rem;
  }
  .container-botones > button {
    font-size: 0.75rem;
  }
  .div-inputs-tags{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.9375rem;
  }
  .inputs-tags{
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 0.9375rem;
  }
  .modalDocFirmado{
    width: 100%;
    margin-top: 0.625rem;
    margin-bottom: 1.508rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: left;
  }
  .modalDocFirmado .nombre_archivo{
    color: #294FCF;
  }
  .input-fecha-firmado {
    display: flex;
    justify-content: left;
    align-items: baseline;
    gap: 0.9375rem;
    width: 100%;
    margin-top: 0.9375rem;
    margin-bottom: 0.9375rem;
  }
  .input-fecha-firmado .css-1d3z3hw-MuiOutlinedInput-notchedOutline{
    border-color: #2c4cb380;
    border-radius: 0.3125rem;
    padding: 0.3125rem;
  }
  .input-fecha-firmado label {
    font-size: 1rem;
  }
  .accordion-button:not(.collapsed) {
    background-color: white;
    box-shadow: none;
  }
  .input-fecha-firmado input {
    border-color: #2c4cb380;
  }

  .checkbox-filtro{
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    justify-content: start;
    align-items: start;
  }