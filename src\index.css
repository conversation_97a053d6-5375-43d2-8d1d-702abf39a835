*{
  margin: 0;
  padding: 0;
}
#root{
  background: #F9FEFF !important;
}
.form-control{
  border-color:  #C5DBFF !important;
  border-radius: 0.6rem !important;
}
.form-control:focus{
  box-shadow: 0px 0px 0.25rem 0.125rem #156CFF42 !important;
  border-color: #156CFF !important;
}
.form-select{
  border-color:  #C5DBFF !important;
  border-radius: 0.6rem !important;
}
.form-select:focus{
  box-shadow: 0px 0px 0.25rem 0.125rem #156CFF42 !important;
  border-color: #156CFF !important;
}
.is-invalid{
  box-shadow: 0px 0px  0.25rem 0.125rem #FF1E1E24 !important;
  border-color: #FF1E1E !important;
}
.css-13cymwt-control{
  border-color: #C5DBFF !important;
  border-radius: 0.6rem !important;
}
.css-15lsz6c-indicatorContainer svg{
  fill: #156CFF !important;
}
.css-t3ipsp-control:hover{
  border-radius: 0.6rem !important;
  box-shadow: 0px 0px 0.25rem 0.125rem #156CFF42 !important;
  border-color: #156CFF !important;
}
.css-t3ipsp-control{
  border-radius: 0.6rem !important;
  box-shadow: 0px 0px 0.25rem 0.125rem #156CFF42 !important;
  border-color: #156CFF !important;
}
.css-1nmdiq5-menu{
  border-radius: 0.6rem !important;
  border-color: #C5DBFF !important;

}
 .check-group   {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 2rem;
  border-radius: 0.5rem;
  border:0.0625rem solid #C5DBFF;
   color: #C5DBFF ;

 }
 .check-selected   {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 2rem;
  border-radius: 0.5rem;
  border:0.0625rem solid #156CFF !important;
  box-shadow: 0px 0px 0.25rem 0.125rem #156CFF42 !important;
  color: #156CFF ;

 }
 .check-group .form-check-input{
  margin-top: 0.05rem !important;
  color: #156CFF !important;
  background-image:none;
  box-shadow: none;
  max-width: 0.2rem !important;
  padding: 0.38rem !important;

}

.check-group  .form-check-label{
  color: #C5DBFF !important;
  font-size: 0.9rem;
  display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.4rem;
    line-height: 1rem;
}
.check-selected  .form-check-label{
  color: #156CFF !important;

}
.icono-barralateral-acciones{
  cursor: pointer;
}
 .numero-pagina:hover svg path{
  fill: #fff !important;

 }
 .no-clausulas-mensaje {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 1rem 0;
}
 .form-check-inline{
  margin-right: 0.5rem !important;
}
.montserrat-font {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}
.montserrat-font-500 {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
}
.lato-font {
  font-family: "Lato", sans-serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
}
.lato-font-400 {
  font-family: "Lato", sans-serif;
  font-optical-sizing: auto;
  font-weight:400;
  font-style: normal;
}
.comfortaa-font{
  font-family: "Comfortaa", serif;
  font-optical-sizing: auto;
  font-weight:500 !important;
  font-style: normal;
}
.comfortaa-font-600{
  font-family: "Comfortaa", serif;
  font-optical-sizing: auto;
  font-weight:600 !important;
  font-style: normal;
}
.accordion-header{
  border-bottom: 0.0625rem solid #e5e5e5;
}
.size-titulos{
  font-size: 1.75rem;
}
i{
font-size: 1.3rem;
}
.cursor-pointer{
  cursor: pointer;
}
.accordion-button{
  color: #156CFF;
  font-size: 1.3rem;
  font-weight: 500;
  font-style: normal;
}
.accordion-button:not(.collapsed){
  color: #156CFF !important;
}
.accordion-item .collapsed{
  color: #156CFF !important;
}
.separador-linea{
  margin-top: 1.3rem;
  margin-bottom: 0.9375rem;
  color : #2b67f6;
}
.css-1dimb5e-singleValue{
  font-size: 1rem;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.form-select {
  background-size: 1rem 0.75rem !important;
}
.alerta-fecha-proxima{
  background-color: #FFF6C9;
}
.alerta-fecha-proxima:hover{
  background-color: #FFF6C9 !important;
}
.alerta-fecha-pasada{
  background-color: #FFEAEA;
}
.alerta-fecha-pasada:hover{
  background-color: #FFEAEA !important;
}
.MuiTooltip-popper[data-popper-placement*="top"] .css-3v9qdb-MuiTooltip-tooltip {
  font-size: 1rem !important;

}/* Base 100% scaling (96dpi) */
@media screen and (max-resolution: 96dpi) {
  html {
    font-size: 16px; /* 1rem = 16px */
  }
}
/* 110% scaling (~105dpi) */
@media screen and (min-resolution: 105dpi) and (max-resolution: 110dpi) {
  html {
    font-size: 14.5px;
  }
}
/* 125% scaling (~120dpi) */
@media screen and (min-resolution: 120dpi) and (max-resolution: 143dpi) {
  html {
    font-size: 12.8px;
  }
}
/* 150% scaling (~144dpi) */
@media screen and (min-resolution: 144dpi) and (max-resolution: 159dpi) {
  html {
    font-size: 10.67px;
  }
}
/* 175% scaling (~168dpi) */
@media screen and (min-resolution: 160dpi) and (max-resolution: 191dpi) {
  html {
    font-size: 9.14px;
  }
}
/* 200% scaling (~192dpi) */
@media screen and (min-resolution: 192dpi) and (max-resolution: 239dpi) {
  html {
    font-size: 8px;
  }
}
/* 225% scaling (~216dpi) */
@media screen and (min-resolution: 240dpi) and (max-resolution: 263dpi) {
  html {
    font-size: 7.11px;
  }
}
/* 250% scaling (~240dpi) */
@media screen and (min-resolution: 264dpi) and (max-resolution: 287dpi) {
  html {
    font-size: 6.4px;
  }
}
/* 300% scaling (~288dpi) */
@media screen and (min-resolution: 288dpi) and (max-resolution: 335dpi) {
  html {
    font-size: 5.33px;
  }
}
/* 350% scaling (~336dpi) */
@media screen and (min-resolution: 336dpi) and (max-resolution: 383dpi) {
  html {
    font-size: 4.57px;
  }
}
/* 400% scaling (~384dpi) */
@media screen and (min-resolution: 384dpi) and (max-resolution: 431dpi) {
  html {
    font-size: 4px;
  }
}
/* 450% scaling (~432dpi) */
@media screen and (min-resolution: 432dpi) and (max-resolution: 479dpi) {
  html {
    font-size: 3.56px;
  }
}
/* 500% scaling (~480dpi) */
@media screen and (min-resolution: 480dpi) {
  html {
    font-size: 3.2px;
  }
}
/* Ajustes para pantallas grandes (3840px x 2160px) */
@media screen and (min-width: 3840px) {
  html {
    font-size: 17px; /* Aumentar el tamaño de fuente para pantallas 4K */
  }
}
/* Para pantallas más pequeñas */
@media screen and (max-width: 768px) {
  html {
    font-size: 14px; /* Ajuste para pantallas medianas (tabletas) */
  }
}
/* Para pantallas móviles */
@media screen and (max-width: 480px) {
  html {
    font-size: 12px; /* Ajuste adicional para pantallas pequeñas */
  }

  svg {
    width: 1rem;
    height: 1rem;
  }

  i {
    font-size: 1rem;
  }
}