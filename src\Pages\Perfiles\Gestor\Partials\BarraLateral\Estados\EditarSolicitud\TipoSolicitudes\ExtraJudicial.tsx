import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>abe<PERSON>, Stepper } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import Cookies from "js-cookie";
import axios from "axios";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import { validateToken } from "../../../../../../../Components/Services/TokenService";
import API_GESTOR from "../../../../../../../../assets/Api/ApisGestor";
import API_SOLICITANTE from "../../../../../../../../assets/Api/ApisSolicitante";
import { RoutesPrivate } from "../../../../../../../../Security/Routes/ProtectedRoute";
import BarraLateralCrearAdenda from "../../../../../../Solicitante/CrearAdendas/BarraLateralCrearAdenda/BarraLateralCrearAdenda";
import BarraLateralCrearEJ from "../BarraLateralEJ";
import getLogs from "../../../../../../../Components/Services/LogsService";


dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");

interface Errors {
  int_idEmpresa?: string;
  int_idUnidadNegocio?: string;
  str_Documento?: string;
  str_Interlocutor?: string;
  db_Honorarios?: string;
  dt_FechaEsperada?: string;
  str_Correo?: string;
  str_RepLegal?: string;
  str_ObjetivoContrato?: string;
  db_Presupuesto?: string;
  str_PlazoSolicitud?: string;
  str_TipoServicio?: string;
  str_CondicionPago?: string;
  str_BienDescripcion?: string;
  str_BienPartidaCertificada?: string;
  str_BienDireccion?: string;
  str_ImporteVenta?: string;
  str_FormaPago?: string;
  str_BienUso?: string;
  str_PlazoArriendo?: string;
  str_RentaPactada?: string;
  str_InteresRetraso?: string;
  str_ReajusteRenta?: string;
  str_Penalidades?: string;
}
interface PrestacionServicio {
  UsuarioId: number | null;
  idAplicacion: number | null;
  Suscriptor: number | null;
  Suscripcion: string | null;
  selectedSolicitud: object | null;
  Asignado: boolean;
}
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
const ExtraJudicial: React.FC<PrestacionServicio> = ({
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  Asignado,
  documentoSubido,
  Nombres,
  Apellidos,
  gestor,
  ver
}) => {
  const [files, setFiles] = useState([]);
  const [filesEJ, setFilesEJ] = useState([]);

  const token = Cookies.get("Token");


  const [activeStep, setActiveStep] = useState(0);
  const [FechaMinima , setFechaMinima] = useState(getLocalDate());

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };
  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioCreacion: UsuarioId,
    int_idEmpresa: selectedSolicitud.int_idEmpresa,
    int_idUnidadNegocio: selectedSolicitud.int_idUnidadNegocio,
    int_idTipoSol: selectedSolicitud.int_idTipoSol,
    int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
    str_DeTerceros: selectedSolicitud.str_DeTerceros,
    dt_FechaEsperada: selectedSolicitud.dt_FechaEsperada,
    db_Honorarios: selectedSolicitud.db_Honorarios,
    str_idSuscriptor: Suscripcion,
    int_idClienteAsociado: selectedSolicitud.int_idClienteAsociado,
    str_CodSolicitudes: selectedSolicitud.str_CodSolicitudes,
    int_idSolicitante:  UsuarioId,
    dt_FechaFirmaEJ:new Date().toISOString().split("T")[0],
    str_DetalleAcuerdo:"",
    str_documentoFirmante1:"",
    str_documentoFirmante2:"",
    str_Firmante1:"",
    str_Firmante2:"",

  });
 
  const steps = [
    "DATOS GENERALES",
    "DATOS DEL ACUERDO EXTRAJUDICIAL",
 
  ];

  useEffect(() => {

    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
            try {
              const response = await axios.get(
                API_GESTOR["ObtenerTiempoRespuestaTS"](Suscripcion,selectedSolicitud.int_idTipoSol, selectedSolicitud.int_idEmpresa),{
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                  }
                }
              );
              const responseData = response.data;
              const ultimoObjeto = responseData.length > 0 ? responseData[responseData.length - 1] : null;
      
              const nuevaFechaEsperada = addDaysToDate(new Date(), ultimoObjeto.int_TiempoRespuesta ? ultimoObjeto.int_TiempoRespuesta : 0);
              setFechaMinima(nuevaFechaEsperada);
              setFormDataSolicitud(prevState => ({
                ...prevState,
                dt_FechaEsperada: nuevaFechaEsperada,
              }));
            } catch (error) {
              console.error("Error al obtener tipos de solicitud:", error);
            }
          };
    ObtenerTiempoRespuestaTipoSolicitud();
  }, []);



 
  const handleFileChange = (event) => {
    
    const fileArray = Array.from(event.target.files);
    setFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };

  const handleFileRemove = (index) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
 
  const handleFileChangeEJ = (event) => {
    
    const file = event.target.files[0]; 
    if (file) {
      setFilesEJ([file]); 
      event.target.value = null;
    }
    event.target.value = null;
  };
  
  const handleFileRemoveEJ = () => {
    setFilesEJ([]); 
    
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));

  };



  //--------------------------------------------------------------------------------

  const handleSubmitConfirmarSolicitud = async () => {
    try {
   
      const response = await axios.post(
        API_GESTOR["CrearEJ"](),
        formDataSolicitud,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
        return response.data;
      } else {
        return null;
      }

      
    } catch (error) {
      return error
    }
  };
  const handleSubmitContenidoSolicitud = async (
    solicitud
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        int_idSolicitudes: solicitud.int_idSolicitudes,
        str_idSuscriptor: Suscripcion,
      };
      const response = await axios.post(
        API_GESTOR["InsertarContenidoSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      
    } catch (error) {
      return error
    }
  };
  const handleSubmitFiles = async (Solicitud) => {
    await validateToken();
    try {
      for (const file of files) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          Solicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          Solicitud.int_idSolicitudes
        );
        formDataSolicitud.append("str_CodTipoDocumento", "DOFI");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }

      }

      
    } catch (error) {
      
    }
  };
  const handleSubmitFilesEJ = async (Solicitud) => {
    await validateToken();
    try {
      for (const file of filesEJ) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          Solicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          Solicitud.int_idSolicitudes
        );
        formDataSolicitud.append("str_CodTipoDocumento", "COAP");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }

      }

      
    } catch (error) {
      
    }
  };
  const CambiarEstado = async (Solicitud,Estado) => {
    try {
      await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado:Estado,
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: Solicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
const handleAsignarGestor = async (idSolicitud) => {
  await validateToken();
  try {
    const response = await axios.put(API_SOLICITANTE["AsignarGestor"](), {
      int_idSolicitudes: idSolicitud,
      int_idGestor: UsuarioId,
      int_idUsuarioModificacion: UsuarioId,
    },{
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    });

    if (response.status === 200) {
      console.error(`Gestor Asignado Correctamente ${response}`);

    } else {
      console.error(`Error: código de estado ${response.status}`);
    }

  } catch (error) {
    return error
  }
};
  const handleSubmitSolicitudConfirmar = async () => {
    
    await validateToken();
    if(filesEJ.length ===0){
      Swal.fire("","No se ha cargado ningun documento","error");
      return
    }

    let success = true;
    let errorMessages = [];

    try {
      let idSolicitud = null;

        idSolicitud = await handleSubmitConfirmarSolicitud().catch(
          (err) => {
            success = false;
            errorMessages.push("Error al confirmar solicitud: " + err.message);
          }
        );

      await handleSubmitContenidoSolicitud(
        idSolicitud,
      ).catch((err) => {
        success = false;
        errorMessages.push("Error al subir archivos: " + err.message);
      });
      if(files.length > 0){
        await handleSubmitFiles(idSolicitud).catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      await handleSubmitFilesEJ(idSolicitud).catch((err) => {
        success = false;
        errorMessages.push("Error al subir archivos: " + err.message);
      });
      await handleAsignarGestor(idSolicitud.int_idSolicitudes);
      await CambiarEstado(idSolicitud,"Firmado")

      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Confirmada.",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
           await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),idSolicitud,"Solicitudes","Solicitudes","Crear ExtraJudicial","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };

  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={Nombres + " " + Apellidos}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Fecha de Registro: </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        min={getLocalDate()}
                        className="form-control"
                        value={getLocalDate()}
                        name="dt_FechaRegistro"
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_NombreEmpresa"
                        value={selectedSolicitud.Empresa_nombre}
                        readOnly
                      />
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Descripcion_UnidadNegocio"
                        value={selectedSolicitud.UN_nombre}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Datos del Acuerdo Extra Judicial
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud" style={{alignItems:"flex-start"}}>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha de Firma del Acuerdo:
                      </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        className="form-control"
                        value={
                          formDataSolicitud.dt_FechaFirmaEJ
                            ? formDataSolicitud.dt_FechaFirmaEJ.split("T")[0]
                            : ""
                        }
                        onChange={handleInputChange}
                        name="dt_FechaFirmaEJ"
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Detalle del Acuerdo:</label>
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleAcuerdo"
                        rows={3}
                        value={formDataSolicitud.str_DetalleAcuerdo}
                        onChange={handleTextareaChange}
                      ></textarea>
                      
                    </div>
                  </div>

                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Nombres del Firmante 1 :</label>
                      <input
                        className="form-control"
                        id=""
                        name="str_Firmante1"
                        value={formDataSolicitud.str_Firmante1}
                        onChange={handleInputChange}
                      />
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Nombres del Firmante 2 :</label>
                      <input
                        className="form-control"
                        id=""
                        name="str_Firmante2"
                        value={formDataSolicitud.str_Firmante2}
                        onChange={handleInputChange}
                      />
                      
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                  <div className="div-input-crear-solicitud">
                      <label className="form-label">Documento del Firmante 1 :</label>
                      <input
                        className="form-control"
                        id=""
                        name="str_documentoFirmante1"
                        value={formDataSolicitud.str_documentoFirmante1}
                        onChange={handleInputChange}
                      />
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Documento del Firmante 2 :</label>
                      <input
                        className="form-control"
                        id=""
                        name="str_documentoFirmante2"
                        value={formDataSolicitud.str_documentoFirmante2}
                        onChange={handleInputChange}
                      />
                      
                    </div>
                  </div>
             
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearEJ
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        handleFileChangeEJ={handleFileChangeEJ}
        handleFileRemoveEJ={handleFileRemoveEJ}
        filesEJ={filesEJ}
        
      />
    </div>
  );
};

export default ExtraJudicial;
