import React from "react";
import { useLocation } from "react-router-dom";
import { RoutesPrivate } from "../../../../Security/Routes/ProtectedRoute";
import Header from "../../../Components/Partials/Header/Header";
import Titulo from "../../../Components/Partials/Seccion/Titulo";
import PrestacionServicio from "./TipoSolicitud/PrestacionServicio";
import LocacionServicio from "./TipoSolicitud/LocacionServicio";
import PrestacionServicioPro from "./TipoSolicitud/PrestacionServicioPro";
import AcuerdoConfidencialidad from "./TipoSolicitud/AcuerdoConfidencialidad";
import ArrendamientoBienes from "./TipoSolicitud/ArrendamientoBienes";
import Consorcio from "./TipoSolicitud/Consorcio";
import CompraVenta from "./TipoSolicitud/CompraVenta";

const CrearAdenda = () => {
  const location = useLocation();

  const solicitud = location.state?.solicitud;
  const idUsuario = location.state?.idUsuario;
  const idAplicacion = location.state?.idAplicacion;
  const Suscriptor = location.state?.Suscriptor;
  const Suscripcion = location.state?.Suscripcion;
  const Nombres = location.state?.Nombres;
  const Apellidos = location.state?.Apellidos;
  const gestor = location.state?.gestor;
  const sinCodigo = location.state?.sinCodigo;
console.log(solicitud)
  return (
    <div>
      <Header />
      <Titulo
        seccion={`${
          solicitud.str_CodSolicitudes
            ? "Adenda del Contrato " + solicitud.str_CodSolicitudes
            : "Adenda de " + solicitud.tipoSol_str_Nombre
        }`}
        salir={true}
        paginaSalir={
          gestor ? RoutesPrivate.INICIOGESTOR : RoutesPrivate.INICIOSOLICITANTE
        }
      />
      {solicitud.tipoSol_str_Nombre === "Contrato de prestación de servicios" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Depósito Simple" ||
      solicitud.tipoSol_str_Nombre ===
        "Contrato de Administración Almacén On Site" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Depósito Temporal" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Transporte" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Distribución" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Depósito y Transporte" ||
      solicitud.tipoSol_str_Nombre === "Contrato de Servicio SILE" ||
      solicitud.tipoSol_str_Nombre ===
        "Contrato de Depósito temporal y Transporte" ? (
        <PrestacionServicio
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre ===
          "Contrato de locación de servicios de cliente" ||
        solicitud.tipoSol_str_Nombre ===
          "Contrato de locación de servicios de proveedor" ? (
        <LocacionServicio
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre ===
        "Contrato de prestación de servicios profesionales" ? (
        <PrestacionServicioPro
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre ===
          "Acuerdo de Confidencialidad Cliente" ||
        solicitud.tipoSol_str_Nombre ===
          "Acuerdo de Confidencialidad Proveedor" ? (
        <AcuerdoConfidencialidad
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre ===
        "Contrato de arrendamiento de bienes muebles o inmuebles" ? (
        <ArrendamientoBienes
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre ===
        "Contrato de compra / venta de bienes muebles o inmuebles" ? (
        <CompraVenta
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : solicitud.tipoSol_str_Nombre === "Contrato de consorcio" ? (
        <Consorcio
          idAplicacion={idAplicacion}
          Suscriptor={Suscriptor}
          Suscripcion={Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}
          ver={false}
          sinCodigo={sinCodigo}
          NomTipoSolicitud={solicitud.tipoSol_str_Nombre}
        />
      ) : (
        ""
      )}
    </div>
  );
};

export default CrearAdenda;
