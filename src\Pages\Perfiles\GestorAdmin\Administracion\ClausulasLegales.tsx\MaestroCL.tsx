/* eslint-disable react-hooks/exhaustive-deps */
import  { useEffect, useState } from 'react'
import IconoEliminarMaestros from '../../../../../assets/SVG/IconoEliminarMaestros'
import API_GESTOR from '../../../../../assets/Api/ApisGestor';
import axios from 'axios';
import Cookies from "js-cookie";
import Swal from 'sweetalert2';
import { decrypt, validateToken } from '../../../../Components/Services/TokenService';

interface ClausulaLegal {
  str_Nombre: string;
  int_idClausulasLegales :number;
}

const MaestroCL = () => {
  const [clausulasLegales, setClausulasLegales] = useState<ClausulaLegal[]>([]);
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const [nuevaClausula, setNuevaClausula] = useState('');
  const token = Cookies.get("Token");

  const fetchClausulas = async () => {
await validateToken();
      try {
        const response = await axios.get(API_GESTOR['ObteneClausulasLegales'](Suscripcion),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        });
        setClausulasLegales(response.data);
      } catch (error) {
        
      }
    };
    const EliminarClausula = async (idClausula: number) => {
      try {
      await axios.delete(API_GESTOR['EliminarClausulaLegal'](idClausula),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      })
        await fetchClausulas()
      } catch (error) {
        return error;
      }
    };
    const confirmarEliminacion = (idClausula: number) => {
      Swal.fire({
        title: '¿Estás seguro?',
        text: '¡No podrás revertir esta acción!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar',
      }).then((result) => {
        if (result.isConfirmed) {
          EliminarClausula(idClausula); 
          Swal.fire('', 'La unidad ha sido eliminada.', 'success');
        }
      });
    };
    const agregarClausula = async () => {
await validateToken();
      if (!nuevaClausula.trim()) {
        Swal.fire('', 'Por favor ingresa un nombre para la nueva clausula', 'error');
        return;
      }
      try {
          await axios.post(API_GESTOR['AgregarClausulas'](), {
            str_Nombre: nuevaClausula,
            str_idSuscripcion:Suscripcion,
            int_idUsuarioCreacion :idUsuario

          },{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          });
          await fetchClausulas();
          setNuevaClausula(''); 
          Swal.fire('', 'La clausula ha sido agregada.', 'success');
        } catch (error) {
          Swal.fire('', 'No se pudo agregar la clausula', 'error');
        }
    };
    useEffect(() => {
      fetchClausulas();
    }, []);
    
  return (
    <div className='global-maestros'>
          <div className="titulo-maestros lato-font">
            Maestro de Clausulas Legales
        </div>
        <div className="container-gestor-administracion">
            <table className='tabla-gestor-administracion'>
                <tbody>
                {clausulasLegales.length === 0 ? (
                <tr>
                  <td colSpan={6} style={{ textAlign: "center" }}>
                    No se encontraron registros
                  </td>
                </tr>
              ) : (
                clausulasLegales.map((clausulaLegal, index) => (
                    <tr key={index}>
                    <td><div className="texto-tabla-gestor-administracion">{clausulaLegal.str_Nombre}</div> <div className="icono-tabla-gestor-administracion" style={{cursor : 'pointer'}} onClick={() => confirmarEliminacion(clausulaLegal.int_idClausulasLegales )}><IconoEliminarMaestros/></div></td>
                    </tr>
                ))
            )}
                </tbody>
            </table>
            <div className="div-registros-gestor-administracion">
                <input type="text" className='form-control' placeholder='Ingresar Clausula'
                 value={nuevaClausula}
                 onChange={(e) => setNuevaClausula(e.target.value)}/>
                <button className='btn-nuevo-maestros' onClick={agregarClausula}><i className="fa-solid fa-plus"></i> Agregar</button>
            </div>
        </div>
    </div>
  )
}

export default MaestroCL
