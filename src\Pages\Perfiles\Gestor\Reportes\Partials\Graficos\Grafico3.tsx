import { useState ,useEffect} from "react";
import { Bar } from 'react-chartjs-2';
import ChartDataLabels from "chartjs-plugin-datalabels";

import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Tooltip,
    Legend
} from 'chart.js';
import { Modal } from "@mui/material";

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend,ChartDataLabels);

const Grafico3 = ({ data, isLoading }) => {
    const [openModal, setOpenModal] = useState(false);
    const handleOpenModal = () => setOpenModal(true);
    const handleCloseModal = () => setOpenModal(false);

    // Usar datos optimizados pasados como prop
    const datosGrafico = data || {};
    const chartData = {
      labels: [
          'Enero', 'Febrero', 'Mar<PERSON>', 'Abril', 'Mayo', '<PERSON><PERSON>',
          '<PERSON>', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
      ],
      datasets: [
          {
              label: 'Solicitudes Creadas',
              data: datosGrafico?.Nuevo?.cantidad || Array(12).fill(0),
              backgroundColor: 'rgba(44, 76, 179, 0.8)',
              borderColor: 'rgba(44, 76, 179, 1)',
              borderWidth: 1,
          },
          {
              label: 'Firmadas',
              data: datosGrafico?.Firmado?.cantidad || Array(12).fill(0),
              backgroundColor: 'rgba(192, 192, 192, 0.8)',
              borderColor: 'rgba(192, 192, 192, 1)',
              borderWidth: 1,
          }
      ]
  };



    
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      plugins: {
          legend: {
              position: 'top',
          },
          tooltip: {
              callbacks: {
                  label: (tooltipItem) => {
                      const dataset = tooltipItem.dataset;
                      const dataIndex = tooltipItem.dataIndex;
                      const value = dataset.data[dataIndex];
                      return `${value} solicitudes`;
                  },
                  
              },
          },
          datalabels: {
            display: false,
            color: "#fff",
            font: {
              weight: "bold",
              size: 10,
            },
            formatter: (value, context) => {
              const total = context.chart.data.datasets[0].data.reduce((acc, val) => acc + val, 0);
              const percentage = ((value / total) * 100).toFixed(0);
              return `${percentage}%`; // Mostrar porcentaje
            },
          },
      },
      scales: {
          y: {
              beginAtZero: true,
          },
      },
  };


  
    return (
        <div className="card-grafico-reportes-gestor">
            <div className="header-card-grafico-reportes-gestor lato-font">
                <div className="titulo-card" onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
                    Comparación de Solicitudes: Solicitudes creadas vs Firmadas
                </div>

            </div>

            <div className="grafico-100-gestor">
            <Bar data={chartData} options={options} 
            />
  
            </div>


            <Modal open={openModal} onClose={handleCloseModal}    style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
                <div className="titulo-card" onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
                    Comparación de Solicitudes: Solicitudes creadas vs Firmadas
                </div>

            </div>
            <div className="grafico-pie-gestor-modal">
            <Bar data={chartData} options={options}  style={{maxWidth:'50rem',maxHeight:'20rem'}} />
  
            </div>
        </div>
      </Modal>
        </div>
    );
};

export default Grafico3;