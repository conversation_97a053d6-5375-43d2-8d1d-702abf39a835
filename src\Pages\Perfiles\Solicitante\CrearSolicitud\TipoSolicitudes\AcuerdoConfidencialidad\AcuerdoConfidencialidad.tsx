import CrearAcuerdoConfidencialidad from "./Crear"
import EditarAcuerdoConfidencialidad from "./Editar"


const AcuerdoConfidencialidad = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  esEdicion,
  selectedSolicitud,
  ver,
  NomTipoSolicitud
}) => {
  return (
    <>    
    {!esEdicion ? 
      <CrearAcuerdoConfidencialidad Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} idTipoSolicitud={idTipoSolicitud} tipoModelo={tipoModelo} NomTipoSolicitud={NomTipoSolicitud} />
      :
      <EditarAcuerdoConfidencialidad Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} selectedSolicitud={selectedSolicitud} tipoModelo={tipoModelo} esEdicion={esEdicion} ver={ver} NomTipoSolicitud={NomTipoSolicitud}/>

    }
    </>

  )
}

export default AcuerdoConfidencialidad