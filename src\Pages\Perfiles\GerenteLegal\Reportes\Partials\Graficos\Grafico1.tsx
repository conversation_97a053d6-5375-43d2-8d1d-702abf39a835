import { useEffect, useState } from "react";
import { Pie } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  ArcElement,
  Tooltip,
  Legend,
} from "chart.js";
import { Modal } from "@mui/material";

ChartJS.register(
  CategoryScale,
  LinearScale,
  ArcElement,
  Tooltip,
  Legend,
  ChartDataLabels
);

interface Grafico1Props {
  data: any[];
  isLoading: boolean;
}

const Grafico1: React.FC<Grafico1Props> = ({ data, isLoading }) => {
  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [
      {
        label: "Solicitudes",
        data: [],
        backgroundColor: [],
        borderColor: "rgba(255, 255, 255, 1)",
        borderWidth: 1,
      },
    ],
  });
  const [chartDataModal, setChartDataModal] = useState(chartData);

  useEffect(() => {
    if (data && data.length > 0) {
      const labels = data.map((item) => item.unidad_negocio);
      const chartDataValues = data.map((item) => item.cantidad_solicitudes);

      const maxIndex = chartDataValues.indexOf(Math.max(...chartDataValues));

      const backgroundColorNormal = chartDataValues.map((_, index) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgba(200, 200, 200, ${0.5 + Math.random() * 0.5})`
      );

      const backgroundColorModal = chartDataValues.map((_, index) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgb(${Math.floor(Math.random() * 170)}, ${Math.floor(
              Math.random() * 170
            )}, ${Math.floor(Math.random() * 170)})`
      );
      setChartData({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorNormal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });

      setChartDataModal({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorModal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });
    }
  }, [data]);

  return (
    <div className="card-grafico-reportes-gestor">
      <div className="header-card-grafico-reportes-gestor lato-font">
        <div
          className="titulo-card"
          onClick={handleOpenModal}
          style={{ cursor: "pointer" }}
        >
          Solicitudes pendientes por unidad de negocio
        </div>
      </div>
      <div className="grafico-pie-gestor">
        <Pie
          data={chartData}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            aspectRatio: 0.8,
            plugins: {
              legend: {
                position: "none",
              },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) => {
                    const dataset = tooltipItem.dataset;
                    const dataIndex = tooltipItem.dataIndex;
                    const value = dataset.data[dataIndex];
                    const total = dataset.data.reduce(
                      (acc, val) => acc + val,
                      0
                    );
                    const percentage = ((value / total) * 100).toFixed(0);
                    return `${percentage}%`;
                  },
                },
              },
              datalabels: {
                display: (context) =>
                  context.dataIndex ===
                  chartData.datasets[0].data.indexOf(
                    Math.max(...chartData.datasets[0].data)
                  ),
                color: "#fff",
                font: {
                  weight: "bold",
                  size: 16,
                },
                formatter: (value, context) => {
                  const total = context.chart.data.datasets[0].data.reduce(
                    (acc, val) => acc + val,
                    0
                  );
                  const percentage = ((value / total) * 100).toFixed(0);
                  return `${percentage}% `; // Mostrar porcentaje
                },
              },
            },
          }}
        />

     
      </div>
      <Modal open={openModal} onClose={handleCloseModal} >
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
            <div className="titulo-card">
              Solicitudes pendientes por unidad de negocio
            </div>
          </div>
          <div className="grafico-pie-gestor-modal">
            <Pie
              data={chartDataModal}
              style={{ maxWidth: "20rem", maxHeight: "20rem" }}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: "none",
                  },
                  tooltip: {
                    callbacks: {
                      label: (tooltipItem) => {
                        const dataset = tooltipItem.dataset;
                        const dataIndex = tooltipItem.dataIndex;
                        const value = dataset.data[dataIndex];
                        const total = dataset.data.reduce(
                          (acc, val) => acc + val,
                          0
                        );
                        const percentage = ((value / total) * 100).toFixed(0);
                        return `${percentage}%`;
                      },
                    },
                  },
                  datalabels: {
                    display: true,
                    color: "#fff",
                    font: {
                      weight: "bold",
                      size: 10,
                    },
                    formatter: (value, context) => {
                      const total = context.chart.data.datasets[0].data.reduce(
                        (acc, val) => acc + val,
                        0
                      );
                      const percentage = ((value / total) * 100).toFixed(0);
                      return `${percentage}%`; // Mostrar porcentaje
                    },
                  },
                },
              }}
            />
            <div className="leyenda-graficos-gestor">
              {chartDataModal.labels.map((label, index) => (
                <div
                  key={index}
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div
                    style={{
                      width: "0.75rem",
                      height: "0.75rem",
                      backgroundColor:
                        chartDataModal.datasets[0].backgroundColor[index],
                      borderRadius: "20%",
                      marginRight: "8px",
                    }}
                  />
                  <span className="texto-leyenda-graficos-gestor">
                    {label}: {chartDataModal.datasets[0].data[index]}{" "}
                    solicitudes
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Grafico1;
