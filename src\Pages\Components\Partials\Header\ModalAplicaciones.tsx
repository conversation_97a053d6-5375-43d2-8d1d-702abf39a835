import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../Security/Routes/ProtectedRoute";
import logoCompliance from "../../../../assets/Img/gifCarga.gif";
import { useState } from "react";
import imagenCompliance from "../../../../assets/Img/Compliance.png";
import { decrypt, get_transfer_token } from "../../Services/TokenService";

const ModalAplicaciones = ({
  isModalVisibleAplicaciones,
  CerrarModal,
  aplicacionesAsignadas,
  Perfil,
  perfilesAsignados,
  suscriptor,
  suscripcion,
  correo,
  documento,
  sesion_id,
}) => {
 

    const handleOnClick = async (app) => {
      get_transfer_token()
      window.open(
        `${app.url}/?idAplicacion=${app.int_idAplicacion}&Suscripcion=${suscripcion}&Suscriptor=${suscriptor}&correoUser=${correo}&app=${documento}&sesion=${sesion_id}`,
        '_blank'
      );
    }
  return (
    <>
      {isModalVisibleAplicaciones && aplicacionesAsignadas && (
        <div className="modal-aplicaciones">
         
          <div className="card-aplicaciones">
            <div className="titulo-mis-apps lato-font">
              Tus aplicaciones
            </div>
            {aplicacionesAsignadas.map((app) => (
              <div className="card-aplicacion-usuario" onClick={()=>handleOnClick(app)}>
                <img
                  alt="Pic"
                  src={
                    app.avatar
                      ? `data:image/png;base64,${app.avatar}`
                      : "/Icono-Logo.png"
                  }
                />
                <div className="titulo-app-usuario montserrat-font-500">
                {app.nombre_app} 
                </div>
 

              </div>
            ))}
 
          </div>
   
          
        </div>
      )}
 
    </>
  );
};

export default ModalAplicaciones;
