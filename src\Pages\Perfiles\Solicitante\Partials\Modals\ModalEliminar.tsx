import axios from "axios";
import React from "react";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import Swal from "sweetalert2";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
import getLogs from "../../../../Components/Services/LogsService";

interface Solicitud {
    int_idSolicitudes: number;
    str_CodSolicitudes: string;
  }
  
  interface ModalEliminarProps {
    isModalVisibleEliminar: boolean;
    CerrarModalEliminar: () => void;
    solicitudSeleccionada: Solicitud;
    SubmitObtenerDatos: () => void;
    setSelectedSolicitud: (solicitud: any) => void,
  }
  
  const ModalEliminar: React.FC<ModalEliminarProps> = ({
    isModalVisibleEliminar,
    CerrarModalEliminar,
    solicitudSeleccionada,
    SubmitObtenerDatos,
    setSelectedSolicitud
  }) => {
    const token = Cookies.get("Token");

    const handleEliminar = async () => {
      await validateToken();
      try {
        const response = await axios.delete(
          API_SOLICITANTE["EliminarSolicitud"](
            solicitudSeleccionada.int_idSolicitudes
          ),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
  
        if (response.status >= 200 && response.status < 300) {
          Swal.fire(
            "Eliminada",
            "La solicitud fue eliminada correctamente",
            "success"
          );
          SubmitObtenerDatos();
          CerrarModalEliminar();
          setSelectedSolicitud({})
          await getLogs(null,solicitudSeleccionada.int_idSolicitudes,null,"Listado Solicitudes","Solicitudes","Eliminar Solicitudes","Contratos","DELETE");

        }
      } catch (error) {
        console.error("Error al eliminar la solicitud:", error);
        Swal.fire("", "No se pudo eliminar la solicitud", "error");
      }
    };
  
    return (
      <>
        {isModalVisibleEliminar && (
          <div className="modal-aceptar-solicitud">
            <div className="boton-cerrar-modal-filtros">
              <button
                type="button"
                className="btn-close"
                aria-label="Close"
                onClick={CerrarModalEliminar}
              ></button>
            </div>
            <div className="pregunta-modal-solicitante lato-font">
              <span>
                ¿Seguro que quieres eliminar la solicitud{" "}
                <strong>{solicitudSeleccionada.str_CodSolicitudes}</strong>?
              </span>
            </div>
            <div className="botones-modal-solicitante">
              <button
                className="btn btn-outline-primary"
                onClick={CerrarModalEliminar}
              >
                Cancelar
              </button>
              <button
                className="btn btn-primary"
                onClick={handleEliminar}
              >
                Aceptar
              </button>
            </div>
          </div>
        )}
      </>
    );
  };
  
  export default ModalEliminar;