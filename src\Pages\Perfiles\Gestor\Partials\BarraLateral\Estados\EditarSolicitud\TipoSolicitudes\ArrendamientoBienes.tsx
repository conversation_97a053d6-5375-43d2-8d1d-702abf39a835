import React, { useEffect, useState } from "react";
import { <PERSON>, Step, <PERSON><PERSON><PERSON><PERSON>, <PERSON>per, TextField } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import Cookies from "js-cookie";
import axios from "axios";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import BarraLateralCrearSolicitud from "../../../../../../Solicitante/CrearSolicitud/BarraLateral/BarraLateralCrearSolicitud";
import API_GESTOR from "../../../../../../../../assets/Api/ApisGestor";
import { RoutesPrivate } from "../../../../../../../../Security/Routes/ProtectedRoute";
import { validateToken } from "../../../../../../../Components/Services/TokenService";
import API_SOLICITANTE from "../../../../../../../../assets/Api/ApisSolicitante";
import IconoMoneda from "../../../../../../../../assets/SVG/IconoMoneda";
import IconoDolares from "../../../../../../../../assets/SVG/IconoSoles";
import IconoInmueble from "../../../../../../../../assets/SVG/IconoInmueble";
import IconoMueble from "../../../../../../../../assets/SVG/IconoMueble";
import getLogs from "../../../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");

interface Errors {
  int_idEmpresa?: string;
  int_idUnidadNegocio?: string;
  str_Documento?: string;
  str_Interlocutor?: string;
  db_Honorarios?: string;
  dt_FechaEsperada?: string;
  str_Correo?: string;
  str_RepLegal?: string;
  str_ObjetivoContrato?: string;
  db_Presupuesto?: string;
  str_PlazoSolicitud?: string;
  str_TipoServicio?: string;
  str_CondicionPago?: string;
  str_BienDescripcion?: string;
  str_BienPartidaCertificada?: string;
  str_BienDireccion?: string;
  str_ImporteVenta?: string;
  str_FormaPago?: string;
  str_BienUso?: string;
  str_PlazoArriendo?: string;
  str_RentaPactada?: string;
  str_InteresRetraso?: string;
  str_ReajusteRenta?: string;
  str_Penalidades?: string;
}
interface PrestacionServicio {
  UsuarioId: number | null;
  idAplicacion: number | null;
  Suscriptor: number | null;
  Suscripcion: string | null;
  selectedSolicitud: object | null;
  Asignado: boolean;
}

const ArrendamientoBienes: React.FC<PrestacionServicio> = ({
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  Asignado,
  documentoSubido,
  ver,
}) => {
  const [files, setFiles] = useState([]);

  const [newFiles, setNewFiles] = useState([]);
  const token = Cookies.get("Token");
  const [interlocutorVendedorEncontrado, setInterlocutorVendedorEncontrado] =
    useState(false);
  const [idInterlocutorVendedor, setIdInterlocutorVendedor] = useState(null);
  const [interlocutorCompradorEncontrado, setInterlocutorCompradorEncontrado] =
    useState(false);
  const [idInterlocutorComprador, setIdInterlocutorComprador] = useState(null);
  const [interlocutorEncontrado, setInterlocutorEncontrado] = useState(false);
  const [idInterlocutor, setIdInterlocutor] = useState(null);
  const [
    interlocutorCliAsociadoEncontrado,
    setInterlocutorCliAsociadoEncontrado,
  ] = useState(false);

  const [idInterlocutorCliAsociado, setIdInterlocutorCliAsociado] =
    useState(null);
  const [tipoBien, setTipoBien] = useState("Mueble");

  const [errors, setErrors] = useState({});
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");
  const [activeStep, setActiveStep] = useState(0);
  const [clausulasIncluidas, setClausulasIncluidas] = useState([]);
  const [clausulasSeleccionadas, setClausulasSeleccionadas] = useState([]);
  const [TiposMoneda, setTiposMoneda] = useState({
    str_Moneda: "",
    str_Pais: "",
  });

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };
  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioModificacion: UsuarioId,
    int_idEmpresa: selectedSolicitud.int_idEmpresa,
    int_idUnidadNegocio: selectedSolicitud.int_idUnidadNegocio,
    int_idTipoSol: selectedSolicitud.int_idTipoSol,
    int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
    str_DeTerceros: selectedSolicitud.str_DeTerceros,
    dt_FechaEsperada: selectedSolicitud.dt_FechaEsperada,
    db_Honorarios: selectedSolicitud.db_Honorarios,
    str_idSuscriptor: Suscripcion,
    int_idClienteAsociado: selectedSolicitud.int_idClienteAsociado,
  });
  const [formDataContenido, setFormDataContenido] = useState({
    str_DocAdjuntos: files ? "si" : newFiles.length >= 1 ? "si" : "no",
    str_ObjetivoContrato: "",
    db_Presupuesto: 0,
    str_PlazoSolicitud: "",
    str_Moneda: selectedTipoMoneda,
    str_TipoServicio: "",
    str_InfoAdicional: "",
    int_idInterlocutor: null,
    int_idInterlocutorComprador: null,
    str_CondicionPago: "",
    str_RenovacionAuto: "no",
    str_AjusteHonorarios: "no",
    str_FormaPago: "",
    str_DetalleRenovAuto: "",
    str_Garantia: "no",
    str_DetalleGarantia: "",
    str_BienDescripcion: "",
    str_BienUso: "",
    str_Penalidades: "no",
    str_DetallePenalidades: "",
    str_BienMuebleInmueble: tipoBien,
    str_BienPartidaCertificada: "",
    str_BienDireccion: "",
    str_RentaPactada: "",
    str_PlazoArriendo: "",
    str_InteresRetraso: "no",
    str_detalleInteresRetraso: "",
    str_ReajusteRenta: "no",
    str_DetalleReajusteRenta: "",
    dt_FechaArriendo: "",
    dt_FechaRenAut: null,
  });
  const [formDataInterlocutor, setFormDataInterlocutor] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: selectedSolicitud.int_idSolicitante,
    str_Correo: "",
    str_RepLegal: "",
    str_RLDocumento: "",
    str_RLTipoDocumento: "Documento de identidad personal",
    str_Domicilio: "",
    int_RLPartida: "",
  });
  const [formDataInterlocutorComprador, setFormDataInterlocutorComprador] =
    useState({
      str_idSuscripcion: Suscripcion,
      str_Interlocutor: "",
      str_TipoDoc: "Documento de identidad personal",
      str_Documento: "",
      int_idUsuarioCreacion: selectedSolicitud.int_idSolicitante,
      str_Correo: "",
      str_RepLegal: "",
      str_RLDocumento: "",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Domicilio: "",
      int_RLPartida: "",
    });

  const [formDataClienteAsociado, setFormDataClienteAsociado] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_RazonSocial: "",
  });
  const validateForm = (): boolean => {
    let newErrors: Errors = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataInterlocutor.str_Documento)
      newErrors.str_Documento = "El Documento es requerido";
    if (!formDataInterlocutor.str_Interlocutor)
      newErrors.str_Interlocutor = "El proveedor es requerido";

    if (!formDataInterlocutor.str_RepLegal)
      newErrors.str_RepLegal = "El Representante legal es requerido";
    if (!formDataContenido.str_BienDescripcion)
      newErrors.str_BienDescripcion = "La descripción del bien es requerida";
    if (!formDataContenido.str_BienPartidaCertificada)
      newErrors.str_BienPartidaCertificada =
        "La Partida registral es requerida";
    if (!formDataContenido.str_BienDireccion)
      newErrors.str_BienDireccion = "La Dirección del bien es requerida";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.str_BienUso)
      newErrors.str_BienUso = "El bien es requerido es requerida";
    if (!formDataContenido.str_RentaPactada)
      newErrors.str_RentaPactada = "La renta pactada es requerida";
    if (!formDataContenido.str_PlazoArriendo)
      newErrors.str_PlazoArriendo = "El plazo de arriendo es requerido";

    setErrors(newErrors);

    // Retorna true si no hay errores
    return Object.keys(newErrors).length === 0;
  };
  const steps = [
    "DATOS GENERALES",
    "DATOS DE CONTRATO",
    "DATOS LEGALES",
    "INFORMACIÓN ADICIONAL",
  ];
  const fetchArchivos = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ListarArchivosEditar"](
          Suscripcion,
          selectedSolicitud.int_idSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setFiles(response.data);
    } catch (error) {
      console.error("Error al obtener las empresas:", error);
    }
  };
  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            str_InfoAdicional: response.data.str_InfoAdicional,
            int_idInterlocutor: response.data.int_idInterlocutor,
            int_idInterlocutorComprador:
              response.data.int_idInterlocutorComprador,
            str_Moneda: response.data.str_Moneda || "dolares",
            str_FormaPago: response.data.str_FormaPago,
            str_Garantia: response.data.str_Garantia  || "no",
            str_DetalleGarantia: response.data.str_DetalleGarantia,
            str_BienDireccion: response.data.str_BienDireccion,
            str_ImporteVenta: response.data.str_ImporteVenta,
            str_BienMuebleInmueble: response.data.str_BienMuebleInmueble,
            str_BienDescripcion: response.data.str_BienDescripcion,
            str_BienPartidaCertificada:
              response.data.str_BienPartidaCertificada,
            dt_FechaArriendo: response.data.dt_FechaArriendo,
            str_BienUso: response.data.str_BienUso,
            str_PlazoArriendo: response.data.str_PlazoArriendo,
            str_RentaPactada: response.data.str_RentaPactada,
            str_InteresRetraso: response.data.str_InteresRetraso || "no",
            str_Penalidades: response.data.str_Penalidades  || "no",
            str_DetallePenalidades: response.data.str_DetallePenalidades,
            str_DetalleReajusteRenta: response.data.str_DetalleReajusteRenta,
            str_DetalleRenovAuto: response.data.str_DetalleRenovAuto,
            str_RenovacionAuto: response.data.str_RenovacionAuto || "no"  ,
            dt_FechaRenAut: response.data.dt_FechaRenAut,
            str_ReajusteRenta: response.data.str_ReajusteRenta || "no",
          }));

          setTipoBien(response.data.str_BienMuebleInmueble);
          setSelectedTipoMoneda(response.data.str_Moneda || "dolares");
          const responseInterlocutor = await axios.get(
            API_GESTOR["BuscarInterlocutorID"](
              response.data.int_idInterlocutor
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseInterlocutor.data) {
            setFormDataInterlocutor((prevData) => ({
              ...prevData,
              str_Documento: responseInterlocutor.data.str_Documento,
            }));
            buscarInterlocutor(responseInterlocutor.data.str_Documento, 1);
          } else {
          }
          const responseInterlocutorComprador = await axios.get(
            API_GESTOR["BuscarInterlocutorID"](
              response.data.int_idInterlocutorComprador
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseInterlocutorComprador.data) {
            setFormDataInterlocutorComprador((prevData) => ({
              ...prevData,
              str_Documento: responseInterlocutorComprador.data.str_Documento,
            }));
            buscarInterlocutor(
              responseInterlocutorComprador.data.str_Documento,
              3
            );
          } else {
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            db_Presupuesto: 0,
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_InfoAdicional: "",
          }));
        }
      } catch (error) {}
    };
    const ObtenerInterlocutorAsociado = async () => {
      await validateToken();
      try {
        const responseInterlocutor = await axios.get(
          API_GESTOR["BuscarInterlocutorID"](
            selectedSolicitud.int_idClienteAsociado
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (responseInterlocutor.data) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_Documento: responseInterlocutor.data.str_Documento,
            str_Interlocutor: responseInterlocutor.data.str_Interlocutor,
          }));
          buscarInterlocutor(responseInterlocutor.data.str_Documento, 2);
        } else {
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const fetchEmpresas = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerEmpresas"](idAplicacion, Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    const fetchClausulasIncluidas = async () => {
      await validateToken();
      try {
        const responseIncluidas = await axios.get(
          API_GESTOR["ObtenerClausulasIncluidas"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedSolicitud.int_idEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasIncluidas(responseIncluidas.data);

        const responseActivas = await axios.get(
          API_GESTOR["ObtenerClausulasActivas"](
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasSeleccionadas(responseActivas.data.clausulas_activas);
      } catch (error) {
        console.error("Error al obtener las cláusulas:", error);
      }
    };
    fetchClausulasIncluidas();
    fetchEmpresas();
    DatosContenidoSolicitud();
    ObtenerInterlocutorAsociado();
    fetchArchivos();
  }, []);
  useEffect(() => {
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](
            selectedSolicitud.int_idEmpresa,
            Suscripcion
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    fetchMoneda();
  }, [selectedSolicitud.int_idEmpresa]);
  const renderClausulas = () => {
    if (!clausulasIncluidas || clausulasIncluidas.length === 0) {
      return (
        <div className="no-clausulas-mensaje">
          No hay cláusulas asignadas para esta solicitud
        </div>
      );
    }
  
    const groupedClausulas = [];
  
    for (let i = 0; i < clausulasIncluidas.length; i += 2) {
      groupedClausulas.push(clausulasIncluidas.slice(i, i + 2));
    }
  
    return groupedClausulas.map((group, index) => (
      <div className="inputs-crear-solicitud" key={index}>
        {group.map((clausula, idx) => (
          <div className="div-input-crear-solicitud" key={idx}>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="checkbox"
                value={clausula.int_idClausulasIncluidas}
                checked={clausulasSeleccionadas.includes(
                  clausula.int_idClausulasIncluidas
                )}
                onChange={(e) =>
                  handleCheckboxChange(
                    clausula.int_idClausulasIncluidas,
                    e.target.checked
                  )
                }
                 disabled={ver}
              />
              <label className="form-check-label">
                {clausula.clausula_legal_nombre}
              </label>
            </div>
          </div>
        ))}
      </div>
    ));
  };
  const handleCheckboxChange = async (clausulaId, isChecked) => {
    let updatedClausulas;
    if (isChecked) {
      updatedClausulas = [...clausulasSeleccionadas, clausulaId];
    } else {
      updatedClausulas = clausulasSeleccionadas.filter(
        (id) => id !== clausulaId
      );
    }
    setClausulasSeleccionadas(updatedClausulas);

    try {
      if (isChecked) {
        await axios.post(
          API_GESTOR["ActivarClausulas"](),
          {
            str_idSuscripcion: Suscripcion,
            int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
            int_idClausulaIncluida: clausulaId,
            int_idUsuarioCreacion: UsuarioId,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
      } else {
        // Llamada a la API para eliminar la cláusula deseleccionada
        await axios.delete(
          API_GESTOR["EliminarClausula"](
            clausulaId,
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
      }
    } catch (error) {
      console.error("Error al actualizar la cláusula:", error);
    }
  };

  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);

    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  const handleChangeTipoBien = (event) => {
    const newTipo = event.target.value;
    setTipoBien(newTipo);
    setFormDataContenido((prevData) => ({
      ...prevData,
      str_BienMuebleInmueble: newTipo,
    }));
  };
  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setNewFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };
  const EliminarArchivo = async (file) => {
    try {
      const response = await axios.delete(
        API_GESTOR["EliminarArchivo"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          file.nombre_archivo,
          file.int_idArchivos,
          file.tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        fetchArchivos();
      }
    } catch (error) {
      console.error("Error al eliminar la solicitud:", error);
    }
  };
  const handleFileRemove = (index, isStoredFile, file) => {
    if (isStoredFile) {
      EliminarArchivo(file);
    } else {
      setNewFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };

  const handleDateChangeCompra = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value;
    setFormDataContenido((prevData) => ({
      ...prevData,
      dt_FechaArriendo: `${selectedDate}T00:00:00`,
    }));
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataInterlocutor((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    if (name.startsWith("Comprador_")) {
      const fieldName = name.replace("Comprador_", "");
      setFormDataInterlocutorComprador((prevData) => ({
        ...prevData,
        [fieldName]: value,
      }));
    }
    if (name.startsWith("CliAsociado_")) {
      const fieldName = name.replace("CliAsociado_", "");
      setFormDataClienteAsociado((prevData) => ({
        ...prevData,
        [fieldName]: value,
      }));

      if (fieldName === "str_Documento") {
        buscarInterlocutor(value, 2);
      }
    }
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string, value: number) => {
    try {
      const response = await axios.get(
        API_GESTOR["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.data) {
        if (value === 1) {
          setFormDataInterlocutor((prevData) => ({
            ...prevData,
            str_Interlocutor: response.data.str_Interlocutor,
            str_Correo: response.data.str_Correo,
            str_RepLegal: response.data.str_RepLegal,
            int_RLPartida: response.data.int_RLPartida,
            str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
            str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
            str_Domicilio: response.data.str_Domicilio,
            str_RLDocumento: response.data.str_RLDocumento,
          }));
          setInterlocutorVendedorEncontrado(true);
          setIdInterlocutorVendedor(response.data.int_idInterlocutor);
        } else if (value === 2) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_RazonSocial: response.data.str_RazonSocial,
          }));
          setIdInterlocutorCliAsociado(response.data.int_idInterlocutor);
          setInterlocutorCliAsociadoEncontrado(true);
        } else if (value === 3) {
          setFormDataInterlocutorComprador((prevData) => ({
            ...prevData,
            str_Interlocutor: response.data.str_Interlocutor,
            str_Correo: response.data.str_Correo,
            str_RepLegal: response.data.str_RepLegal,
            int_RLPartida: response.data.int_RLPartida,
            str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
            str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
            str_Domicilio: response.data.str_Domicilio,
            str_RLDocumento: response.data.str_RLDocumento,
          }));
          setInterlocutorCompradorEncontrado(true);
          setIdInterlocutorComprador(response.data.int_idInterlocutor);
        }
      } else {
        if (value === 1) {
          setFormDataInterlocutor((prevData) => ({
            ...prevData,
            str_Interlocutor: "",
            str_Correo: "",
            str_RepLegal: "",
            int_RLPartida: "",
            str_RLTipoDocumento: "Documento de identidad personal",
            str_Domicilio: "",
            str_RLDocumento: "",
          }));
          setInterlocutorVendedorEncontrado(false);
          setIdInterlocutorVendedor(null);
        } else if (value === 2) {
          setInterlocutorCliAsociadoEncontrado(false);
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_RazonSocial: "",
          }));
          setIdInterlocutorCliAsociado(null);
        } else if (value === 3) {
          setFormDataInterlocutorComprador((prevData) => ({
            ...prevData,
            str_Interlocutor: "",
            str_Correo: "",
            str_RepLegal: "",
            int_RLPartida: "",
            str_RLTipoDocumento: "Documento de identidad personal",
            str_Domicilio: "",
            str_RLDocumento: "",
          }));
          setInterlocutorCompradorEncontrado(false);
          setIdInterlocutorComprador(null);
        }
      }
    } catch (error) {
      if (value === 1) {
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_Correo: "",
          str_RepLegal: "",
          int_RLPartida: "",
          str_RLTipoDocumento: "Documento de identidad personal",
          str_Domicilio: "",
          str_RLDocumento: "",
        }));
        setInterlocutorVendedorEncontrado(false);
        setIdInterlocutorVendedor(null);
      } else if (value === 2) {
        setInterlocutorCliAsociadoEncontrado(false);
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
          str_RazonSocial: "",
        }));
        setIdInterlocutorCliAsociado(null);
      } else if (value === 3) {
        setFormDataInterlocutorComprador((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_Correo: "",
          str_RepLegal: "",
          int_RLPartida: "",
          str_RLTipoDocumento: "Documento de identidad personal",
          str_Domicilio: "",
          str_RLDocumento: "",
        }));
        setInterlocutorCompradorEncontrado(false);
        setIdInterlocutorComprador(null);
      }
    }
  };

  const handleSubmitInterlocutores = async () => {
    await validateToken();
    try {
      if (interlocutorCliAsociadoEncontrado) {
        const formDataModificadaComprador = {
          ...formDataClienteAsociado,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificadaComprador.int_idUsuarioCreacion;

        const response = await axios.put(
          API_GESTOR["ActualizarInterlocutor"](idInterlocutorCliAsociado),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }

        return idInterlocutorCliAsociado;
      } else {
        const response = await axios.post(
          API_GESTOR["AgregarInterlocutor"](),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }

        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);

        return response.data.int_idInterlocutor;
      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };
  // --------- AGREGAR O ACTUALIZAR INTERLOCUTORES  -----------
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  };

  const updateInterlocutor = async (id, data) => {
    return axios.put(API_GESTOR["ActualizarInterlocutor"](id), data, {
      headers,
    });
  };

  const addInterlocutor = async (data) => {
    return axios.post(API_GESTOR["AgregarInterlocutor"](), data, { headers });
  };

  const handleSubmitInterlocutoresVendedorComprador = async () => {
    await validateToken();

    try {
      const formDataModificadaVendedor = {
        ...formDataInterlocutor,
        int_idUsuarioModificacion: UsuarioId,
      };
      const formDataModificadaComprador = {
        ...formDataInterlocutorComprador,
        int_idUsuarioModificacion: UsuarioId,
      };

      delete formDataModificadaVendedor.int_idUsuarioCreacion;
      delete formDataModificadaComprador.int_idUsuarioCreacion;

      if (interlocutorVendedorEncontrado && interlocutorCompradorEncontrado) {
        await updateInterlocutor(
          idInterlocutorVendedor,
          formDataModificadaVendedor
        );
        await updateInterlocutor(
          idInterlocutorComprador,
          formDataModificadaComprador
        );

        handleSubmitContenidoSolicitud(
          idInterlocutorVendedor,
          idInterlocutorComprador
        );

        return idInterlocutorCliAsociado;
      } else if (
        !interlocutorVendedorEncontrado &&
        interlocutorCompradorEncontrado
      ) {
        const response = await addInterlocutor(formDataInterlocutor);
        await updateInterlocutor(
          idInterlocutorComprador,
          formDataModificadaComprador
        );

        handleSubmitContenidoSolicitud(
          response.data.int_idInterlocutor,
          idInterlocutorComprador
        );
        return response.data.int_idInterlocutor;
      } else if (
        interlocutorVendedorEncontrado &&
        !interlocutorCompradorEncontrado
      ) {
        await updateInterlocutor(
          idInterlocutorVendedor,
          formDataModificadaVendedor
        );
        const response = await addInterlocutor(formDataInterlocutorComprador);

        setIdInterlocutorComprador(response.data.int_idInterlocutor);
        handleSubmitContenidoSolicitud(
          idInterlocutorVendedor,
          response.data.int_idInterlocutor
        );

        return idInterlocutorCliAsociado;
      } else {
        const response1 = await addInterlocutor(formDataInterlocutor);
        const response2 = await addInterlocutor(formDataInterlocutorComprador);

        setIdInterlocutorVendedor(response1.data.int_idInterlocutor);
        setIdInterlocutorComprador(response2.data.int_idInterlocutor);

        handleSubmitContenidoSolicitud(
          response1.data.int_idInterlocutor,
          response1.data.int_idInterlocutor
        );

        return response2.data.int_idInterlocutor;
      }
    } catch (error) {
      console.error(
        "Error al gestionar el interlocutor:",
        error.response?.data || error.message
      );
    }
  };

  //--------------------------------------------------------------------------------
  const handleSubmitGuardarSolicitud = async (
    idInterlocutorCliAsociado: number
  ) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarSolicitud"](selectedSolicitud.int_idSolicitudes),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitConfirmarSolicitud = async (idInterlocutorCliAsociado) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarSolicitud"](selectedSolicitud.int_idSolicitudes),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitContenidoSolicitud = async (
    idInterlocutor,
    idInterlocutorComprador
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idInterlocutor: idInterlocutor,
        int_idInterlocutorComprador: idInterlocutorComprador,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarContenidoSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitFiles = async () => {
    await validateToken();
    try {
      for (const file of newFiles) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          selectedSolicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          selectedSolicitud.int_idSolicitudes
        );
             if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }
      }
    } catch (error) {}
  };
  const CambiarEstado = async () => {
    await validateToken();
    try {
      const responseCambiarEstado = await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: !Asignado ? "En Validación" : "En Proceso",
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitSolicitudGuardar = async () => {
    await validateToken();
    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        await handleSubmitGuardarSolicitud(null).catch((err) => {
          success = false;
          errorMessages.push("Error al guardar la solicitud: " + err.message);
        });
      } else {
        await handleSubmitGuardarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al guardar la solicitud: " + err.message);
        });
      }
      await handleSubmitInterlocutoresVendedorComprador();

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Guardada Correctamente..",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
        await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };

  const handleSubmitSolicitudConfirmar = async () => {
    await validateToken();
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop();
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }

    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        await handleSubmitConfirmarSolicitud(null).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });
      } else {
        await handleSubmitConfirmarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });
      }
      await handleSubmitInterlocutoresVendedorComprador();

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (!Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Confirmada.",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
         await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };
  const handleDownload = async (nombreArchivo: string ,tipo_adjunto:string) => {
    try {
      const response2 = await axios.get(
        API_GESTOR["DescargarArchivoNombre"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          nombreArchivo,
          tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          responseType: "blob",
        }
      );
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        : `${nombreArchivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={selectedSolicitud.nombre_completo}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Fecha de Registro: </label>
                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                          selectedSolicitud.dt_FechaRegistro.split("T")[0] || ""
                        }
                        name="dt_FechaRegistro"
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_NombreEmpresa"
                        value={selectedSolicitud.str_NombreEmpresa}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha Esperada de entrega:{" "}
                      </label>
                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                          formDataSolicitud.dt_FechaEsperada
                            ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                            : ""
                        }
                        name="dt_FechaEsperada"
                        onChange={handleInputChange}

                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Descripcion_UnidadNegocio"
                        value={selectedSolicitud.str_Descripcion_UnidadNegocio}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Condiciones del Servicio</label>
                      <textarea
                        className="form-control"
                        name="str_InfoAdicional"
                        id="exampleFormControlTextarea1"
                        rows={3}
                        value={formDataContenido.str_InfoAdicional}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                    </div>
                  </div>
                  <hr className="separador-linea" />

                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Arrendador
                  </div>
                  <div className="inputs-crear-solicitud">
                  <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutor.str_TipoDoc || ""} disabled={ver}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento Representante:</label>
                      <select name="str_RLTipoDocumento" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutor.str_RLTipoDocumento || ""}  disabled={ver}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                   
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        name="str_Documento"
                        value={formDataInterlocutor.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value, 1);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="str_RLDocumento"
                        className="form-control"
                        value={formDataInterlocutor.str_RLDocumento}
                        placeholder=""
                        onChange={(e) => {
                          handleInputChange(e);
                          const maxLength =
                            15;
                          if (e.target.value.length <= maxLength) {
                            handleInputChange(e);
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_RLDocumento && (
                        <span className="error-message">
                          {errors.str_RLDocumento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Arrendador:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Interlocutor"
                        value={formDataInterlocutor.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante Legal:</label>
                      <input
                        type="text"
                        name="str_RepLegal"
                        className="form-control"
                        value={formDataInterlocutor.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_RepLegal && (
                        <span className="error-message">
                          {errors.str_RepLegal}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Domicilio"
                        value={formDataInterlocutor.str_Domicilio}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Domicilio && (
                        <span className="error-message">
                          {errors.str_Domicilio}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Partida Registral : </label>
                      <input
                        type="text"
                        name="int_RLPartida"
                        className="form-control"
                        value={formDataInterlocutor.int_RLPartida}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.int_RLPartida && (
                        <span className="error-message">
                          {errors.int_RLPartida}
                        </span>
                      )}
                    </div>
                  </div>
                  <hr className="separador-linea" />

                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Arrendatario
                  </div>
                  <div className="inputs-crear-solicitud">
                  <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="Comprador_str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutorComprador.str_TipoDoc || ""} disabled={ver}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento Representante:</label>
                      <select name="Comprador_str_RLTipoDocumento" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutorComprador.str_RLTipoDocumento || ""} disabled={ver}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                   
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        name="Comprador_str_Documento"
                        value={formDataInterlocutorComprador.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value, 3);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="Comprador_str_RLDocumento"
                        className="form-control"
                        value={formDataInterlocutorComprador.str_RLDocumento}
                        placeholder=""
                        onChange={(e) => {
                          handleInputChange(e);
                          const maxLength =
                            15;
                          if (e.target.value.length <= maxLength) {
                            handleInputChange(e);
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_RLDocumento && (
                        <span className="error-message">
                          {errors.str_RLDocumento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Arrendatario:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="Comprador_str_Interlocutor"
                        value={formDataInterlocutorComprador.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante Legal:</label>
                      <input
                        type="text"
                        name="Comprador_str_RepLegal"
                        className="form-control"
                        value={formDataInterlocutorComprador.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_RepLegal && (
                        <span className="error-message">
                          {errors.str_RepLegal}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="Comprador_str_Domicilio"
                        value={formDataInterlocutorComprador.str_Domicilio}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Domicilio && (
                        <span className="error-message">
                          {errors.str_Domicilio}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Partida Registral : </label>
                      <input
                        type="text"
                        name="Comprador_int_RLPartida"
                        className="form-control"
                        value={formDataInterlocutorComprador.int_RLPartida}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.int_RLPartida && (
                        <span className="error-message">
                          {errors.int_RLPartida}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Datos del contrato
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Bien
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Bien:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_BienMuebleInmueble ===
                              "Mueble" && "check-selected"
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_BienMuebleInmueble"
                            id="mueble"
                            value="Mueble"
                            checked={
                              formDataContenido.str_BienMuebleInmueble ===
                              "Mueble"
                            }
                            onChange={handleChangeTipoBien}
                          />
                          <label className="form-check-label" htmlFor="mueble">
                            <IconoMueble
                              size={"1.3rem"}
                              color={
                                formDataContenido.str_BienMuebleInmueble ===
                                "Mueble"
                                  ? "#156CFF"
                                  : "#C5DBFF"
                              }
                            />{" "}
                            Mueble
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_BienMuebleInmueble ===
                              "Inmueble" && "check-selected"
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_BienMuebleInmueble"
                            id="inmueble"
                            value="Inmueble"
                            checked={
                              formDataContenido.str_BienMuebleInmueble ===
                              "Inmueble"
                            }
                            onChange={handleChangeTipoBien}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="inmueble"
                          >
                            <IconoInmueble
                              size={"1.3rem"}
                              color={
                                formDataContenido.str_BienMuebleInmueble ===
                                "Inmueble"
                                  ? "#156CFF"
                                  : "#C5DBFF"
                              }
                            />{" "}
                            Inmueble
                          </label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Descripción del Bien:
                      </label>
                      <textarea
                        className="form-control"
                        id=""
                        name="str_BienDescripcion"
                        rows={3}
                        value={formDataContenido.str_BienDescripcion}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                    </div>
                  </div>

                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Partida Registral / Certificado:
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_BienPartidaCertificada"
                        value={formDataContenido.str_BienPartidaCertificada}
                        onChange={handleInputChange}
                        readOnly={ver}
                        required
                      />
                      {errors.str_BienPartidaCertificada && (
                        <span className="error-message">
                          {errors.str_BienPartidaCertificada}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Uso del Bien:</label>
                      <textarea
                        className="form-control"
                        id=""
                        name="str_BienUso"
                        rows={3}
                        value={formDataContenido.str_BienUso}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                      {errors.str_BienUso && (
                        <span className="error-message">
                          {errors.str_BienUso}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio del Bien:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_BienDireccion"
                        value={formDataContenido.str_BienDireccion}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_BienDireccion && (
                        <span className="error-message">
                          {errors.str_BienDireccion}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Objeto del contrato:</label>
                      <textarea
                        className="form-control"
                        id=""
                        name="str_ObjetivoContrato"
                        rows={3}
                        value={formDataContenido.str_ObjetivoContrato}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                      {errors.str_ObjetivoContrato && (
                        <span className="error-message">
                          {errors.str_ObjetivoContrato}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Arriendo
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Renta Pactada</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_RentaPactada"
                        value={formDataContenido.str_RentaPactada}
                        onChange={handleInputChange}
                        readOnly={ver}
                        required
                      />
                      {errors.str_RentaPactada && (
                        <span className="error-message">
                          {errors.str_RentaPactada}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Plazo de arriendo</label>
                      <textarea
                        className="form-control"
                        id=""
                        name="str_PlazoArriendo"
                        rows={3}
                        value={formDataContenido.str_PlazoArriendo}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                      {errors.str_PlazoArriendo && (
                        <span className="error-message">
                          {errors.str_PlazoArriendo}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Moneda:</label>
                      <div className="radio-inputs-crear-solicitud">
                        {TiposMoneda.str_Moneda !== "Dolares" && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "dolares"
                                ? "check-selected"
                                : ""
                            }`}
                          >
                            <input
                              className="form-check-input"
                              type="radio"
                              name="inlineRadioOptions"
                              id="monedaDolares"
                              value="dolares"
                              checked={selectedTipoMoneda === "dolares"}
                              onChange={handleChangeTipoMoneda}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="monedaDolares"
                            >
                              <IconoDolares size={"1.5rem"} color={"#156CFF"} />{" "}
                              Dólares
                            </label>
                          </div>
                        )}

                        {Object.keys(TiposMoneda).length > 0 && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "empresa"
                                ? "check-selected"
                                : ""
                            }`}
                          >
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoMoneda"
                              id="tipoMoneda"
                              value={"empresa"}
                              checked={selectedTipoMoneda === "empresa"}
                              onChange={handleChangeTipoMoneda}
                              disabled={ver}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="tipoMoneda"
                            >
                              <IconoMoneda size={"1.5rem"} color={"#156CFF"} />{" "}
                              {TiposMoneda.str_Moneda}
                            </label>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha de Inicio de Arriendo:{" "}
                      </label>

                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                          formDataContenido.dt_FechaArriendo
                            ? formDataContenido.dt_FechaArriendo.split("T")[0]
                            : ""
                        }
                        name="dt_FechaArriendo"
                        onChange={handleDateChangeCompra}
                        disabled={ver}
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Forma de Pago</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_FormaPago"
                        value={formDataContenido.str_FormaPago}
                        onChange={handleInputChange}
                        required
                        readOnly={ver}
                      />
                      {errors.str_FormaPago && (
                        <span className="error-message">
                          {errors.str_FormaPago}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Renovación Automática:
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_RenovacionAuto === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_RenovacionAuto"
                            id="SiRenovacionAuto"
                            value="si"
                            checked={
                              formDataContenido.str_RenovacionAuto === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiRenovacionAuto">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_RenovacionAuto === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_RenovacionAuto"
                            id="NoRenovacionAuto"
                            value="no"
                            checked={
                              formDataContenido.str_RenovacionAuto === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoRenovacionAuto">No</label>
                        </div>
                      </div>
                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                          formDataContenido.dt_FechaRenAut
                            ? formDataContenido.dt_FechaRenAut.split("T")[0]
                            : ""
                        }
                        onChange={handleInputChange}
                        name="dt_FechaRenAut"
                        disabled={
                          formDataContenido.str_RenovacionAuto === "no" || ver
                        }
                      />
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleRenovAuto"
                        rows={2}
                        value={formDataContenido.str_DetalleRenovAuto}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_RenovacionAuto === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(2)}
                >
                  Datos Legales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Garantía:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Garantia === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Garantia"
                            id="SiGarantia"
                            value="si"
                            checked={formDataContenido.str_Garantia === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="SiGarantia"
                          >
                            Sí
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Garantia === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Garantia"
                            id="NoGarantia"
                            value="no"
                            checked={formDataContenido.str_Garantia === "no"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="NoGarantia"
                          >
                            No
                          </label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Interes por retraso de devolución del bien:
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                      <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_InteresRetraso === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_InteresRetraso"
                            id="SiInteresRetraso"
                            value="si"
                            checked={
                              formDataContenido.str_InteresRetraso === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiInteresRetraso">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_InteresRetraso === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >                             <input
                            className="form-check-input"
                            type="radio"
                            name="str_InteresRetraso"
                            id="NoInteresRetraso"
                            value="no"
                            checked={
                              formDataContenido.str_InteresRetraso === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoInteresRetraso">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleGarantia"
                        rows={3}
                        value={formDataContenido.str_DetalleGarantia}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_Garantia === "no" || ver
                        }
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_detalleInteresRetraso"
                        rows={3}
                        value={formDataContenido.str_detalleInteresRetraso}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_InteresRetraso === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>

                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Reajuste de Renta:</label>
                      <div className="radio-inputs-crear-solicitud">
                      <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ReajusteRenta === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >                             <input
                            className="form-check-input"
                            type="radio"
                            name="str_ReajusteRenta"
                            id="SiReajusteRenta"
                            value="si"
                            checked={
                              formDataContenido.str_ReajusteRenta === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiReajusteRenta">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ReajusteRenta === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >                            <input
                            className="form-check-input"
                            type="radio"
                            name="str_ReajusteRenta"
                            id="NoReajusteRenta"
                            value="no"
                            checked={
                              formDataContenido.str_ReajusteRenta === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoReajusteRenta">No</label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Penalidades:</label>
                      <div className="radio-inputs-crear-solicitud">
                      <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >                            <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="SiPenalidades"
                            value="si"
                            checked={formDataContenido.str_Penalidades === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiPenalidades">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >  
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="NoPenalidades"
                            value="no"
                            checked={formDataContenido.str_Penalidades === "no"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoPenalidades">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleReajusteRenta"
                        rows={3}
                        value={formDataContenido.str_DetalleReajusteRenta}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_ReajusteRenta === "no" || ver
                        }
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetallePenalidades"
                        rows={3}
                        value={formDataContenido.str_DetallePenalidades}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_Penalidades === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Inclusión de Clausulas
                  </div>
                  {renderClausulas()}
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingFour">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseFour"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseFour"
                  onClick={() => handleStepChange(3)}
                >
                  Condiciones del Servicio
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseFour"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingFour"
              >
                <div className="accordion-body">
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Asociación a cuenta del cliente
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Interlocutor"
                        value={formDataClienteAsociado.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Documento"
                        value={formDataClienteAsociado.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value, 2);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Razón Social:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_RazonSocial"
                        value={formDataClienteAsociado.str_RazonSocial}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        newFiles={newFiles}
        esEdicion={true}
        ver={ver}
        gestor={true}
        documentoSubido={documentoSubido}
        handleDownload={handleDownload}
      />
    </div>
  );
};

export default ArrendamientoBienes;
