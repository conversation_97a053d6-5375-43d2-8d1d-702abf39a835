import React, { useEffect, useState } from 'react';
import axios from 'axios';
import Cookies from "js-cookie";
import API_SOLICITANTE from '../../../../assets/Api/ApisSolicitante';
import { validateToken } from '../../../Components/Services/TokenService';



interface GestorAsignadoProps {
  solicitud: {
    nombre_gestor: number;
    nombre_completo: number | null; 
  };
}

const GestorAsignadoSolicitante: React.FC<GestorAsignadoProps> = ({ solicitud }) => {



  return (
    <>
      {solicitud ? (
        <>
          <span className="subtitulo-barra-Lateral lato-font-400">Gestor</span>
          <span className="nombre-solicitante-barra-lateral">
            {solicitud?.nombre_gestor}
          </span>
          <span className="subtitulo-barra-Lateral lato-font-400">Solicitante</span>
          <span className="nombre-solicitante-barra-lateral">
            {solicitud?.nombre_completo}
          </span>
        </>
      ) :           <span className="subtitulo-barra-Lateral lato-font-400">Gestor</span>
}
    </>
  );
};

export default GestorAsignadoSolicitante;