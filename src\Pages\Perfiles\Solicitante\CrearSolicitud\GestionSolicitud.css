.div-contenido-crear-solicitud {
   border-collapse: separate;
  border-spacing: 0;
   border-radius: 0.625rem;
  overflow: hidden;
   max-width: 85%;
  min-width: 85%;
  display: flex;
  padding: 1.9rem 3.125rem;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  margin-bottom: 1.9rem;
}
.container-acordion-crear-solicitud {
  width: 100%;
  margin-top: 1.3rem;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
}
.Header-accordion-crear-solicitud {
  font-size: 1.3rem;
}
.inputs-crear-solicitud {
  display: flex;
  justify-content: start;
  align-items: start;
  gap: 80px;
}
.inputs-valoracion-asociados-solicitud{
  display: flex;
  flex: 1 1;
}
.titulo-solicitud-interlocutores{
  margin-top: 2% !important;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  color: #156CFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.div-inputs-fechas{
  display: flex;
  flex-direction: column;
  width: 40%;
}
.div-input-fecha-solicitud {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
  width: 100%;
  margin-top: 0.9375rem;
}
.div-input-fecha-solicitud .css-1d3z3hw-MuiOutlinedInput-notchedOutline{
  border-color: #2c4cb380;
  border-radius: 0.3125rem;
  padding: 0.3125rem;
}
.div-input-fecha-solicitud label {
  font-size: 1rem;
}
.div-input-crear-solicitud {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
  width: 40%;
  margin-top: 0.9375rem;
}
.div-input-crear-solicitud .css-1d3z3hw-MuiOutlinedInput-notchedOutline{
  border-color: #2c4cb380;
  border-radius: 0.3125rem;
  padding: 0.3125rem;
}
.div-input-crear-solicitud label {
  font-size: 1rem;
  color: #2C4CB3;
}
.accordion-button:not(.collapsed) {
  background-color: white;
  box-shadow: none;
}
/* .div-input-crear-solicitud input {
  border-color: #2c4cb380;
} */
.barraLateral-crear-solicitud {
  width: 15%;
  border: 0.0625rem solid #d9d9d9;
  background-color: #fff;
  padding: 2rem 0.5rem ;
  border-radius: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 75vh; 
  position: sticky;
  top: 7rem; 
  overflow-y: auto;
  background-color: white;
  box-shadow: 2px 0 0.3125rem rgba(0, 0, 0, 0.1); 
}
.titulo-barra-Lateral-CS{
    font-size: 1rem;
    width: 90%;
    margin: auto;
    margin-top: 1rem;
    color: #4B4B4B;
}
.superior-barraLateral-crear-solicitud{
    display: flex;
    flex-direction: column;
    gap: 0.3125rem;
}
.div-subirArchivos-barraLateral{
    margin-top: 0.9375rem;
    justify-content: center;
    display: flex;
    align-items: center;
    flex-direction: column;
}
.div-subirArchivos-barraLateral input{
    font-size: 0.625rem;
}
.botones-crear-solicitud{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 0.625rem;
}
.boton-crear-asociado{
  display: flex;
  justify-content: right;
  align-items: center;
  width: 100%;
  gap: 0.625rem;
}
.btn-outline-primary{
    width: 90%;
    margin: auto;
    padding: 0.125rem;
    border-radius: 0.9375rem;
    border-color: #294FCF;
    color: #294FCF;
    font-size: 1rem;
}
.btn-outline-primary:hover{
    background-color: white;
    color: #294FCF;
}
.btn-primary{
    width: 90%;
    margin: auto;
    padding: 0.3125rem;
    background-color: #294FCF;
    border-radius: 0.9375rem;
    font-size: 0.8rem;
}
.btn-subirArchivos{
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.75rem;
  justify-content: left;
  align-items: center;
  width: 90%;
  border: 0.0625rem solid #C5DBFF;
  border-radius: 0.3125rem;
  overflow: none;
  display: flex;
  justify-content: center;
  background-color: #FAF9FF;
  color: #345DE1;
  padding: 1rem 0;
}
.btn-seleccionar-archivo{
  display: flex;
  font-size: 0.75rem;
  padding: 0.3rem  1rem;
  border-radius: 0.35rem;
  border: 0.0625rem solid #345DE1;
  background-color: #345DE1;
  color: white;
  margin-top: 0.5rem;
}
.btn-seleccionar-archivo:hover{
  background-color: #3451b3;
  color: white;
}
.btn-subirArchivos:hover {
  display: flex;
   font-size: 0.75rem;;
  justify-content: left;
  align-items: center;
  width: 90%;
  border: 0.0625rem solid #D9D9D9;
  border-radius: 0.3125rem;
  overflow: none;
  display: flex;
  justify-content: center;
  background-color: #FAF9FF;
  color: #345DE1;


}
.div-subirArchivos-barraLateral .btn:active{
  border: 0.0625rem solid #D9D9D9;

}
.btn-subirArchivos:active{
  display: flex;
   font-size: 0.75rem;
  justify-content: left;
  align-items: center;
  width: 90%;
  border: 0.0625rem solid #D9D9D9;
  border-radius: 0.3125rem;
  overflow: none;
  color: #345DE1;

}
.btn-subirArchivos i {

  font-size: 0.75rem;
}

.div-subirArchivos-barraLateral ul{
    width: 90%;
    padding: 0;
    display: flex;
    justify-content: start;
    align-items: baseline;
    flex-direction: column;
}
.div-subirArchivos-barraLateral li{
    margin-top: 0.5rem;
    list-style: none;
  
    font-size: 0.75rem;
    color: #294FCF;
    padding: 0.3rem;
    max-width: 13rem;
    min-width: 13rem;
    width: 100%;
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem;
    border-radius: 0.3125rem;
    border: 0.0625rem solid #E9E8E8;
    background-color: #fff;
}
.listado-archivos{
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: 0.625rem;
}
.div-subirArchivos-barraLateral li .iconolistar{
    font-size: 0.625rem;
    margin-right: 0.3125rem;
}
.div-subirArchivos-barraLateral ul .iconoborrar{
    font-size: 0.625rem;
    margin-left: 0.3125rem;
    cursor: pointer;
    color: #294FCF;
}
.text-area-crear-solicitud{
    max-width: 60%;
}
.text-area-crear-solicitud textarea{
    border-color: #2c4cb380;
}
.nombres-stepper span{
    color: #294FCF !important;

}
.radio-inputs-crear-solicitud{
    display: flex;
    justify-content: left;
    gap: 0.625rem;
}
.error-message{
  color: red;
  font-size: 13px;
}
.eliminar-asociado{
  cursor: pointer;
}