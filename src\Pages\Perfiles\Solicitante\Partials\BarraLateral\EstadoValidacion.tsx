import React, { useEffect, useState } from "react";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import axios from "axios";
import Swal from "sweetalert2";
import HistorialSolicitud from "./HistorialSolicitud";
import GestorAsignado from "./GestorAsignado";
import Tooltip from "@mui/material/Tooltip";
import ModalAceptar from "../Modals/ModalAceptar";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
import IconoDownload from "../../../../../assets/SVG/IconoDownload";
import IconoFirmar from "../../../../../assets/SVG/IconoFirmar";
import getLogs from "../../../../Components/Services/LogsService";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import IconoVer from "../../../../../assets/SVG/IconoVer";

interface EstadoValidacionProps {
  solicitudSeleccionada: {
    int_idSolicitudes: number;
    int_idGestor:number
  };
  historiales: any[]; 
  idUsuario: number;
  SubmitObtenerDatos: () => void;
  Suscripcion: string;
  setSelectedSolicitud: (solicitud: any) => void;
}

const EstadoValidacion: React.FC<EstadoValidacionProps> = ({
  solicitudSeleccionada,
  historiales,
  idUsuario,
  SubmitObtenerDatos,
  Suscripcion,
  setSelectedSolicitud,
  aprobadores,
  idAplicacion,
  Suscriptor,
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAceptar: false
  });
  const navigate = useNavigate();
  const token = Cookies.get("Token");

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const CambiarEstado = async () => {
    await validateToken();
    try {
      await axios.put(API_SOLICITANTE["ActualizarEstado"](), {
        str_idSuscriptor: Suscripcion,
        nombre_estado: "Aceptado",
        int_idUsuarioCreacion: idUsuario,
        int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      Swal.fire("", "Contrato Aceptado", "success");
      SubmitObtenerDatos();
      setSelectedSolicitud({});
    } catch (error) {
       Swal.fire("", "No se pudo cambiar el estado de la solicitud", "error");
    }
  };
  const Aceptar = async () => {
    await validateToken();
    try {
      // await axios.post(API_SOLICITANTE["FirmarAceptacion"](), {
      //   str_idSuscriptor: Suscripcion,
      //   str_CodSolicitudes: solicitudSeleccionada.str_CodSolicitudes,
      //   int_idUsuarioCreacion: idUsuario,
      //   str_CodTipoDocumento: "COAP",
      // },{
      //   headers: {
      //     "Content-Type": "application/json",
      //     "Authorization": `Bearer ${token}`
      //   }
      // });
      await (CambiarEstado());
      await getLogs(null,null,null,"Listado Solicitudes","Solicitudes","Validar Solicitud","Contratos","POST");

    } catch (error) {
      Swal.fire("", "Hubo un error al firmar el contrato , por favor verifique su firma", "error");
      return false
    }
  };

  const Rechazar = async () => {
    await validateToken();
    try {
      await axios.put(API_SOLICITANTE["ActualizarEstado"](), {
        str_idSuscriptor: Suscripcion,
        nombre_estado: "En Proceso",
        int_idUsuarioCreacion: idUsuario,
        int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      Swal.fire("", "Contrato Rechazado", "success");
      SubmitObtenerDatos();
      setSelectedSolicitud({});
      await getLogs(null,null,null,"Listado Solicitudes","Solicitudes","Rechazar Solicitud","Contratos","PUT");

    } catch (error) {
      console.error("Error al cambiar el estado:", error);
      Swal.fire("", "No se pudo cambiar el estado de la solicitud", "error");
    }
  };
  const handleDownload = async () => {
    await validateToken();
    try {

      const response = await axios.get(API_SOLICITANTE["ListarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"COAP"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if(response.data){

      const response2 = await axios.get(API_SOLICITANTE["DescargarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"COAP"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      , responseType: "blob" });
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        :  `${response.data.nombre_archivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
    const handleVerSolicitud = () => {
      navigate(RoutesPrivate.EDITARSOLICITUD, {
        state: {
          solicitudSeleccionada,
          Suscripcion,
          idAplicacion,
          idUsuario,
          Suscriptor,
          ver: true,
        },
      });
    };
  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Descargar" placement="top">
        <div className="icono-barralateral-acciones" onClick={handleDownload}>
            <IconoDownload onClick={handleDownload} size={"1.3rem"} color={"#000"}/>
           </div>
        </Tooltip>
        <Tooltip title="Aprobar Contrato" placement="top"> 
          <div className="icono-barralateral-acciones" onClick={() => toggleModal('isModalVisibleAceptar')}>
          <IconoFirmar size={"1.3rem"} color={"#000"}/>

          </div>
         </Tooltip>
            {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") ? (
          ""
        ) : (
          <Tooltip title="Ver Solicitud" placement="top"> 
          <div className="icono-barralateral-acciones" onClick={handleVerSolicitud}>
          <IconoVer size={"1.3rem"} color={"#000"}/>

          </div>
         </Tooltip>
        )}
      </div>

      <GestorAsignado solicitudSeleccionada={solicitudSeleccionada} />
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>
      <ModalAceptar 
        isModalVisibleAceptar={modales.isModalVisibleAceptar}
        CerrarModalEliminar={() => toggleModal('isModalVisibleAceptar')}
        solicitudSeleccionada={solicitudSeleccionada}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
        Rechazar ={Rechazar}
        accion={Aceptar}
      />
    </>
  );
};

export default EstadoValidacion;