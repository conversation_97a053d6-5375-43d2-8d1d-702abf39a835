import React from "react";

function formaterFecha(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}
const ListaAprobadores = ({aprobadores }) => {
  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">
        Lista de Aprobadores
      </span>
      <ul className="lista-aprobadores montserrat-font">
        {aprobadores.map((aprobador) => (
          <li key={aprobador.int_idUsuario}>
            <span className="nombres-aprobadores-inicio-aprobadores">{aprobador.str_Nombres} {aprobador.str_Apellidos}</span>
            
            <br />
            <span>Fecha de Aprobación: </span>
            <br />
            <span>
              {" "}
              {aprobador.int_EstadoAprobacion === 1
                ? formaterFecha(aprobador.dt_FechaAceptacion)
                : "Sin Aprobar"}
            </span>
          </li>
        ))}
      </ul>
      
    </>
  );
};

export default ListaAprobadores;
