import React from "react";

const IconoUpload = ({ size ,color}) => {
  return (
    <svg
      enable-background="new 0 0 50 50"
       id="Layer_1"
      version="1.1"
      viewBox="0 0 50 50"
      width={size}
      xml:space="preserve"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <rect fill="none" height="50" width="50" />
      <path
        d="  M32,35c0,0,8.312,0,9.098,0C45.463,35,49,31.463,49,27.099s-3.537-7.902-7.902-7.902c-0.02,0-0.038,0.003-0.058,0.003  c0.061-0.494,0.103-0.994,0.103-1.504c0-6.71-5.439-12.15-12.15-12.15c-5.229,0-9.672,3.309-11.386,7.941  c-1.087-1.089-2.591-1.764-4.251-1.764c-3.319,0-6.009,2.69-6.009,6.008c0,0.085,0.01,0.167,0.013,0.251  C3.695,18.995,1,22.344,1,26.331C1,31.119,4.881,35,9.67,35c0.827,0,8.33,0,8.33,0"
        stroke={color}
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-miterlimit="10"
        stroke-width="2"
        fill="none"
      />
      <polyline
         points="20,28 25,23 30,28   "
        stroke={color}
        stroke-linecap="round"
        stroke-miterlimit="10"
        stroke-width="2"
        fill="#156CFF"
       />
      <line
        stroke={color}
        stroke-linecap="round"
        stroke-miterlimit="10"
        stroke-width="2"
        x1="25"
        x2="25"
        y1="43"
        y2="23.333"
        fill="#156CFF"
       />
    </svg>
  );
};

export default IconoUpload;
