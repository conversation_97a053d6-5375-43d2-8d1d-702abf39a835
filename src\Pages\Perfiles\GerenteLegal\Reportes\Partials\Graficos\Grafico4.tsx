import { useEffect, useState } from "react";
import { Bar } from 'react-chartjs-2';
import ChartDataLabels from "chartjs-plugin-datalabels";

import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Tooltip,
    Legend
} from 'chart.js';
import { Modal } from "@mui/material";

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend,ChartDataLabels);

interface Grafico4Props {
  data: any[];
  isLoading: boolean;
}

const Grafico4: React.FC<Grafico4Props> = ({ data, isLoading }) => {
    const [openModal, setOpenModal] = useState(false);
    const handleOpenModal = () => setOpenModal(true);
    const handleCloseModal = () => setOpenModal(false);
    
    const [chartData, setChartData] = useState({
        labels: [],
        datasets: [
            {
                label: 'Horas Trabajadas', 
                data: [],
                backgroundColor: 'rgba(44, 76, 179, 0.5)',
                borderColor: 'rgba(44, 76, 179, 1)', 
                borderWidth: 1,
            },
        ],
    });
    const [chartDataModal, setChartDataModal] = useState(chartData);

    useEffect(() => {
        if (data && data.length > 0) {
            const labels = data.map((item) => item.unidad_negocio);
            const chartDataValues = data.map((item) => item.total_horas_trabajadas);
            const maxIndex = chartDataValues.indexOf(Math.max(...chartDataValues));

      const backgroundColorNormal = chartDataValues.map((_, index) =>
        index === maxIndex ? "#2C4CB3" : `rgba(200, 200, 200, ${0.5 + Math.random() * 0.5})`
      );

      const backgroundColorModal = chartDataValues.map((_, index) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgb(${Math.floor(Math.random() * 170)}, ${Math.floor(
              Math.random() * 170
            )}, ${Math.floor(Math.random() * 170)})`
      );
      setChartData({
        labels,
        datasets: [
          {
            label: "Horas Trabajadas",
            data: chartDataValues,
            backgroundColor: backgroundColorNormal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });

      setChartDataModal({
        labels,
        datasets: [
          {
            label: "Horas Trabajadas",
            data: chartDataValues,
            backgroundColor: backgroundColorModal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });
        }
    }, [data]);

    return (
        <div className="card-grafico-reportes-gestor">
            <div className="header-card-grafico-reportes-gestor lato-font">
                <div className="titulo-card"  onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
                    Top 10 Horas trabajadas por unidad de negocio
                </div>

            </div>
          

            <div className="grafico-graficos-gestor"> 
                <Bar
                    data={chartData}
                    options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        aspectRatio: 1.2,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: (tooltipItem) => {
                                        const dataset = tooltipItem.dataset;
                                        const dataIndex = tooltipItem.dataIndex;
                                        const value = dataset.data[dataIndex];
                                        const total = dataset.data.reduce((acc, val) => acc + val, 0);
                                        const percentage = ((value / total) * 100).toFixed(0);
                                        return `${percentage}%`;
                                    },
                                },
                            },
                            
                            datalabels: {
                                display: (context: any) => {
                                    const data = context.chart.data.datasets[0].data as number[];
                                    const maxValue = Math.max(...data);
                                    const currentValue = data[context.dataIndex];
                                    return currentValue === maxValue;
                                },
                                color: "#fff",
                                font: {
                                  weight: "bold",
                                  size: 12,
                                },
                                formatter: (value: any, context: any) => {
                                  const data = context.chart.data.datasets[0].data as number[];
                                  const total = data.reduce((acc: number, val: number) => acc + val, 0);
                                  const percentage = ((value / total) * 100).toFixed(0);
                                  return `${percentage}%`;
                                },
                              },
                        },
                        scales: {
                            x: {
                              beginAtZero: true,
                              grid: {
                                display: false
                              }
                            },
                            y: {
                              beginAtZero: true,
                              grid: {
                                display: false
                              }
                            },
                          },
                    }} 
                /> 

            </div>
            
            <Modal open={openModal} onClose={handleCloseModal}>
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
                <div className="titulo-card"  onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
                    Top 10 Horas trabajadas por unidad de negocio
                </div>

            </div>
            <div className="grafico-graficos-gestor"> 
                <Bar 
                    data={chartDataModal} 
                    style={{maxWidth:'25rem',maxHeight:'50rem'}}

                    options={{ 
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: (tooltipItem: any) => {
                                        const dataset = tooltipItem.dataset;
                                        const dataIndex = tooltipItem.dataIndex;
                                        const value = dataset.data[dataIndex] as number;
                                        const data = dataset.data as number[];
                                        const total = data.reduce((acc: number, val: number) => acc + val, 0);
                                        const percentage = ((value / total) * 100).toFixed(0);
                                        return `${percentage}%`;
                                    },
                                },
                            },
                            datalabels: {
                                display: false,
                                color: "#fff",
                                font: {
                                  weight: "bold",
                                  size: 12,
                                },
                                formatter: (value: any, context: any) => {
                                  const data = context.chart.data.datasets[0].data as number[];
                                  const total = data.reduce((acc: number, val: number) => acc + val, 0);
                                  const percentage = ((value / total) * 100).toFixed(0);
                                  return `${percentage}%`;
                                },
                              },
                        },
                        scales: {
                            x: {
                                beginAtZero: true, 
                                grid: {
                                    display: false
                                  }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: false
                                  } 
                            },
                        },
                    }} 
                /> 
                  <div className="leyenda-graficos-gestor">
              {chartDataModal.labels.map((label, index) => (
                <div
                  key={index}
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div
                    style={{
                      width: "0.75rem",
                      height: "0.75rem",
                      backgroundColor:
                      chartDataModal.datasets[0].backgroundColor[index],
                      borderRadius: "20%",
                      marginRight: "8px",
                    }}
                  />
                  <span className="texto-leyenda-graficos-gestor">
                    {label}: {chartDataModal.datasets[0].data[index] ? chartDataModal.datasets[0].data[index] : "0"} horas
                  </span>
                </div>
              ))}
              
            </div>
            </div>
        </div>
      </Modal>
        </div>
    );
};

export default Grafico4;