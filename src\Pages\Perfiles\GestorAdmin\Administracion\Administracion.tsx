import { useState } from 'react';
import Header from '../../../Components/Partials/Header/Header';
import Titulo from '../../../Components/Partials/Seccion/Titulo';
import './Administracion.css';
import IconoAdministracion from '../../../../assets/SVG/IconoAdministracion';
import MaestroUN from './UnidadesNegocio.tsx/MaestroUN';
import MaestroCL from './ClausulasLegales.tsx/MaestroCL';
import ClausulasIncluidas from './ClausulasIncluidas/ClausulasIncluidas';
import Divisas from './TipoCambio/Divisas';
import IconoCambioDivisas from '../../../../assets/SVG/IconoCambioDivisas';

const Administracion = () => {
  const [activeCard, setActiveCard] = useState('unidades');

  const handleCardClick = (card : string) => {
    setActiveCard(card);
  };

  return (
    <div>
      <Header />
      <Titulo seccion={"Administración"} />
      <div className="global-administrar-gestor">
        <div className="div-botones-administrar">
          <div 
            className={`card-boton-administrar lato-font ${activeCard === 'unidades' ? 'active-boton-administrar' : ''}`} 
            onClick={() => handleCardClick('unidades')}
          >
            <IconoAdministracion active={activeCard === 'unidades'} />
            <div> Maestro de unidades de Negocio </div>
          </div>

          <div 
            className={`card-boton-administrar lato-font ${activeCard === 'clausulas' ? 'active-boton-administrar' : ''}`} 
            onClick={() => handleCardClick('clausulas')}
          >
            <IconoAdministracion active={activeCard === 'clausulas'} />
            <div> Maestro de Clausulas Legales </div>
          </div>
          <div 
            className={`card-boton-administrar lato-font ${activeCard === 'clausulasIncluidas' ? 'active-boton-administrar' : ''}`} 
            onClick={() => handleCardClick('clausulasIncluidas')}
          >
            <IconoAdministracion active={activeCard === 'clausulasIncluidas'} />
            <div> Gestión de tipo de solicitud </div>
          </div>
          <div 
            className={`card-boton-administrar lato-font ${activeCard === 'divisas' ? 'active-boton-administrar' : ''}`} 
            onClick={() => handleCardClick('divisas')}
          >
            <IconoCambioDivisas size={"1rem"} active={activeCard === 'divisas'} />
            <div> Divisas </div>
          </div>
        </div>
        <hr />
        {activeCard === 'unidades'? <MaestroUN/>:activeCard === 'clausulas'?  <MaestroCL/> : activeCard === 'clausulasIncluidas'? <ClausulasIncluidas/> : <Divisas/>}
      </div>
    </div>
  );
};

export default Administracion;