import { useNavigate } from "react-router-dom";

function formaterHora(dateString: string) {
  const date = new Date(dateString);
  const hour = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const formattedHour = `${hour < 10 ? '0' : ''}${hour}:${minutes < 10 ? '0' : ''}${minutes}`;

  return `${formattedHour}`;
}

function getHoraActual() {
  const now = new Date();
  const hour = now.getHours();
  const minutes = now.getMinutes();

  const formattedHoraActual = `${hour < 10 ? '0' : ''}${hour}:${minutes < 10 ? '0' : ''}${minutes}`;

  return formattedHoraActual;
}

function formaterFecha(dateString: string) {
  const date = new Date(dateString);

  const peruTimeOffset = 0;
  const utcOffset = date.getTimezoneOffset();
  const adjustedTime = date.getTime() + (utcOffset + peruTimeOffset) * 60000;

  const adjustedDate = new Date(adjustedTime); // Fecha ajustada

  const day = adjustedDate.getDate();
  const month = adjustedDate.toLocaleString("es-PE", { month: "long" });
  const year = adjustedDate.getFullYear();
  return `${day} de ${month} de ${year}`;
}

const ModalEventosHoy = ({
  isModalVisibleEventos,
  eventos,
  CerrarModal,
  tareas,
  suscripcion,
  suscriptor,
  correo,
  documento,
  sesion_id,
  aplicacionProcesos
}) => {
  const navigate = useNavigate();

  eventos.sort((a, b) => {
    const fechaEventoA = new Date(a.dt_fechaEvento);
    const fechaEventoB = new Date(b.dt_fechaEvento);

    return fechaEventoA - fechaEventoB;
  });
  const onClick = async (idProceso) => {
    window.open(
      `${aplicacionProcesos.url}/Solicitudes-Gestor?idAplicacion=${aplicacionProcesos.idAplicacion}&Suscripcion=${suscripcion}&Suscriptor=${suscriptor}&correoUser=${correo}&app=${documento}&sesion=${sesion_id}&proceso=${idProceso}`
    )
  }
  return (
    <>
      {isModalVisibleEventos && eventos && (
        <div className="modal-eventos-semana">
          <div className="boton-cerrar-modal-eventos">
            <div className="leyenda-eventos-hoy">
              <div className="leyenda-evento-terminado">
                Terminado
              </div>
              <div className="leyenda-evento-proximo">
                Próximo
              </div>
            </div>
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarModal}
            ></button>
          </div>
          <div className="card-aplicaciones">
            <div className="titulo-mis-apps lato-font">
              Eventos de la semana
            </div>
            {eventos.map((evento) => {
              const fechaEvento = new Date(evento.dt_fechaEvento);
              const horaEvento = formaterHora(evento.dt_fechaEvento);
              const fechaEventoLocal = fechaEvento.toLocaleDateString("en-CA");

              const ahora = new Date();
              const fechaActual = ahora.toLocaleDateString("en-CA");
              const horaActualComparacion = getHoraActual();

              let claseEstado = "proximo"; 
              if (fechaEventoLocal < fechaActual || (fechaEventoLocal === fechaActual && horaEvento <= horaActualComparacion)) {
                claseEstado = "terminado";
              }

              return (
                <div
                  className={`card-eventos-usuario evento-${claseEstado}`}
                  key={evento.int_idProceso}
                  
                >
                  <div className="titulo-app-usuario montserrat-font-500" style={{ cursor: 'pointer' }} onClick={() => onClick(evento.int_idProceso)}>
                    {evento.str_nombreEvento}
                  </div>
                  <span>Proceso: {evento.str_idProceso}</span>
                  <span>Fecha: {formaterFecha(evento.dt_fechaEvento)} </span>
                  <span>Hora: {horaEvento} horas</span>
                </div>
              );
            })}
          </div>
          <div className="card-aplicaciones">
            <div className="titulo-mis-apps lato-font">
              Tareas de la semana
            </div>
            {tareas.map((tarea) => {
              const fechaTarea = new Date(tarea.dt_FechaFinTarea);
              const fechaEventoLocal = fechaTarea.toLocaleDateString("en-CA");

              const ahora = new Date();
              const fechaActual = ahora.toLocaleDateString("en-CA");

              let claseEstado = "proximo"; 
              if (fechaEventoLocal < fechaActual ) {
                claseEstado = "terminado";
              }
              return (
                <div
                  className={`card-eventos-usuario evento-${claseEstado}`}
                  key={tarea.int_idProceso}
                  
                >
                  <div className="titulo-app-usuario montserrat-font-500" style={{ cursor: 'pointer' }} onClick={() => onClick(tarea.int_idProceso)}>
                    {tarea.str_nombreTarea}
                  </div>
                  <span>Proceso: {tarea.str_idProceso}</span>
                  <span>Fecha Inicio: {formaterFecha(tarea.dt_fechaInicioTarea)} </span>
                  <span>Fecha Fin: {formaterFecha(tarea.dt_FechaFinTarea)} </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </>
  );
};

export default ModalEventosHoy;