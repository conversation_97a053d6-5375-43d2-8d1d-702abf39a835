import React from "react";

const IconoCheck = ({size,color}) => {
  return (
    <svg
      height={size}
      version="1.1"
      viewBox="0 0 18 15"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <title />
      <desc />
      <defs />
      <g
        fill="none"
        fill-rule="evenodd"
        id="Page-1"
        stroke="none"
        stroke-width="1"
    
      >
        <g
          fill="#000000"
          id="Core"
          transform="translate(-423.000000, -47.000000)"
        >
          <g id="check" transform="translate(423.000000, 47.500000)">
            <path
              d="M6,10.2 L1.8,6 L0.4,7.4 L6,13 L18,1 L16.6,-0.4 L6,10.2 Z"
              id="Shape"
              fill={color}
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default IconoCheck;
