
/* Optimización de espacios para reportes */
.div-seleccion-reportes-gestor{
    width: 95%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin:  auto;
    padding-left: 2rem;
}

/* Contenedor para menú y filtros */
.menu-y-filtros-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* Contenedor de tabs del menú */
.menu-tabs-container {
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 1.2rem;
}

/* Contenedor de filtros a nivel del menú */
.filtros-nivel-menu {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
}

.filtros-nivel-menu .contenedor-select-graficos {
    min-width: 11rem;
}
.select-graficos{
    min-width: 17rem;
}
.filtros-nivel-menu .select-graficos {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
    border: 0.0625rem solid #ddd;
    border-radius: 0.25rem;
}

/* Estilos para React Select en filtros del menú */
.filtros-nivel-menu .select-tiposolicitud-graficos,
.filtros-nivel-menu .select-empresa-graficos {
    min-width: 200px;
    font-size: 0.9rem;
}

.filtros-nivel-menu .select-tiposolicitud-graficos .css-13cymwt-control,
.filtros-nivel-menu .select-empresa-graficos .css-13cymwt-control {
    min-height: 38px;
    border: 0.0625rem solid #ddd;
    border-radius: 0.25rem;
    box-shadow: none;
}

.filtros-nivel-menu .select-tiposolicitud-graficos .css-13cymwt-control:hover,
.filtros-nivel-menu .select-empresa-graficos .css-13cymwt-control:hover {
    border-color: #aaa;
}

.filtros-nivel-menu .select-tiposolicitud-graficos .css-1hb7zxy-IndicatorsContainer,
.filtros-nivel-menu .select-empresa-graficos .css-1hb7zxy-IndicatorsContainer {
    padding: 0.25rem;
}

.filtros-nivel-menu .select-tiposolicitud-graficos .css-1wa3eu0-placeholder,
.filtros-nivel-menu .select-empresa-graficos .css-1wa3eu0-placeholder {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Estilo para el select nativo de año */
.filtros-nivel-menu .form-select {
    min-width: 7.5rem;
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
    border: 0.0625rem solid #ddd;
    border-radius: 0.25rem;
    background-color: white;
}

/* Estilos adicionales para React Select - usando selectores más específicos */
.filtros-nivel-menu .select-tiposolicitud-graficos > div,
.filtros-nivel-menu .select-empresa-graficos > div {
    border: 0.0625rem solid #ddd !important;
    border-radius: 0.25rem !important;
    min-height: 2.375rem !important;
    font-size: 0.9rem !important;
}

.filtros-nivel-menu .select-tiposolicitud-graficos > div:hover,
.filtros-nivel-menu .select-empresa-graficos > div:hover {
    border-color: #aaa !important;
}

.filtros-nivel-menu .select-tiposolicitud-graficos > div > div:first-child,
.filtros-nivel-menu .select-empresa-graficos > div > div:first-child {
    padding: 0.4rem 0.8rem !important;
}

/* Estilos para el texto del placeholder y valores seleccionados */
.filtros-nivel-menu .select-tiposolicitud-graficos .css-1dimb5e-singleValue,
.filtros-nivel-menu .select-empresa-graficos .css-1dimb5e-singleValue,
.filtros-nivel-menu .select-tiposolicitud-graficos .css-1wa3eu0-placeholder,
.filtros-nivel-menu .select-empresa-graficos .css-1wa3eu0-placeholder {
    font-size: 0.9rem !important;
    color: #495057 !important;
}

.div-graficos-Reportes-gestor{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 0.5rem;
}

.contenedor-select-graficos{
    display: flex;
    justify-content: right;
    align-items: center;
    gap: 1rem;
 }
 
.titulo-barra-Lateral-graficos{
    font-size: 0.9rem;
    color: white;
    cursor: pointer;
    padding: 0.3rem 1.5rem;
    border-radius: 0.3rem;
    background-color: #294FCF;
    white-space: nowrap;
}

.unselect-matriz{
    font-size: 0.9rem;
    color: #294FCF;
    cursor: pointer;
    padding: 0.3rem 1.5rem;
    border-radius: 0.3rem;
    background-color: #FFF;
    border: 0.0625rem solid #294FCF;
    white-space: nowrap;
}

.contenedorGrafico-reportes-gestor{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
 }

.contenedorGrafico-reportes-gestor .row{
    margin: 0;
    --bs-gutter-x: 1rem;
}

.contenedorGrafico-reportes-gestor .row .col-3,
.contenedorGrafico-reportes-gestor .row .col-4,
.contenedorGrafico-reportes-gestor .row .col-5{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Estilos específicos para los gráficos del medio */
.contenedorGrafico-reportes-gestor .col-5 .flex-fill {
    min-height: 320px;
    max-height: 380px;
}

.contenedorGrafico-reportes-gestor .col-5 .flex-fill .card-grafico-reportes-gestor {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contenedorGrafico-reportes-gestor .col-5 .flex-fill .grafico-graficos-gestor {
    flex: 1;
    min-height: 240px;
    height: 280px;
}

.subtitulo-matriz-reportes{
    width: 100%;
    text-align: left;
    color:#294FCF;
    font-size: 1rem;
    text-indent: 0.9375rem;
    cursor: pointer;
}
.subtitulo-matriz-reportes i{
    font-size: 1rem;
}

.cards-contadores-reportes-gestor{
    display: flex;
    gap: 1rem;
    width: 100%;
    margin-bottom: 0.8rem;
}
.card-contadores-reportes-gestor{
    width: 20%;
    max-width: 20%;
    box-shadow: 0px 0.25rem 6px 0px #0B2FA640;
    border-radius: 0.8rem;
    padding: 0.7rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
    min-height: 120px;
}
.total-card-reportes{
    font-size: 3rem;
    color: #294FCF;
    line-height: 1;
}
.dias-card-reportes{
    font-size: 1.1rem;
    line-height: 1.2;
}
.titulo-card-reportes{
    font-size: 0.9rem;
    color: #4B4B4B;
    text-align: center;
    line-height: 1.2;
}

/* Optimización de gráficos */
.card-grafico-reportes-gestor{
    border: none;
    padding: 0;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0;
    background-color: transparent;
    height: 100%;
}

.header-card-grafico-reportes-gestor{
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 0.95rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #2c4cb3;
}
.header-card-grafico-reportes-gestor .select-card{
    width: 20%;
}

.grafico-graficos-gestor{
    width: 100%;
    height: calc(100% - 3.125rem);
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.grafico-pie-gestor{
    width: 100%;
    height: calc(100% - 3.125rem);
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
}

.grafico-100-gestor{
    width: 100%;
    height: 11rem;
    display: flex;
        max-height: 11rem;

    justify-content: space-evenly;
    align-items: center;
}

/* Ajustes específicos para gráficos en columna del medio */
.col-5 .grafico-graficos-gestor{
    height: 17rem;
}

.col-5 .card-grafico-reportes-gestor{
    min-height: 300px;
    height: 350px;
}

.grafico-graficos-gestor canvas{
    height: 100% !important;
    width: 100% !important;
}

.texto-leyenda-graficos-gestor{
    font-size: 0.75rem;
}
.leyenda-graficos-gestor{
    display: flex;
    flex-direction: column;
    justify-content: start;
    max-height: 100%;
    white-space: nowrap;
    overflow-y: scroll;
    text-overflow:ellipsis;
    width: 30%;
    gap: 0.625rem;

}
.leyenda-graficos-gestor-pie{
    display: flex;
    justify-content: center;
    max-height: 100%;
    align-items: center;
    width: 100%;
    gap: 0.625rem;
    margin: auto;
    margin-top: 1.5rem;
}

.div-boton-descargar-csv{
    width: 100%;
    display: flex;
    justify-content: right;
    align-items: center;
}
.boton-descargar-csv{
    background: #F5F5F5;
    border-radius: 0.3125rem;
    padding: 0.5rem;
    font-size: 1rem;
    border: none;
    color: #4B4B4B;
}

.modal-matriz{
    position: absolute;
    right: 0;
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0px 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    border-radius:  1.5rem 0 0 1.5rem  ;
    z-index: 9;
    width: 32%;
    border: 0.0625rem solid #ccc;
}
.select-matriz{
    width: 45%;
}
.campos-seleccion{
    padding: 0.625rem;
    margin-top: 1.3rem;
    margin-bottom: 0.9375rem;
}
.campos-seleccion .row .col{
    gap: 0.3125rem;
    display: flex;
    flex-direction: column;
}

.div-botones-matriz{
    display: flex;
    justify-content: right;
    padding: 0 1.3rem;
    margin-bottom: 1.3rem;
}


/* Optimización de matriz */
.container-matriz-selector{
    display: flex;
    justify-content: start;
    align-items: start;
    width: 100%;
    gap: 1rem;
    margin-top: 0.5rem;
}

.modal-matriz{
    position: absolute;
    right: 0;
    background: white;
    padding: 0.8rem 1.5rem;
    box-shadow: 0px 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    border-radius: 1.2rem 0 0 1.2rem;
    z-index: 9;
    width: 30%;
    border: 0.0625rem solid #ccc;
}

/* ESTADISTICAS GESTORES */
.contenedor-estadisticas-gestores{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}
.div-descargar-csv{
    display: flex;
    justify-content: space-between;
    max-width: 65%;
}
.contenedor-tabla-estadisticas-gestores{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    gap: 1rem;
}
.barra-lateral-gestores{
    display: flex;
    justify-content: space-between;
    max-width: 35%;
    width: 35%;
    border: 0.0625rem solid #D0D5DD;
    border-radius: 1.2rem;
    position: relative;
    height: 100%;
    background-color: white;
}
.datos-gestor-gestores{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 45%;
    padding: 0.8rem;
    margin-top: 0.5rem;
    justify-content: start;
    align-items: center;
}
.datos-gestor-gestores img{
    width: 55%;
    object-fit: cover;
    height: 7rem;
    border-radius:100%;
}
.datos-text-gestor-gestores{
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    width: 100%;
    justify-content: center;
    align-items: center;
}
.nombre-gestor-gestores{
    font-size: 0.9rem;
    color: #4B4B4B;
    display: flex;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 0.3rem;
    font-weight: bold;
    line-height: 1.2;
}
.rol-gestor-gestores{
    font-size: 0.75rem;
    color: #4B4B4B;
    text-align: center;
    width: 100%;
    line-height: 1.2;
}
.contacto-text-gestor-gestores{
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    width: 100%;
    justify-content: start;
    align-items: start;
}
.contacto-gestor-gestores{
    font-size: 0.8rem;
    color: #4B4B4B;
    display: flex;
    width: 100%;
    display: flex;
    justify-content: start;
    gap: 0.3rem;
    line-height: 1.2;
}
.contadores-gestor-gestores{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 55%;
    padding: 0.8rem;
    margin-top: 0.5rem;
    justify-content: start;
    align-items: center;
}
.conteo-gestor-gestores{
    display: flex;
    flex-direction: row;
    gap: 0.3rem;
    width: 100%;
    justify-content: start;
    align-items: baseline;
    line-height: 1.2;
}
.loader-gestores {
    position: absolute; /* Posición absoluta respecto al contenedor */
    top: 0;
    left: 0;
    width: 100%; /* Ocupa todo el ancho */
    height: 100%; /* Ocupa todo el alto */
    background: rgb(255, 255, 255); /* Fondo semitransparente */
    display: flex; /* Flexbox para centrar contenido */
    align-items: center; /* Centrado vertical */
    justify-content: center; /* Centrado horizontal */
    z-index: 10; /* Asegura que esté encima de otros elementos */
    border-radius: 1.5rem;

  }
  .loader-gestores img{
    width: 30%;
    object-fit: cover;
    height: 20%;
  }

/* Optimizaciones adicionales de espaciado */
.campos-seleccion{
    padding: 0.4rem;
    margin-top: 0.8rem;
    margin-bottom: 0.6rem;
}

.campos-seleccion .row .col{
    gap: 0.2rem;
    display: flex;
    flex-direction: column;
}

.div-botones-matriz{
    display: flex;
    justify-content: right;
    padding: 0 1rem;
    margin-bottom: 0.8rem;
}

.select-matriz{
    width: 45%;
}

/* Optimización del contenedor principal */
.div-graficos-Reportes-gestor .contenedor-select-graficos .form-select{
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
}

/* Reducir espacios en leyendas */
.leyenda-graficos-gestor{
    display: flex;
    flex-direction: column;
    justify-content: start;
    max-height: 100%;
    white-space: nowrap;
    overflow-y: auto;
    text-overflow: ellipsis;
    width: 30%;
    gap: 0.4rem;
}

.leyenda-graficos-gestor-pie{
    display: flex;
    justify-content: center;
    max-height: 100%;
    align-items: center;
    width: 100%;
    gap: 0.4rem;
    margin: auto;
    margin-top: 1rem;
}

.texto-leyenda-graficos-gestor{
    font-size: 0.7rem;
    line-height: 1.2;
}

/* Estilos para el contenedor de gráficos optimizado */
.contenedorGrafico-reportes-gestor {
    width: 100%;
    margin: 1rem auto;
    padding: 0;
}

/* Contenedor individual para cada gráfico */
.grafico-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.08);
    padding: 1rem;
    height: 17.5rem;
    display: flex;
    flex-direction: column;
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

/* Layout personalizado con rem */
.contenedorGrafico-reportes-gestor {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.fila-graficos {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.grafico-col-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
}

.grafico-col-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* Estilos responsivos para móviles */
@media (max-width: 48rem) {
    .contenedorGrafico-reportes-gestor {
        gap: 1rem;
    }

    .fila-graficos {
        flex-direction: column;
        gap: 1rem;
    }

    .grafico-col-8,
    .grafico-col-4 {
        flex: 1 1 100%;
        max-width: 100%;
    }

    .grafico-container {
        height: 15rem;
        padding: 0.75rem;
    }
}

.grafico-container:hover {
    box-shadow: 0 6px 1.25rem rgba(0,0,0,0.12);
}

/* Asegurar que los gráficos ocupen el espacio disponible */
.grafico-container > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Estilos para los canvas de los gráficos */
.grafico-container canvas {
    max-width: 100% !important;
    max-height: calc(100% - 1.25rem) !important;
    height: auto !important;
    flex: 1;
}

/* Estilos específicos para canvas dentro de contenedores de gráficos */
.grafico-graficos-gestor canvas,
.grafico-pie-gestor canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain;
}

/* Responsive para pantallas más pequeñas */
@media (max-width: 768px) {
    .grafico-container {
        height: 220px;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .contenedorGrafico-reportes-gestor .row {
        margin: 0 !important;
    }

    .contenedorGrafico-reportes-gestor .col-6 {
        padding: 0.25rem !important;
    }
}
