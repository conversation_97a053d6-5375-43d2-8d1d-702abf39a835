import CrearLocacionServicio from "./Crear"
import EditarLocacionServicio from "./Editar"


const LocacionServicio = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  esEdicion,
  selectedSolicitud,
  ver,
  NomTipoSolicitud
}) => {
  return (
    <>    
    {!esEdicion ? 
      <CrearLocacionServicio Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} idTipoSolicitud={idTipoSolicitud} tipoModelo={tipoModelo} NomTipoSolicitud={NomTipoSolicitud} />
      :
      <EditarLocacionServicio Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} selectedSolicitud={selectedSolicitud} tipoModelo={tipoModelo} esEdicion={esEdicion} ver={ver} NomTipoSolicitud={NomTipoSolicitud}/>

    }
    </>

  )
}

export default LocacionServicio