{"name": "prisma-contratos-front", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://greta.pe/Prisma-Contratos/", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@babel/runtime": "^7.26.7", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.1", "@mui/x-date-pickers": "^7.17.0", "@popperjs/core": "^2.11.8", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "bootstrap": "^5.3.3", "chart.js": "^4.4.4", "chartjs-plugin-datalabels": "^2.2.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "qs": "^6.13.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-inlinesvg": "^4.1.3", "react-leaflet": "^5.0.0", "react-router-dom": "^6.26.2", "react-select": "^5.8.0", "react-toastify": "^10.0.6", "regenerator-runtime": "^0.14.1", "sweetalert2": "^11.14.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.25.9", "@eslint/js": "^9.9.0", "@types/bcryptjs": "^2.4.6", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "sass-embedded": "^1.78.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}