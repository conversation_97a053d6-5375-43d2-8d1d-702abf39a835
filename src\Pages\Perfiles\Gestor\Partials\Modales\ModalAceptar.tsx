import React from "react";

interface Solicitud {
    int_idSolicitudes: number;
    str_CodSolicitudes: string;
  }
  
  interface ModalAceptarProps {
    isModalVisibleAceptar: boolean;
    CerrarModalEliminar: () => void;
    solicitudSeleccionada: Solicitud;
    accion: () => void;
  }
  
  const ModalAceptar: React.FC<ModalAceptarProps> = ({
    isModalVisibleAceptar,
    CerrarModalEliminar,
    solicitudSeleccionada,
    accion
  }) => {
    
  
    return (
      <>
        {isModalVisibleAceptar && (
          <div className="modal-aceptar">
            <div className="boton-cerrar-modal-filtros">
              <button
                type="button"
                className="btn-close"
                aria-label="Close"
                onClick={CerrarModalEliminar}
              ></button>
            </div>
            <div className="pregunta-modal-solicitante lato-font">
              <span>
                ¿Seguro que quieres aceptar la solicitud{" "}
                <strong>{solicitudSeleccionada.str_CodSolicitudes}</strong>?
              </span>
            </div>
            <div className="botones-modal-solicitante">
              <button
                className="btn btn-outline-primary"
                onClick={CerrarModalEliminar}
              >
                Cancelar
              </button>
              <button
                className="btn btn-primary"
                onClick={accion}
              >
                Aceptar
              </button>
            </div>
          </div>
        )}
      </>
    );
  };
  
  export default ModalAceptar;