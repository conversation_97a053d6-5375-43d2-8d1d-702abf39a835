import axios from "axios";
import { useEffect, useState } from "react";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
const anioActual = new Date().getFullYear();

const Promedios = ({idUsuario, Suscripcion}) => {
    const [PromedioAtencion, setPromedioAtencion] = useState(null);
    const [PromedioPreparacion, setPromedioPreparacion] = useState(null);
    const [PromedioTotal, setPromedioTotal] = useState(null);
    const [SolicitudesTotal, setSolicitudesTotal] = useState(null);
    const navigate = useNavigate();
    const token = Cookies.get("Token");

    // Validar antes de dividir por 24 para evitar errores
    const SolicitudesTotalMes = SolicitudesTotal ? SolicitudesTotal.total : 0;

    const PromedioAtencionGestor = async () => {
await validateToken();
        try {
            const response = await axios.get(API_GESTOR["PromedioAtencionGeneralTotales"]( Suscripcion,anioActual),{
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`
                }
              });
              const data = response.data; 
          const validData = data.filter(item => item.total !== null && item.int_idGestor !== null);
          const totalSum = validData.reduce((sum, item) => sum + item.total, 0);
          const promedio = validData.length > 0 ? (totalSum / validData.length)/24 :0.0;
  
            setPromedioAtencion(promedio.toFixed(1)|| 0.0); 
        } catch (error) {
            console.error("Error al obtener las estadísticas", error);
            setPromedioAtencion(0);  // En caso de error, setear como null
        }
    };

    const PromedioPreparacionGestor = async () => {
await validateToken();
        try {
            const response = await axios.get(API_GESTOR["PromedioPreparacionGeneralTotales"]( Suscripcion,anioActual),{
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`
                }
              });
              const data = response.data; 
              const validData = data.filter(item => item.total !== null && item.int_idGestor !== null);
              const totalSum = validData.reduce((sum, item) => sum + item.total, 0);
              const promedio = validData.length > 0 ? (totalSum / validData.length)/24 :0.0;
      
            setPromedioPreparacion(promedio.toFixed(1)|| 0.0);
        } catch (error) {
            console.error("Error al obtener las estadísticas", error);
            setPromedioPreparacion(0);
        }
    };

    const PromedioTotalGestor = async () => {
await validateToken();
        try {
            const response = await axios.get(API_GESTOR["PromedioTotalGeneralTotales"]( Suscripcion,anioActual),{
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`
                }
              });
              const data = response.data; 
          const validData = data.filter(item => item.total !== null && item.int_idGestor !== null);
          const totalSum = validData.reduce((sum, item) => sum + item.total, 0);
          const promedio = validData.length > 0 ? (totalSum / validData.length)/24 :0.0;
  
            setPromedioTotal(promedio.toFixed(1)|| 0.0);
        } catch (error) {
            console.error("Error al obtener las estadísticas", error);
            setPromedioTotal(0);
        }
    };

    const SolicitudesTotalGestor = async () => {
await validateToken();
        try {
            const response = await axios.get(API_GESTOR["SolicitudesTotalGeneralTotales"]( Suscripcion,anioActual),{
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`
                }
              });
            setSolicitudesTotal(response.data || null);
        } catch (error) {
            console.error("Error al obtener las estadísticas", error);
            setSolicitudesTotal(null);
        }
    };

    useEffect(() => {
        PromedioAtencionGestor();
        PromedioPreparacionGestor();
        PromedioTotalGestor();
        SolicitudesTotalGestor();
    }, [idUsuario]);

    const Reportes = () => {
        navigate(RoutesPrivate.REPORTESGESTOR, { state: { idUsuario, Suscripcion } });
    };

    return (
        <div className="estadisticas-inicio-gestor">
        <div className="header-estadisticas-inicio-gestor" onClick={Reportes}>
            <div className="titulo-estadisticas-inicio-gestor">Estadísticas</div>
            <i className="fa-solid fa-caret-right"></i>
        </div>
        <div className="container-estadisticas-inicio-gestor">
        
        <div className="card-estadistica-inicio-gestor">
            <div className="datos-card-estadistica-IG">
                <div className="dias-card-estadistica-ig montserrat-font-500">{PromedioAtencion}</div>
                <div className="dias-text-card-estadistica-ig lato-font">Días</div>
            </div>
            <div className="titulo-estadistica-inicio-gestor montserrat-font">Tiempo promedio total de atención</div>
        </div>
        <div className="card-estadistica-inicio-gestor">
            <div className="datos-card-estadistica-IG">
                <div className="dias-card-estadistica-ig montserrat-font-500">{PromedioPreparacion}</div>
                <div className="dias-text-card-estadistica-ig lato-font">Días</div>
            </div>
            <div className="titulo-estadistica-inicio-gestor montserrat-font">Tiempo promedio de firma</div>
        </div>
        <div className="card-estadistica-inicio-gestor">
            <div className="datos-card-estadistica-IG">
                <div className="dias-card-estadistica-ig montserrat-font-500">{PromedioTotal}</div>
                <div className="dias-text-card-estadistica-ig lato-font">Días</div>
            </div>
            <div className="titulo-estadistica-inicio-gestor montserrat-font">Tiempo promedio total</div>
        </div>
        <div className="card-estadistica-inicio-gestor">
            <div className="datos-card-estadistica-IG">
                <div className="dias-card-estadistica-ig montserrat-font-500">{SolicitudesTotalMes}</div>
                <div className="dias-text-card-estadistica-ig lato-font">Contratos</div>
            </div>
            <div className="titulo-estadistica-inicio-gestor montserrat-font">Contratos trabajados por año</div>
        </div>
        </div>
    </div>
    );
};

export default Promedios;