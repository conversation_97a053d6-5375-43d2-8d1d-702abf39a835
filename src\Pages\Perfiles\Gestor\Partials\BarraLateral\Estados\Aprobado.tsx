import React, { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";

import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import axios from "axios";
import ModalDocumentoFirmado from "../../Modales/ModalDocumentoFirmado";
import Cookies from "js-cookie";
import { decrypt, validateToken } from "../../../../../Components/Services/TokenService";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import IconoUpload from "../../../../../../assets/SVG/IconoUpload";
import IconoDownload from "../../../../../../assets/SVG/IconoDownload";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import IconoVer from "../../../../../../assets/SVG/IconoVer";


interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes:string
  };
  historiales: any[]; 
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: ()=>void
  setSelectedSolicitud: () => void
}

const Aprobado: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  aprobadores
}) => {

  const [modales, setModales] = useState({
    isModalVisiblContratoFirmado:false

  });
  const navigate = useNavigate();
    const Suscriptor = decrypt(Cookies.get("suscriptor"));

  const [file, setFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const token = Cookies.get("Token");
  const [solicitud, setSolicitud] = useState(null);

  useEffect(() => {
    const obtenerSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerSolicitud"](solicitudSeleccionada.int_idSolicitudes),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        setSolicitud(response.data);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
        setSolicitud(null);

      } 
    };

      obtenerSolicitud();
  }, [solicitudSeleccionada]);
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFile(event.target.files[0]); 
    }
  };
  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const handleSubmitFiles = async () => {
await validateToken();
    if (!file) {
      Swal.fire("", "Por favor, selecciona un archivo primero", "error");
      return;
    }
    
    try {
      const formDataSolicitud = new FormData();
      formDataSolicitud.append("archivo", file);
      formDataSolicitud.append("str_idSuscriptor", Suscripcion);
      formDataSolicitud.append("str_CodSolicitudes", solicitudSeleccionada.str_CodSolicitudes);
      formDataSolicitud.append("int_idSolicitudes", solicitudSeleccionada.int_idSolicitudes);
      formDataSolicitud.append("str_CodTipoDocumento", "COFI");
      formDataSolicitud.append("int_idUsuarioCreacion", idUsuario);

      const response = await axios.post(
        API_GESTOR["UploadArchivos"](),
        formDataSolicitud,
        {
          headers: {
            "Content-Type": "multipart/form-data",
             "Authorization": `Bearer ${token}`
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        setFile(null);
       
      }else{
        throw new Error("No se pudo ingresar el archivo");
      }
      
    } catch (error) {
      
      Swal.fire("", "No se pudo subir el archivo", "error");
    }
  };
  const handleDownload = async () => {
    await validateToken();
    try {

      const response = await axios.get(API_SOLICITANTE["ListarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"COAP"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if(response.data){

      const response2 = await axios.get(API_SOLICITANTE["DescargarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"COAP"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      , responseType: "blob" });
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        :  `${response.data.nombre_archivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
  useEffect(() => {
    

    if (file) {
        setModales((prev) => ({ ...prev, isModalVisiblContratoFirmado: true }));
    }
  }, [file]); 
      const handleVerSolicitud = () => {
        navigate(RoutesPrivate.EDITARSOLICITUD, {
          state: {
            solicitudSeleccionada,
            Suscripcion,
            idAplicacion,
            idUsuario,
            Suscriptor,
            ver: true,
          },
        });
      };
  return (
   <div className={`conteo-inicio-gestor-pageSolicitudes`}>

      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Subir Documento Firmado" placement="top" >
        <div
              className="icono-barralateral-acciones"
              onClick={handleButtonClick}
            > 
            <IconoUpload size={"1.3rem"} color={"#000"}/>
            </div>
        </Tooltip>
        <Tooltip title="Descargar Documento Aprobado" placement="top">
                    <div
              className="icono-barralateral-acciones"
              onClick={handleDownload}
            > 
            <IconoDownload size={"1.3rem"} color={"#000"}/>
            </div>
                 </Tooltip>
                 <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
      </div>

      <input
        ref={fileInputRef}
        className="form-control"
        type="file"
        multiple
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>

     <ModalDocumentoFirmado
        isModalVisiblContratoFirmado={modales.isModalVisiblContratoFirmado}
        CerrarModalContratoFirmado={() => toggleModal('isModalVisiblContratoFirmado')}
        file={file}
        idUsuario={idUsuario}
        handleSubmitFiles={handleSubmitFiles}
        Suscripcion={Suscripcion}
        idSolicitud={solicitudSeleccionada.int_idSolicitudes}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
        solicitudActual={solicitud}
      />
      </div>
      
  );
};

export default Aprobado;