import React, { useRef, useState, useEffect } from "react";
import IconoUpload from "../../../../../assets/SVG/IconoUpload";
import "./BarraLatera.css";
import IconoBasureroEliminar from "../../../../../assets/SVG/IconoBasureroEliminar";
import Swal from "sweetalert2";

const BarraLateralCrearSolicitud = ({
  files,
  handleFileRemove,
  handleFileChange,
  handleSubmitSolicitudGuardar,
  handleSubmitSolicitudConfirmar,
  esEdicion,
  newFiles,
  ver,
  gestor,
  handleDownload,
  documentoSubido,
  NomTipoSolicitud
}: any) => {
   const [isGuardando, setIsGuardando] = useState(false);
   const [isConfirmando, setIsConfirmando] = useState(false);

   // Debug logs para verificar estados
   useEffect(() => {
     console.log("🔍 Estado isGuardando cambió a:", isGuardando);
   }, [isGuardando]);

   useEffect(() => {
     console.log("🔍 Estado isConfirmando cambió a:", isConfirmando);
   }, [isConfirmando]);

  // Referencias para cada tipo de documento
  const dniRepresentanteRef = useRef<HTMLInputElement>(null);
  const fichaRucRef = useRef<HTMLInputElement>(null);
  const vigenciaPersonaJuridicaRef = useRef<HTMLInputElement>(null);
  const vigenciaPoderRepresentanteRef = useRef<HTMLInputElement>(null);
  const tarifasRef = useRef<HTMLInputElement>(null);
  const segurosRef = useRef<HTMLInputElement>(null);
  const partidaRegistralRef = useRef<HTMLInputElement>(null);
  const tarjetasPropiedadRef = useRef<HTMLInputElement>(null);
  const basesLicitacionRef = useRef<HTMLInputElement>(null);
  const cotizacionServicioRef = useRef<HTMLInputElement>(null);
  const adjuntosRef = useRef<HTMLInputElement>(null);

  // Estados para controlar la visibilidad de cada campo
  const [camposOcultos, setCamposOcultos] = useState({
    DNI_REPRESENTANTE: false,
    FICHA_RUC: false,
    VIGENCIA_PERSONA_JURIDICA: false,
    VIGENCIA_PODER_REPRESENTANTE: false,
    TARIFAS: false,
    SEGUROS: false,
    PARTIDA_REGISTRAL: false,
    TARJETAS_PROPIEDAD: false,
    BASES_LICITACION: false,
    COTIZACION_SERVICIO: false
  });

  // useEffect para ocultar campos cuando se cargan archivos existentes
  useEffect(() => {
    const todosLosArchivos = [...(files || []), ...(newFiles || [])];

    if (todosLosArchivos.length > 0) {
      const tiposExistentes = todosLosArchivos
        .filter((file: any) => file.tipo_adjunto && file.tipo_adjunto !== 'ADJUNTOS')
        .map((file: any) => file.tipo_adjunto);

      // Crear un objeto con los tipos únicos que deben estar ocultos
      const tiposUnicos = [...new Set(tiposExistentes)];
      const nuevosOcultos = {
        DNI_REPRESENTANTE: tiposUnicos.includes('DNI_REPRESENTANTE'),
        FICHA_RUC: tiposUnicos.includes('FICHA_RUC'),
        VIGENCIA_PERSONA_JURIDICA: tiposUnicos.includes('VIGENCIA_PERSONA_JURIDICA'),
        VIGENCIA_PODER_REPRESENTANTE: tiposUnicos.includes('VIGENCIA_PODER_REPRESENTANTE'),
        TARIFAS: tiposUnicos.includes('TARIFAS'),
        SEGUROS: tiposUnicos.includes('SEGUROS'),
        PARTIDA_REGISTRAL: tiposUnicos.includes('PARTIDA_REGISTRAL'),
        TARJETAS_PROPIEDAD: tiposUnicos.includes('TARJETAS_PROPIEDAD'),
        BASES_LICITACION: tiposUnicos.includes('BASES_LICITACION'),
        COTIZACION_SERVICIO: tiposUnicos.includes('COTIZACION_SERVICIO')
      };

      setCamposOcultos(nuevosOcultos);
    } else {
      // Si no hay archivos, mostrar todos los campos
      setCamposOcultos({
        DNI_REPRESENTANTE: false,
        FICHA_RUC: false,
        VIGENCIA_PERSONA_JURIDICA: false,
        VIGENCIA_PODER_REPRESENTANTE: false,
        TARIFAS: false,
        SEGUROS: false,
        PARTIDA_REGISTRAL: false,
        TARJETAS_PROPIEDAD: false,
        BASES_LICITACION: false,
        COTIZACION_SERVICIO: false
      });
    }
  }, [files, newFiles]); // Se ejecuta cuando cambian los arrays de archivos

  // Funciones para manejar clicks en cada tipo de documento
  const handleButtonClick = (tipoDocumento: string) => {
    switch(tipoDocumento) {
      case 'DNI_REPRESENTANTE':
        dniRepresentanteRef.current?.click();
        break;
      case 'FICHA_RUC':
        fichaRucRef.current?.click();
        break;
      case 'VIGENCIA_PERSONA_JURIDICA':
        vigenciaPersonaJuridicaRef.current?.click();
        break;
      case 'VIGENCIA_PODER_REPRESENTANTE':
        vigenciaPoderRepresentanteRef.current?.click();
        break;
      case 'TARIFAS':
        tarifasRef.current?.click();
        break;
      case 'SEGUROS':
        segurosRef.current?.click();
        break;
      case 'PARTIDA_REGISTRAL':
        partidaRegistralRef.current?.click();
        break;
      case 'TARJETAS_PROPIEDAD':
        tarjetasPropiedadRef.current?.click();
        break;
      case 'BASES_LICITACION':
        basesLicitacionRef.current?.click();
        break;
      case 'COTIZACION_SERVICIO':
        cotizacionServicioRef.current?.click();
        break;
      case 'ADJUNTOS':
        adjuntosRef.current?.click();
        break;
      default:
        break;
    }
  };

  // Función para manejar archivos con tipo específico
  const handleFileChangeWithType = (event: React.ChangeEvent<HTMLInputElement>, tipoDocumento: string) => {
    if (event.target.files) {
      const fileArray = Array.from(event.target.files);
      const filesWithType = fileArray.map((file: any) => {
        // Crear un nuevo objeto que incluya las propiedades del archivo y el tipo
        const fileWithType = Object.assign(file, { tipo_adjunto: tipoDocumento });
        return fileWithType;
      });

      // Ocultar el campo específico si no es ADJUNTOS
      if (tipoDocumento !== 'ADJUNTOS') {
        setCamposOcultos(prev => ({
          ...prev,
          [tipoDocumento]: true
        }));
      }

      // Llamar a la función original con los archivos modificados
      handleFileChange({ target: { files: filesWithType } } as any);
      event.target.value = '';
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>, tipoDocumento?: string) => {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      if (tipoDocumento) {
        const fileArray = Array.from(event.dataTransfer.files);
        const filesWithType = fileArray.map((file: any) => {
          const fileWithType = Object.assign(file, { tipo_adjunto: tipoDocumento });
          return fileWithType;
        });

        // Ocultar el campo específico si no es ADJUNTOS
        if (tipoDocumento !== 'ADJUNTOS') {
          setCamposOcultos(prev => ({
            ...prev,
            [tipoDocumento]: true
          }));
        }

        handleFileChange({ target: { files: filesWithType } } as any);
      } else {
        handleFileChange({ target: { files: event.dataTransfer.files } } as any);
      }
      event.dataTransfer.clearData();
    }
  };

  // Función para mostrar campo cuando se elimina un archivo
  const mostrarCampoSiEsNecesario = (archivoEliminado: any) => {
    if (archivoEliminado && archivoEliminado.tipo_adjunto && archivoEliminado.tipo_adjunto !== 'ADJUNTOS') {
      // Verificar si hay otros archivos del mismo tipo (incluyendo newFiles si existe)
      const otrosArchivosDelMismoTipo = files.filter((file: any) =>
        file.tipo_adjunto === archivoEliminado.tipo_adjunto && file !== archivoEliminado
      );

      // También verificar en newFiles si existe
      let otrosEnNewFiles = 0;
      if (newFiles && newFiles.length > 0) {
        otrosEnNewFiles = newFiles.filter((file: any) =>
          file.tipo_adjunto === archivoEliminado.tipo_adjunto && file !== archivoEliminado
        ).length;
      }

      // Si no hay otros archivos del mismo tipo en ningún array, mostrar el campo
      if (otrosArchivosDelMismoTipo.length === 0 && otrosEnNewFiles === 0) {
        setCamposOcultos(prev => ({
          ...prev,
          [archivoEliminado.tipo_adjunto]: false
        }));
      }
    }
  };

  // Función personalizada para manejar la eliminación de archivos
  const handleFileRemoveCustom = (index: number, esArchivoExistente: boolean, archivo?: any) => {
    mostrarCampoSiEsNecesario(archivo);
    handleFileRemove(index, esArchivoExistente, archivo);
  };

  // Función para obtener etiquetas legibles de los tipos de adjunto
  const getTipoAdjuntoLabel = (tipoAdjunto: string) => {
    const labels: { [key: string]: string } = {
      'DNI_REPRESENTANTE': 'DNI del Representante Legal',
      'FICHA_RUC': 'Ficha RUC',
      'VIGENCIA_PERSONA_JURIDICA': 'Vigencia de Persona Jurídica',
      'VIGENCIA_PODER_REPRESENTANTE': 'Vigencia de Poder del Representante',
      'TARIFAS': 'Tarifas',
      'SEGUROS': 'Seguros (Pólizas y Endosos)',
      'PARTIDA_REGISTRAL': 'Partida Registral',
      'TARJETAS_PROPIEDAD': 'Tarjetas de Propiedad',
      'BASES_LICITACION': 'Bases de licitación',
      'COTIZACION_SERVICIO': 'Cotización del servicio',
      'ADJUNTOS': 'Adjuntos'
    };
    return labels[tipoAdjunto] || tipoAdjunto;
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
    if (size < 1024 * 1024 * 1024)
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  // Función para verificar si el tipo de solicitud requiere campos de servicios
  const requiereCamposServicios = () => {
    return NomTipoSolicitud === "Contrato de locación de servicios de cliente" ||
           NomTipoSolicitud === "Contrato de locación de servicios de proveedor" ||
           NomTipoSolicitud === "Contrato de prestación de servicios" ||
           NomTipoSolicitud === "Contrato de Depósito Simple" ||
           NomTipoSolicitud === "Contrato de Administración Almacén On Site" ||
           NomTipoSolicitud === "Contrato de Depósito Temporal" ||
           NomTipoSolicitud === "Contrato de Transporte" ||
           NomTipoSolicitud === "Contrato de Distribución" ||
           NomTipoSolicitud === "Contrato de Depósito y Transporte" ||
           NomTipoSolicitud === "Contrato de Servicio SILE" ||
           NomTipoSolicitud === "Contrato de Depósito temporal y Transporte";
  };

  // Función para verificar si el tipo de solicitud requiere campos de bienes inmuebles
  const requiereCamposBienesInmuebles = () => {
    return NomTipoSolicitud === "Contrato de arrendamiento de bienes muebles o inmuebles" ||
           NomTipoSolicitud === "Contrato de compra / venta de bienes muebles o inmuebles";
  };

  // Función para validar archivos obligatorios según el tipo de contrato
  const validarArchivosObligatorios = () => {
    const todosLosArchivos = [...(files || []), ...(newFiles || [])];
    const tiposExistentes = todosLosArchivos
      .filter((file: any) => file.tipo_adjunto && file.tipo_adjunto !== 'ADJUNTOS')
      .map((file: any) => file.tipo_adjunto);

    // Archivos obligatorios para todos los contratos
    const archivosObligatoriosBase = [
      'DNI_REPRESENTANTE',
      'FICHA_RUC',
      'VIGENCIA_PERSONA_JURIDICA',
      'VIGENCIA_PODER_REPRESENTANTE',
      'TARIFAS',
      'SEGUROS'
    ];

    let archivosObligatorios = [...archivosObligatoriosBase];

    // Agregar archivos específicos según el tipo de contrato
    if (requiereCamposBienesInmuebles()) {
      archivosObligatorios.push('PARTIDA_REGISTRAL', 'TARJETAS_PROPIEDAD');
    }

    if (requiereCamposServicios()) {
      archivosObligatorios.push('BASES_LICITACION', 'COTIZACION_SERVICIO');
    }

    // Verificar qué archivos obligatorios faltan
    const archivosFaltantes = archivosObligatorios.filter(tipo =>
      !tiposExistentes.includes(tipo)
    );

    return archivosFaltantes;
  };

  const guardar = async () => {
    if (isGuardando) return;
    console.log("🔄 Iniciando guardar...");
    try {
      setIsGuardando(true);
      console.log("✅ Estado isGuardando activado:", true);
      await handleSubmitSolicitudGuardar();
      console.log("✅ handleSubmitSolicitudGuardar completado");
    } catch (error) {
      console.error("❌ Error al guardar:", error);
    } finally {
      setIsGuardando(false);
      console.log("✅ Estado isGuardando desactivado:", false);
    }
  };

  const confirmar = async () => {
    if (isConfirmando) return;
    console.log("🔄 Iniciando confirmar...");

    // Validar archivos obligatorios antes de confirmar
    // const archivosFaltantes = validarArchivosObligatorios();

    // if (archivosFaltantes.length > 0) {
    //   const nombresFaltantes = archivosFaltantes.map(tipo => getTipoAdjuntoLabel(tipo));

    //   Swal.fire({
    //     title: "Archivos Obligatorios Faltantes",
    //     html: `<p>Para confirmar la solicitud, debe subir los siguientes archivos obligatorios:</p>
    //            <ul style="text-align: left; margin: 10px 0 ;">
    //              ${nombresFaltantes.map(nombre => `<li> ${nombre}</li>`).join('')}
    //            </ul>`,
    //     icon: "warning",
    //     confirmButtonText: "Entendido",
    //     confirmButtonColor: "#156CFF"
    //   });
    //   return;
    // }

    try {
      setIsConfirmando(true);
      console.log("✅ Estado isConfirmando activado:", true);
      await handleSubmitSolicitudConfirmar();
      console.log("✅ handleSubmitSolicitudConfirmar completado");
    } catch (error) {
      console.error("❌ Error al confirmar:", error);
    } finally {
      setIsConfirmando(false);
      console.log("✅ Estado isConfirmando desactivado:", false);
    }
  };

  return (
    <div className="barraLateral-crear-solicitud">
      <div className="superior-barraLateral-crear-solicitud">


     {!gestor &&
        <div className="accordion" id="accordionArchivosObligatorios">
          <div className="accordion-item" style={{border:"  none"}}>
            <h2 className="accordion-header" id="headingArchivosObligatorios">
              <button
                className="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapseArchivosObligatorios"
                aria-expanded="false"
                aria-controls="collapseArchivosObligatorios"
                style={{padding: "0.5rem" , fontSize: "0.9rem"}}
              >
                Archivos Obligatorios
              </button>
            </h2>
            <div
              id="collapseArchivosObligatorios"
              className="accordion-collapse collapse"
              aria-labelledby="headingArchivosObligatorios"
              data-bs-parent="#accordionArchivosObligatorios"
            >
              <div className="accordion-body" style={{padding: "0.1rem"}}>
                {/* DNI del Representante Legal */}
                {!camposOcultos.DNI_REPRESENTANTE && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={dniRepresentanteRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'DNI_REPRESENTANTE')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('DNI_REPRESENTANTE')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'DNI_REPRESENTANTE')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      DNI del Representante Legal{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Ficha RUC */}
                {!camposOcultos.FICHA_RUC && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={fichaRucRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'FICHA_RUC')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('FICHA_RUC')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'FICHA_RUC')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Ficha RUC{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Vigencia de Persona Jurídica */}
                {!camposOcultos.VIGENCIA_PERSONA_JURIDICA && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={vigenciaPersonaJuridicaRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'VIGENCIA_PERSONA_JURIDICA')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('VIGENCIA_PERSONA_JURIDICA')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'VIGENCIA_PERSONA_JURIDICA')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Vigencia de Persona Jurídica{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Vigencia de Poder del Representante */}
                {!camposOcultos.VIGENCIA_PODER_REPRESENTANTE && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={vigenciaPoderRepresentanteRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'VIGENCIA_PODER_REPRESENTANTE')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('VIGENCIA_PODER_REPRESENTANTE')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'VIGENCIA_PODER_REPRESENTANTE')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Vigencia de Poder del Representante{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Tarifas */}
                {!camposOcultos.TARIFAS && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={tarifasRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'TARIFAS')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('TARIFAS')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'TARIFAS')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Tarifas{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Seguros (Pólizas y Endosos) */}
                {!camposOcultos.SEGUROS && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={segurosRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'SEGUROS')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('SEGUROS')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'SEGUROS')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Seguros (Pólizas y Endosos){" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Partida Registral - Solo para contratos específicos */}
                {(NomTipoSolicitud === "Contrato de arrendamiento de bienes muebles o inmuebles" ||
                  NomTipoSolicitud === "Contrato de compra / venta de bienes muebles o inmuebles") &&
                  !camposOcultos.PARTIDA_REGISTRAL && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={partidaRegistralRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'PARTIDA_REGISTRAL')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('PARTIDA_REGISTRAL')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'PARTIDA_REGISTRAL')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Partida Registral{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Tarjetas de Propiedad - Solo para contratos específicos */}
                {(NomTipoSolicitud === "Contrato de arrendamiento de bienes muebles o inmuebles" ||
                  NomTipoSolicitud === "Contrato de compra / venta de bienes muebles o inmuebles") &&
                  !camposOcultos.TARJETAS_PROPIEDAD && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={tarjetasPropiedadRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'TARJETAS_PROPIEDAD')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('TARJETAS_PROPIEDAD')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'TARJETAS_PROPIEDAD')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Tarjetas de Propiedad{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Bases de licitación - Solo para contratos de servicios */}
                {requiereCamposServicios() && !camposOcultos.BASES_LICITACION && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={basesLicitacionRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'BASES_LICITACION')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('BASES_LICITACION')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'BASES_LICITACION')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Bases de licitación{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}

                {/* Cotización del servicio - Solo para contratos de servicios */}
                {requiereCamposServicios() && !camposOcultos.COTIZACION_SERVICIO && (
                  <div className="div-subirArchivos-barraLateral">
                    <input
                      ref={cotizacionServicioRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(e) => handleFileChangeWithType(e, 'COTIZACION_SERVICIO')}
                    />
                    <div
                      className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
                      onClick={() => handleButtonClick('COTIZACION_SERVICIO')}
                      onDragOver={(e) => e.preventDefault()}
                      onDragEnter={(e) => e.preventDefault()}
                      onDrop={(e) => handleDrop(e, 'COTIZACION_SERVICIO')}
                    >
                      <IconoUpload size={"1.5rem"} color={"#156CFF"} />
                      Cotización del servicio{" "}
                      <button
                        className="btn btn-seleccionar-archivo"
                        type="button"
                        disabled={ver}
                      >
                        Seleccionar Archivo
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>}
                      <span className="titulo-barra-Lateral-CS lato-font-400">
          Documentos Adjuntos
        </span>
        {/* Adjuntos (siempre visible, fuera del acordeón) */}
        <div className="div-subirArchivos-barraLateral">
          <input
            ref={adjuntosRef}
            className="form-control"
            type="file"
            multiple
            style={{ display: "none" }}
            onChange={(e) => handleFileChangeWithType(e, 'ADJUNTOS')}
          />
          <div
            className={`${ver ? "disabled btn btn-subirArchivos" : "btn btn-subirArchivos"}`}
            onClick={() => handleButtonClick('ADJUNTOS')}
            onDragOver={(e) => e.preventDefault()}
            onDragEnter={(e) => e.preventDefault()}
            onDrop={(e) => handleDrop(e, 'ADJUNTOS')}
          >
            <IconoUpload size={"1.5rem"} color={"#156CFF"} />
            Adjuntos{" "}
            <button
              className="btn btn-seleccionar-archivo"
              type="button"
              disabled={ver}
            >
              Seleccionar Archivo
            </button>
          </div>
        </div>

        {/* Lista de archivos cargados */}
        <div className="div-subirArchivos-barraLateral contenedor-archivos-cargados">
            <ul>
              {files.map((file: any, index: number) => (
                <div className="listado-archivos" key={index}>
                  <li
                    onClick={
                      gestor ? () => handleDownload(file.nombre_archivo, file.tipo_adjunto) : undefined
                    }
                    className={gestor ? "cursor-pointer" : ""}
                  >
                    <div className="div-info-archivo-cargado">
                      <div className="nombre-archivo-cargado">
                        {file.name || file.nombre_archivo}
                      </div>
                      {file.tipo_adjunto && (
                        <div className="tipo-documento-info">
                          <small style={{ color: '#156CFF', fontWeight: 'bold' }}>
                            📎 {getTipoAdjuntoLabel(file.tipo_adjunto)}
                          </small>
                        </div>
                      )}
                      <div className="size-archivo-cargado">
                        {formatFileSize(file.size || file.tamaño_archivo)}
                      </div>
                    </div>
                    {ver ? (
                      ""
                    ) : (
                      <div
                        className="contenedor-icono-borrar-archivo-cargado"
                        onClick={() => handleFileRemoveCustom(index, true, file)}
                      >
                        <IconoBasureroEliminar size={"1rem"} color={"#000"} />
                      </div>
                    )}
                  </li>
                </div>
              ))}
              {esEdicion
                ? newFiles.map((file: any, index: number) => (
                  <div className="listado-archivos" key={`new-${index}`}>
                    <li>
                      <div className="div-info-archivo-cargado">
                        <div className="nombre-archivo-cargado">
                          {file.name}
                        </div>
                        {file.tipo_adjunto && (
                          <div className="tipo-documento-info">
                            <small style={{ color: '#156CFF', fontWeight: 'bold' }}>
                              📎 {getTipoAdjuntoLabel(file.tipo_adjunto)}
                            </small>
                          </div>
                        )}
                        <div className="size-archivo-cargado">
                          {formatFileSize(file.size)}
                        </div>
                      </div>
                      <div
                        className="contenedor-icono-borrar-archivo-cargado"
                        onClick={() => handleFileRemoveCustom(index, false, file)}
                      >
                        <IconoBasureroEliminar size={"1rem"} color={"#000"} />
                      </div>
                    </li>
                  </div>
                ))
                : ""}
            </ul>
          </div>
      </div>
      {!ver && (
        <div className="botones-crear-solicitud">
          <button
            className="btn btn-outline-primary"
            onClick={guardar}
            disabled={isGuardando || isConfirmando}
          >
            <i className="fa-solid fa-download"></i> {isGuardando ? "Guardando..." : "Guardar"}
          </button>
          <button
            className="btn btn-primary"
            onClick={confirmar}
            disabled={(!documentoSubido && gestor === true) || isGuardando || isConfirmando}
          >
            {isConfirmando
              ? "Confirmando..."
              : gestor
                ? documentoSubido === false
                  ? "Documento No Subido"
                  : "Enviar Validación"
                : "Confirmar Solicitud"}
          </button>
        </div>
      )}
    </div>
  );
};

export default BarraLateralCrearSolicitud;
