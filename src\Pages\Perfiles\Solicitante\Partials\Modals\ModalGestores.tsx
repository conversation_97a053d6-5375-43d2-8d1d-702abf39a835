import axios from "axios";
import React, { useEffect, useState } from "react";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import Swal from "sweetalert2";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
import getLogs from "../../../../Components/Services/LogsService";

interface ModalGestoresProps {
  isModalVisibleAsignar: boolean;
  CerrarAsignar: () => void;
  idAplicacion: number;
  Suscripcion: string;
  idSolicitud: number;
  idUsuario: number;
  SubmitObtenerDatos: () => void;
  setSelectedSolicitud: (solicitud: any) => void;
  Controller: boolean
}
interface Gestor {
  int_idUsuario: number;
  str_Nombres: string;
  str_Apellidos: string;
  nombre_especialidad: string;
}
const ModalGestores: React.FC<ModalGestoresProps> = ({ 
  isModalVisibleAsignar,
  CerrarAsignar,
  idAplicacion,
  Suscripcion,
  idSolicitud,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  Controller,
  solicitudSeleccionada
}) => {
  const [gestores, setGestores] = useState<Gestor[]>([]); 
  const token = Cookies.get("Token"); 
const [gestorSeleccionado, setGestorSeleccionado] = useState<number | null>(null); 
  useEffect(() => {
    const obtenerGestores = async () => {
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerGestores"](Suscripcion, idAplicacion),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const respuesta = response.data;
        setGestores(respuesta);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    obtenerGestores();
  }, [Suscripcion, idAplicacion]);

  const handleAsignarGestor = async () => {
    if(solicitudSeleccionada?.estado_nombre !== "Asignado" && solicitudSeleccionada?.estado_nombre !== "Nuevo"){
      Swal.fire({
        icon: 'error',
        title: '',
        text: 'No se puede cambiar el gestor porque la solicitud ya está en proceso',
      })
      SubmitObtenerDatos()
      return
    }
    if(solicitudSeleccionada?.estado_nombre === "Nuevo"){
      Swal.fire({
        icon: 'error',
        title: '',
        text: 'No se puede cambiar el gestor porque la solicitud aun no está en la fase de asignación',
      })
      SubmitObtenerDatos()
      return
    }
    await validateToken();
    try {
      const response = await axios.put(API_SOLICITANTE["AsignarGestor"](), {
        int_idSolicitudes: idSolicitud,
        int_idGestor: gestorSeleccionado,
        int_idUsuarioModificacion: idUsuario,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        try {
          const respuestaActualizarEstado = await axios.put(
            API_SOLICITANTE["ActualizarEstado"](),
            {
              str_idSuscriptor: Suscripcion,
              nombre_estado: "Asignado",
              int_idUsuarioCreacion: idUsuario,
              int_idSolicitudes: idSolicitud,
            },{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            }
            
          );
          if(respuestaActualizarEstado.status >= 200 && respuestaActualizarEstado.status < 300){
            Swal.fire("", "Gestor asignado a la solicitud", "success");
          CerrarAsignar();
          SubmitObtenerDatos();
          setSelectedSolicitud({});
 
          }
        } catch {
          console.error(`Error para cambiar el estado ${response.status}`);
        }
        await getLogs(gestorSeleccionado,null,null,"Listado Solicitudes","Solicitudes","Asignar Gestor","Contratos","PUT");

      } else {
        console.error(`Error: código de estado ${response.status}`);
      }

    } catch (error) {
      return error
    }
  };
  return (
    <>
      {isModalVisibleAsignar && (
        <div className={Controller?  `modal-asignar-controller` :`modal-aceptar-solicitud`}>
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarAsignar}
            ></button>
          </div>
          <div className="titulo-modal-filtros">
            <span className="text-titulo-modal-filtros lato-font size-titulos">
              Asignar un gestor
            </span>
          </div>
          {gestores.map((gestor, index) => (
        <div className="form-check input-select-gestor" key={index}>
          <input
            className="form-check-input"
            type="radio"
            name="flexRadioDefault"
            id="flexRadioDefault1"
            onChange={() => setGestorSeleccionado(gestor.int_idUsuario)}
          />
          <label className="labels-datos-gestor montserrat-font">
            <span className="label-nombre-gestor">
              {gestor.str_Nombres} {gestor.str_Apellidos}
            </span>{" "}
            <span className="label-nombre-gestor">
              {gestor.nombre_especialidad}
            </span>
          </label>
        </div>
      ))}
          <div className="div-btn-asignar">
            <button className="btn btn-asignar" onClick={handleAsignarGestor}>
              Asignar
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalGestores;
