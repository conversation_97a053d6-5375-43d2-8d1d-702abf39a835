import React, { useEffect, useState } from "react";
import { <PERSON>, Step, Step<PERSON>abel, Stepper } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import axios from "axios";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../../../../assets/Api/ApisSolicitante";
import BarraLateralCrearSolicitud from "../../../../../../Solicitante/CrearSolicitud/BarraLateral/BarraLateralCrearSolicitud";
import API_GESTOR from "../../../../../../../../assets/Api/ApisGestor";
import { RoutesPrivate } from "../../../../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../../../Components/Services/TokenService";
import IconoBasureroEliminar from "../../../../../../../../assets/SVG/IconoBasureroEliminar";
import IconoMoneda from "../../../../../../../../assets/SVG/IconoMoneda";
import IconoDolares from "../../../../../../../../assets/SVG/IconoSoles";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");

interface Consorcio {
  UsuarioId: number | null;
  idAplicacion: number | null;
  Suscriptor: number | null;
  Suscripcion: string | null;
  selectedSolicitud: object | null;
  Asignado: boolean;
}

interface FormDataSolicitud {
  int_idUsuarioModificacion: number | string;
  str_idSuscriptor: string;
  int_idEmpresa: string;
  int_idUnidadNegocio: string;
  int_idTipoSol: number;
  int_SolicitudGuardada: null | number;
  str_DeTerceros: string;
  dt_FechaEsperada: string;
  db_Honorarios: number;
}

interface FormDataContenido {
  str_DocAdjuntos: string;
  str_ObjetivoContrato: string;
  str_ObligacionesConjuntas: string;
  str_TipoServicio: string;
  str_InfoAdicional: string;
  int_idInterlocutor: number | null;
  int_idInterlocutorComprador: number | null;
  str_idSuscriptor: string;

  str_RentaPactada: string;
  str_Moneda: string;
  str_BienPartidaCertificada: string;
  dt_FechaArriendo: string;
}

const Consorcio: React.FC<Consorcio> = ({
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  Asignado,
  documentoSubido,
}) => {
  const [files, setFiles] = useState([]);
  const token = Cookies.get("Token");
  const [errors, setErrors] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [clausulasIncluidas, setClausulasIncluidas] = useState([]);
  const [clausulasSeleccionadas, setClausulasSeleccionadas] = useState([]);
  const [
    interlocutorCliAsociadoEncontrado,
    setInterlocutorCliAsociadoEncontrado,
  ] = useState(false);
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");

  const [idInterlocutorCliAsociado, setIdInterlocutorCliAsociado] =
    useState(null);
  const [newFiles, setNewFiles] = useState([]);
  const [idContenidoSolicitud, setIdContenidoSolicitud] = useState(null);
  const [totalAporte, setTotalAporte] = useState(0);
  const navigate = useNavigate();
  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };
  const [asociados, setAsociados] = useState([createInitialFormData()]);
  const [TiposMoneda, setTiposMoneda] = useState({str_Moneda: "", str_Pais: ""});

  function createInitialFormData() {
    return {
      str_TipoDoc: "Documento de identidad personal",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Documento: "",
      str_RLDocumento: "",
      str_Interlocutor: "",
      str_RepLegal: "",
      str_Domicilio: "",
      int_RLPartida: "",
      str_Obligacion: "",
      int_idInterlocutor: "",
      str_ValorAporte: "",
      str_PorcentajeAporte: "",
      str_ValorServicios: "",
      str_ValorHonorarios: "",
    };
  }
  const handleAddAsociado = () => {
    setAsociados([...asociados, createInitialFormData()]);
  };
  const handleRemoveAsociado = (index) => {
    if (asociados.length === 1){
      Swal.fire ("","No se puede eliminar el único asociado existente", "error")
      return
    }
    setAsociados(asociados.filter((_, i) => i !== index));
  };
  useEffect(() => {
    const sumaAporte = asociados.reduce((sum, asociado) => {
      const aporte = parseFloat(asociado.str_PorcentajeAporte) || 0;
      return sum + aporte;
    }, 0);
    setTotalAporte(sumaAporte);
  }, [asociados]);

  const [formDataClienteAsociado, setFormDataClienteAsociado] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_RazonSocial: "",
  });

  const [formDataSolicitud, setFormDataSolicitud] = useState<FormDataSolicitud>(
    {
      int_idUsuarioModificacion: UsuarioId,
      int_idEmpresa: selectedSolicitud.int_idEmpresa,
      int_idUnidadNegocio: selectedSolicitud.int_idUnidadNegocio,
      int_idTipoSol: selectedSolicitud.int_idTipoSol,
      int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
      str_DeTerceros: selectedSolicitud.str_DeTerceros,
      dt_FechaEsperada: selectedSolicitud.dt_FechaEsperada,
      db_Honorarios: selectedSolicitud.db_Honorarios,
      str_idSuscriptor: Suscripcion,
    }
  );
  const [formDataContenido, setFormDataContenido] = useState<FormDataContenido>(
    {
      str_DocAdjuntos: files.length >= 1 ? "si" : "no",
      str_ObjetivoContrato: "",
      str_ObligacionesConjuntas: "",
      str_TipoServicio: "",
      str_InfoAdicional: "",
      int_idInterlocutor: null,
      int_idInterlocutorComprador: null,
      str_idSuscriptor: Suscripcion,
      str_RentaPactada: "",
      str_Moneda: "dolares",
      str_BienPartidaCertificada: "",
      dt_FechaArriendo: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      str_InfoCompartida: "",
    }
  );

  const validateForm = (): boolean => {
    let newErrors: { [key: string]: string } = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.str_ObligacionesConjuntas)
      newErrors.str_ObligacionesConjuntas = "El Plazo es requerido";

    setErrors(newErrors);
    // Retornar true si no hay errores
    return Object.keys(newErrors).length === 0;
  };
  const validateFormDocumentos = (): boolean => {
    let newErrors: { [key: string]: string } = {};

    asociados.forEach((asociado, index) => {
      if (
        asociado.str_TipoDoc === "RUC" &&
        asociado.str_Documento?.length !== 11
      ) {
        newErrors[`asociado_${index}_str_Documento`] =
          "El Ruc debe tener como minimo 11 caracteres";
      }
      if (
        asociado.str_TipoDoc === "DNI" &&
        asociado.str_Documento?.length !== 8
      ) {
        newErrors[`asociado_${index}_str_Documento`] =
          "El DNI debe tener como mínimo 8 caracteres";
      }
      if (
        asociado.str_RLTipoDocumento === "RUC" &&
        asociado.str_RLDocumento?.length !== 11
      ) {
        newErrors[`asociado_${index}_str_Documento`] =
          "El Ruc debe tener como minimo 11 caracteres";
      }
      if (
        asociado.str_RLTipoDocumento === "DNI" &&
        asociado.str_RLDocumento?.length !== 8
      ) {
        newErrors[`asociado_${index}_str_Documento`] =
          "El DNI debe tener como mínimo 8 caracteres";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const steps = ["DATOS GENERALES", "DATOS DE CONTRATO"];
  const fetchArchivos = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["ListarArchivosEditar"](
          Suscripcion,
          selectedSolicitud.int_idSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setFiles(response.data);
    } catch (error) {
      console.error("Error al obtener las empresas:", error);
    }
  };
  const buscarAsociado = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      return response.data; // Retorna los datos del interlocutor
    } catch (error) {
      
      return null; // Retorna null si hay un error
    }
  };
  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);

    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            str_PlazoSolicitud: response.data.str_PlazoSolicitud,
            str_InfoAdicional: response.data.str_InfoAdicional,
            int_idInterlocutor: response.data.int_idInterlocutor,
            int_idInterlocutorComprador:
              response.data.int_idInterlocutorComprador,
            str_BienDescripcion: response.data.str_BienDescripcion,
            str_BienUso: response.data.str_BienUso,
            str_BienDireccion: response.data.str_BienDireccion,
            str_RentaPactada: response.data.str_RentaPactada,
            str_BienMuebleInmueble: response.data.str_BienMuebleInmueble,
            dt_FechaArriendo: response.data.dt_FechaArriendo,
            str_Moneda: response.data.str_Moneda,
            str_BienPartidaCertificada:
              response.data.str_BienPartidaCertificada,
            str_ObligacionesConjuntas: response.data.str_ObligacionesConjuntas,
            str_InfoCompartida: response.data.str_InfoCompartida,
          }));
          setIdContenidoSolicitud(response.data.int_idSolicitudCont);
          const responseConsorcio = await axios.get(
            API_SOLICITANTE["ObtenerDatosConsorcio"](
              response.data.int_idSolicitudCont
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseConsorcio.data) {
            
            const nuevosAsociados = await Promise.all(
              responseConsorcio.data.map(async (data) => {
                try {
                  
                  const interlocutorData = await buscarAsociado(
                    data.str_Documento
                  );
                  
                  if (interlocutorData) {
                    
                    return {
                      str_TipoDoc: "Documento de identidad personal",
                      str_RLTipoDocumento: "Documento de identidad personal",
                      str_Documento: data.str_Documento,
                      str_RLDocumento: interlocutorData.str_RLDocumento || "",
                      str_Interlocutor: interlocutorData.str_Interlocutor || "",
                      str_RepLegal: interlocutorData.str_RepLegal || "",
                      str_Domicilio: interlocutorData.str_Domicilio || "",
                      int_RLPartida: interlocutorData.int_RLPartida || "",
                      int_idInterlocutor:
                        interlocutorData.int_idInterlocutor || "",
                      str_Obligacion: data.str_Obligacion || "",
                      str_ValorAporte: data.str_ValorAporte || "",
                      str_PorcentajeAporte: data.str_PorcentajeAporte || "",
                      str_ValorServicios: data.str_ValorServicios || "",
                      str_ValorHonorarios: data.str_ValorHonorarios || "",
                    };
                  } else {
                    
                    return null;
                  }
                } catch (error) {
                  
                  return null;
                }
              })
            );
            const filteredAsociados = nuevosAsociados.filter(Boolean);
            

            setAsociados(filteredAsociados);
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_InfoAdicional: "",
            str_ObligacionesConjuntas: "",
            str_BienPartidaCertificada: "",
          }));
        }
        
      } catch (error) {
        
      }
    };
    const ObtenerInterlocutorAsociado = async () => {
      await validateToken();
      try {
        const responseInterlocutor = await axios.get(
          API_GESTOR["BuscarInterlocutorID"](
            selectedSolicitud.int_idClienteAsociado
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (responseInterlocutor.data) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_Documento: responseInterlocutor.data.str_Documento,
            str_Interlocutor: responseInterlocutor.data.str_Interlocutor
        }));
          buscarInterlocutor(responseInterlocutor.data.str_Documento);
        } else {
          ;
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    const fetchClausulasIncluidas = async () => {
      await validateToken();
      try {
        const responseIncluidas = await axios.get(
          API_GESTOR["ObtenerClausulasIncluidas"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedSolicitud.int_idEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasIncluidas(responseIncluidas.data);

        const responseActivas = await axios.get(
          API_GESTOR["ObtenerClausulasActivas"](
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasSeleccionadas(responseActivas.data.clausulas_activas);
      } catch (error) {
        console.error("Error al obtener las cláusulas:", error);
      }
    };
    fetchClausulasIncluidas();
    ObtenerInterlocutorAsociado();
    DatosContenidoSolicitud();
    fetchArchivos();
  }, []);
  useEffect(() => {
   
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](selectedSolicitud.int_idEmpresa, Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
          
        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    fetchMoneda();

  } , [selectedSolicitud.int_idEmpresa]);
  const renderClausulas = () => {
    if (!clausulasIncluidas || clausulasIncluidas.length === 0) {
      return (
        <div className="no-clausulas-mensaje">
          No hay cláusulas asignadas para esta solicitud
        </div>
      );
    }
  
    const groupedClausulas = [];
  
    for (let i = 0; i < clausulasIncluidas.length; i += 2) {
      groupedClausulas.push(clausulasIncluidas.slice(i, i + 2));
    }
  
    return groupedClausulas.map((group, index) => (
      <div className="inputs-crear-solicitud" key={index}>
        {group.map((clausula, idx) => (
          <div className="div-input-crear-solicitud" key={idx}>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="checkbox"
                value={clausula.int_idClausulasIncluidas}
                checked={clausulasSeleccionadas.includes(
                  clausula.int_idClausulasIncluidas
                )}
                onChange={(e) =>
                  handleCheckboxChange(
                    clausula.int_idClausulasIncluidas,
                    e.target.checked
                  )
                }
                disabled
              />
              <label className="form-check-label">
                {clausula.clausula_legal_nombre}
              </label>
            </div>
          </div>
        ))}
      </div>
    ));
  };
  const handleCheckboxChange = async (clausulaId, isChecked) => {
    let updatedClausulas;
    if (isChecked) {
      updatedClausulas = [...clausulasSeleccionadas, clausulaId];
    } else {
      updatedClausulas = clausulasSeleccionadas.filter(
        (id) => id !== clausulaId
      );
    }
    setClausulasSeleccionadas(updatedClausulas);

    try {
      if (isChecked) {
        await axios.post(
          API_GESTOR["ActivarClausulas"](),
          {
            str_idSuscripcion: Suscripcion,
            int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
            int_idClausulaIncluida: clausulaId,
            int_idUsuarioCreacion: UsuarioId,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
      } else {
        // Llamada a la API para eliminar la cláusula deseleccionada
        await axios.delete(
          API_GESTOR["EliminarClausula"](
            clausulaId,
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
      }
    } catch (error) {
      console.error("Error al actualizar la cláusula:", error);
    }
  };
  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setNewFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };
  const EliminarArchivo = async (file) => {
    try {
      const response = await axios.delete(
        API_GESTOR["EliminarArchivo"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          file.nombre_archivo,
          file.int_idArchivos
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        fetchArchivos();
      }
    } catch (error) {
      console.error("Error al eliminar la solicitud:", error);
    }
  };
  const handleFileRemove = (index, isStoredFile, file) => {
    if (isStoredFile) {
      EliminarArchivo(file);
    } else {
      setNewFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };

  const handleInputChangeAsociados = (index, event) => {
    const { name, value } = event.target;
    const newAsociados = [...asociados];
    newAsociados[index][name] = value;

    if (name.startsWith("str_TipoDoc")) {
      newAsociados[index].str_TipoDoc = value;
    }
    if (name.startsWith("str_RLTipoDocumento")) {
      newAsociados[index].str_RLTipoDocumento = value;
    }

    setAsociados(newAsociados);
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    if (name.startsWith("CliAsociado_")) {
      const fieldName = name.replace("CliAsociado_", "");
      setFormDataClienteAsociado((prevData) => ({
        ...prevData,
        [fieldName]: value,
      }));

      if (fieldName === "str_Documento") {
        buscarInterlocutor(value);
      }
    }
  };

  const buscarInterlocutor = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_GESTOR["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      if (response.data) {
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
           str_RazonSocial: response.data.str_RazonSocial,
        }));
        setIdInterlocutorCliAsociado(response.data.int_idInterlocutor);
        setInterlocutorCliAsociadoEncontrado(true);
      } else {
        setInterlocutorCliAsociadoEncontrado(false);
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
           str_RazonSocial: "",
        }));
        setIdInterlocutorCliAsociado(null);
      }
    } catch (error) {
      setInterlocutorCliAsociadoEncontrado(false);
      setFormDataClienteAsociado((prevData) => ({
        ...prevData,
         str_RazonSocial: "",
      }));
      setIdInterlocutorCliAsociado(null);
    }
  };
  const resetInterlocutor = (index: number) => {
    const newAsociados = [...asociados];
    newAsociados[index] = {
      ...newAsociados[index],
      str_Interlocutor: "",
      str_RepLegal: "",
      int_RLPartida: "",
      str_RLTipoDocumento: "Documento de identidad personal",      
      str_Domicilio: "",
      str_RLDocumento: "",
      str_TipoDoc: "Documento de identidad personal",

    };
    setAsociados(newAsociados);
  };
  const buscarInterlocutorAsociado = async (ruc: string, index: number) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      if (response.data) {
        // Actualiza solo el asociado correspondiente
        const newAsociados = [...asociados];
        newAsociados[index] = {
          ...newAsociados[index],
          str_Interlocutor: response.data.str_Interlocutor,
          str_Correo: response.data.str_Correo,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio,
          str_RLDocumento: response.data.str_RLDocumento,
          int_idInterlocutor: response.data.int_idInterlocutor,
        };
        setAsociados(newAsociados);
      } else {
        // Manejo si no se encuentra el interlocutor
        resetInterlocutor(index);
      }
    } catch (error) {
      
      resetInterlocutor(index);
    }
  };
  const CambiarEstado = async () => {
    await validateToken();
    try {
      const responseCambiarEstado = await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: !Asignado ? "En Validación" : "En Proceso",
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitGuardarSolicitud = async (idInterlocutorCliAsociado) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitConfirmarSolicitud = async (idInterlocutorCliAsociado) => {
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop(); 
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };

      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitFiles = async () => {
    await validateToken();
    try {
      for (const file of newFiles) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          selectedSolicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          selectedSolicitud.int_idSolicitudes
        );
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }

        
      }

      
    } catch (error) {
      
    }
  };

  const handleSubmitInterlocutores = async () => {
    await validateToken();
    try {
      if (interlocutorCliAsociadoEncontrado) {
        const formDataModificadaComprador = {
          ...formDataClienteAsociado,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificadaComprador.int_idUsuarioCreacion;

        const response = await axios.put(
          API_GESTOR["ActualizarInterlocutor"](idInterlocutorCliAsociado),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }
        
        return idInterlocutorCliAsociado;
      } else {
        const response = await axios.post(
          API_GESTOR["AgregarInterlocutor"](),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }

        
        return response.data.int_idInterlocutor;
      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };
  const handleSubmitContenidoSolicitud = async () => {
    try {
      const updatedFormData = {
        ...formDataContenido,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarContenidoSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
        await handleSubmitAsociados();
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitAsociados = async () => {
    await validateToken();
    const responseeliminar = await axios.delete(
      API_SOLICITANTE["EliminarAsociados"](idContenidoSolicitud),
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (responseeliminar.status >= 200 && responseeliminar.status < 300) {
      try {
        for (const asociado of asociados) {

          if (asociado.int_idInterlocutor) {
            const formDataModificado = {
              ...asociado,
              int_idUsuarioModificacion: UsuarioId,
            };
            delete formDataModificado.int_idUsuarioCreacion;

            const response = await axios.put(
              API_SOLICITANTE["ActualizarInterlocutor"](
                asociado.int_idInterlocutor
              ),
              formDataModificado,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.status !== 200) {
              throw new Error("No se pudo actualizar el interlocutor");
            }
            await handleCrearConsorcio(
              asociado.int_idInterlocutor,
              asociado.str_ValorAporte,
              asociado.str_PorcentajeAporte,
              asociado.str_ValorServicios,
              asociado.str_ValorHonorarios,
              asociado.str_Obligacion
            );
            
          } else {
            const formDataNuevo = {
              ...asociado,
              int_idUsuarioCreacion: UsuarioId,
              str_idSuscripcion: Suscripcion,
            };

            const response = await axios.post(
              API_SOLICITANTE["AgregarInterlocutor"](),
              formDataNuevo,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            await handleCrearConsorcio(
              asociado.int_idInterlocutor,
              asociado.str_ValorAporte,
              asociado.str_PorcentajeAporte,
              asociado.str_ValorServicios,
              asociado.str_ValorHonorarios,
              asociado.str_Obligacion
            );
            if (response.status !== 201) {
              throw new Error("No se pudo ingresar el interlocutor");
            }
            asociado.int_idInterlocutor = response.data.int_idInterlocutor;
            
          }
        }
      } catch (error) {
        
      }
    }
  };

  const handleCrearConsorcio = async (
    idAsociado: number | string,
    str_ValorAporte: string,
    str_PorcentajeAporte: string,
    str_ValorServicios: string,
    str_ValorHonorarios: string,
    str_Obligacion: string
  ) => {
    try {
      const updatedFormData = {
        int_idInterlocutor: idAsociado,
        str_ValorAporte: str_ValorAporte,
        str_PorcentajeAporte: str_PorcentajeAporte,
        str_ValorServicios: str_ValorServicios,
        str_ValorHonorarios: str_ValorHonorarios,
        int_idUsuarioCreacion: UsuarioId,
        str_Obligacion: str_Obligacion,
        int_idSolicitudCont: idContenidoSolicitud,
      };

      const responseListConsorcio = await axios.post(
        API_GESTOR["AgregarConsorcio"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (
        responseListConsorcio.status >= 200 &&
        responseListConsorcio.status < 300
      ) {
        handlerConsorcioSolicitud(
          responseListConsorcio.data.id,
          idContenidoSolicitud
        );
      } else {
      }
    } catch (error) {
      return error
    }
  };
  const handlerConsorcioSolicitud = async (
    idConsorcio: number,
    idContenidoSolicitud: number
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        int_idConsorcio: idConsorcio,
        int_idSolicitudCont: idContenidoSolicitud,
        int_idUsuarioCreacion: UsuarioId,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarConsorcioSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
      
    } catch (error) {
      return error
    }
  };
  const isInvalidLength = (type, value, requiredLength) =>
    type === "RUC" && value.length < requiredLength && value.length > 0;

  const isInvalidDNILength = (type, value, requiredLength) =>
    type === "DNI" && value.length < requiredLength && value.length > 0;

  const handleSubmitSolicitudGuardar = async () => {
 
    if (asociados.length < 2) {
      Swal.fire(
        "",
        "No pueden haber menos de 2 asociados en la solicitud",
        "error"
      );
      return;
    }
    await validateToken();
    if (totalAporte !== 100) {
      Swal.fire(
        "",
        "La suma de los porcentajes de aporte debe ser 100%",
        "error"
      );
      return;
    }
    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        success = false;
        errorMessages.push("No se pudo obtener el ID del interlocutor.");
      } else {
        await handleSubmitGuardarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al guardar la solicitud: " + err.message);
        });
      }

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }

      if (idContenidoSolicitud) {
        await handleSubmitContenidoSolicitud();
      }
      if (Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Guardada Correctamente..",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };

  const handleSubmitSolicitudConfirmar = async () => {
 
    if (asociados.length < 2) {
      Swal.fire(
        "",
        "No pueden haber menos de 2 asociados en la solicitud",
        "error"
      );
      return;
    }
    await validateToken();
    if (totalAporte !== 100) {
      Swal.fire(
        "",
        "La suma de los porcentajes de aporte debe ser 100%",
        "error"
      );
      return;
    }
if (!validateForm()) {
      const errorMessages = Object.values(errors).pop(); 
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }

    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        success = false;
        errorMessages.push("No se pudo obtener el ID del interlocutor.");
      } else {
        await handleSubmitConfirmarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });
      }
      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (!Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Confirmada.",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };
  const handleDownload = async (nombreArchivo: string, tipo_adjunto: string) => {
    try {
      const response2 = await axios.get(
        API_GESTOR["DescargarArchivoNombre"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          nombreArchivo,tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          responseType: "blob",
        }
      );
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        : `${nombreArchivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={selectedSolicitud.nombre_completo}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_NombreEmpresa"
                        value={selectedSolicitud.str_NombreEmpresa}
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Descripcion_UnidadNegocio"
                        value={selectedSolicitud.str_Descripcion_UnidadNegocio}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha de Registro:{" "}
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={new Date().toISOString().split("T")[0]}
                          className="form-control"
                          value={
                            selectedSolicitud.dt_FechaRegistro.split("T")[0] ||
                            ""
                          }
                          name="dt_FechaRegistro"
                          readOnly
                        />
                      </div>
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha Esperada de entrega:{" "}
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={new Date().toISOString().split("T")[0]}
                          className="form-control"
                         value={
                            formDataSolicitud.dt_FechaEsperada
                              ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                              : ""
                          }
                          name="dt_FechaEsperada"
                          readOnly
                        />
                      </div>
                    </div>
                  </div>
                  {asociados.map((asociado, index) => (
                    <div key={index}>
                      <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        
                        <h5>Datos del Asociado {index + 1}</h5>
                        <div className="div-eliminarAsociado">
                          <div className="eliminar-asociado" onClick={()=>handleRemoveAsociado(index)}><IconoBasureroEliminar size={'1rem'} color={'#294FCF'} /></div>
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                      <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name={`str_TipoDoc_${index}`} id="tipoDocumento" className="form-select" onChange={(event)=>handleInputChangeAsociados(index,event)} value={asociado.str_TipoDoc || ""} disabled >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name={`str_RLTipoDocumento${index}`} id="tipoDocumento" className="form-select" onChange={(event)=>handleInputChangeAsociados(index,event)} value={asociado.str_RLTipoDocumento || ""} disabled >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <input
                            type="number"
                            onKeyDown={(e) => {
                              if (["e", "E", "+", "-","."].includes(e.key)) {
                                e.preventDefault();
                              }
                            }}
                            onWheel={(e) => e.target.blur()}
                            className="form-control"
                            name="str_Documento"
                            value={asociado.str_Documento}
                            onChange={(e) => {
                              const maxLength =
                                 15;
                              const value = e.target.value;

                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index, e);

                                if (value.length >= 8) {
                                  buscarInterlocutorAsociado(value, index);
                                }
                              }
                            }}
                            readOnly
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <input
                            type="number"
                            onKeyDown={(e) => {
                              if (["e", "E", "+", "-","."].includes(e.key)) {
                                e.preventDefault();
                              }
                            }}
                            onWheel={(e) => e.target.blur()}
                            name="str_RLDocumento"
                            className="form-control"
                            value={asociado.str_RLDocumento}
                            placeholder=""
                            onChange={(e) => {
                              const maxLength =
                                 15;
                              const value = e.target.value;

                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index, e);
                              }
                            }}
                            readOnly
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Asociado {index + 1}:
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="str_Interlocutor"
                            value={asociado.str_Interlocutor}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                            readOnly
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Representante Legal:
                          </label>
                          <input
                            type="text"
                            name="str_RepLegal"
                            className="form-control"
                            value={asociado.str_RepLegal}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                            readOnly
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">Domicilio:</label>
                          <input
                            type="text"
                            className="form-control"
                            name="str_Domicilio"
                            value={asociado.str_Domicilio}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                            readOnly
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Partida Registral :{" "}
                          </label>
                          <input
                            type="text"
                            name="int_RLPartida"
                            className="form-control"
                            value={asociado.int_RLPartida}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                            readOnly
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Obligaciones del Asociado {index + 1}
                          </label>
                          <textarea
                            className="form-control"
                            id=""
                            value={asociado.str_Obligacion}
                            name="str_Obligacion"
                            rows={3}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                            readOnly
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="boton-crear-asociado">
                    <button
                      type="button"
                      className="btn-asignar"
                      onClick={handleAddAsociado}
                    >
                      Agregar Asociado
                    </button>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Moneda de Contrato</h5>
                  </div>
                  <div className="div-input-crear-solicitud">
                    <div className="radio-inputs-crear-solicitud">
                    {TiposMoneda.str_Moneda !== "Dolares" && (
                          <div
        className={`check-group form-check-inline ${
          selectedTipoMoneda === "dolares" ? "check-selected" : ""
        }`}
      >
        <input
          className="form-check-input"
          type="radio"
          name="inlineRadioOptions"
          id="monedaDolares"
          value="dolares"
          checked={selectedTipoMoneda === "dolares"}
          onChange={handleChangeTipoMoneda}
          disabled
        />
        <label className="form-check-label" htmlFor="monedaDolares">
          <IconoDolares size={"1.5rem"} color={"#156CFF"} /> Dólares
        </label>
      </div>
                        )}
                        
                        {Object.keys(TiposMoneda).length > 0 && (
                          <div
        className={`check-group form-check-inline ${
          selectedTipoMoneda === "empresa" ? "check-selected" : ""
        }`}
      >
        <input
          className="form-check-input"
          type="radio"
          name="tipoMoneda"
          id="tipoMoneda"
          value={"empresa"}
          checked={selectedTipoMoneda === "empresa"}
          onChange={handleChangeTipoMoneda}
          disabled
        />
        <label className="form-check-label" htmlFor="tipoMoneda">
          <IconoMoneda size={"1.5rem"} color={"#156CFF"} /> {TiposMoneda.str_Moneda}
        </label>
      </div>
                        )}
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Valoración de aportes del consorcio </h5>
                  </div>
                  {asociados.map((asociado, index) => (
                    <div className="inputs-crear-solicitud" key={index}>
                      <div className="div-input-crear-solicitud">
                        <label className="form-label">
                          Aporte Asociado {index + 1} (Valor):
                        </label>
                        <input
                          type="number"
                          onKeyDown={(e) => {
                            if (["e", "E", "+", "-","."].includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          onWheel={(e) => e.target.blur()}
                          className="form-control"
                          name="str_ValorAporte"
                          value={asociado.str_ValorAporte}
                          onChange={(e) => handleInputChangeAsociados(index, e)}
                          readOnly
                        />
                      </div>
                      <div className="div-input-crear-solicitud">
                        <label className="form-label">
                          Aporte Asociado {index + 1} (%):
                        </label>
                        <input
                          type="number"
                          onKeyDown={(e) => {
                            if (["e", "E", "+", "-","."].includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          onWheel={(e) => e.target.blur()}
                          className="form-control"
                          name="str_PorcentajeAporte"
                          value={asociado.str_PorcentajeAporte}
                          onChange={(e) => handleInputChangeAsociados(index, e)}
                          readOnly
                        />
                      </div>
                    </div>
                  ))}
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Valoración de Servicios contratados via consorcio </h5>
                  </div>
                  {asociados
                    .reduce((rows, asociado, index) => {
                      if (index % 2 === 0) {
                        rows.push([]);
                      }
                      rows[rows.length - 1].push(asociado);
                      return rows;
                    }, [])
                    .map((fila, filaIndex) => (
                      <div
                        className="row"
                        key={filaIndex}
                        style={{
                          display: "flex",
                          justifyContent: "left",
                          gap: "80px",
                        }}
                      >
                        {fila.map((asociado, inputIndex) => (
                          <div
                            className="div-input-crear-solicitud"
                            key={inputIndex}
                          >
                            <label className="form-label">
                              Servicios Asociado {filaIndex * 2 + inputIndex + 1}{" "}
                              :
                            </label>
                            <input
                              type="text"
                              className="form-control"
                              name="str_ValorServicios"
                              value={asociado.str_ValorServicios}
                              onChange={(e) =>
                                handleInputChangeAsociados(
                                  filaIndex * 2 + inputIndex,
                                  e
                                )
                              }
                              readOnly
                            />
                          </div>
                        ))}
                      </div>
                    ))}
      
                  {asociados
                    .reduce((rows, asociado, index) => {
                      if (index % 2 === 0) {
                        rows.push([]);
                      }
                      rows[rows.length - 1].push(asociado);
                      return rows;
                    }, [])
                    .map((fila, filaIndex) => (
                      <div
                        className="row"
                        key={filaIndex}
                        style={{
                          display: "flex",
                          justifyContent: "left",
                          gap: "80px",
                        }}
                      >
                        {fila.map((asociado, inputIndex) => (
                          <div
                            className="div-input-crear-solicitud"
                            key={inputIndex}
                          >
                            <label className="form-label">
                              Honorarios Asociado{" "}
                              {filaIndex * 2 + inputIndex + 1} (Valor):
                            </label>
                            <input
                              type="number"
                              onKeyDown={(e) => {
                                if (["e", "E", "+", "-","."].includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                              onWheel={(e) => e.target.blur()}
                              className="form-control"
                              name="str_ValorHonorarios"
                              value={asociado.str_ValorHonorarios}
                              onChange={(e) =>
                                handleInputChangeAsociados(
                                  filaIndex * 2 + inputIndex,
                                  e
                                )
                              }
                              readOnly
                            />
                          </div>
                        ))}
                      </div>
                    ))}
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Inclusión de Clausulas</h5>
                  </div>
                  {renderClausulas()}
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(1)}
                >
                  Condiciones del Servicio
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Asociación a cuenta del cliente
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Interlocutor"
                        value={formDataClienteAsociado.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Documento"
                        value={formDataClienteAsociado.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value);
                            }
                          }
                        }}
                        readOnly
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Razón Social:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_RazonSocial"
                        value={formDataClienteAsociado.str_RazonSocial}
                        onChange={handleInputChange}
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Asociación a contrato del cliente</h5>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Codigo de Contrato:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_InfoCompartida"
                        value={formDataContenido.str_InfoCompartida}
                        onChange={handleInputChange}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        newFiles={newFiles}
        esEdicion={true}
        ver={true}
        gestor={true}
        handleDownload={handleDownload}
        documentoSubido={documentoSubido}
      />
    </div>
  );
};

export default Consorcio;
