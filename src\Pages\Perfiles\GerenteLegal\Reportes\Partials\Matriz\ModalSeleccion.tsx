import { useState } from "react";

const ModalSeleccion = ({
 
  handleAnioChange,
  anios,
  aniosSeleccionados,
  fields,
  selectedFields,
  handleFieldChange,
  fetchSolicitudes,
  handleBorrarFiltro,
  handleInputChange,
  codSolicitud
}) => {

  return (
    <div className="selector-campos-matriz">

    <div className="titulo-modal-filtros">
      <span className="text-titulo-modal-filtros lato-font-400 size-titulos">
        Seleccionar Campos
      </span>
      <span className="borrarTodo-modal-filtros montserrat-font-500" onClick={handleBorrarFiltro}>
        Borrar Todo
      </span>
    </div>
    <div className="div-filtros-matriz montserrat-font ">
      <select
        className="form-select select-matriz"
        aria-label="Default select example"
        value={aniosSeleccionados.matriz}
        onChange={handleAnioChange}
        name="matriz"
      >
        <option disabled>Año</option>
        {anios.map((anio) => (
          <option key={anio} value={anio}>
            {anio}
          </option>
        ))}
      </select>
      <input type="text" name="" id="" placeholder="Cod-Solicitud" className="form-control" value={codSolicitud} onChange={handleInputChange} maxLength={19}/>
    </div>
    <div className="campos-seleccion montserrat-font ">
      <div className="row">
        <div className="col">
          {fields.slice(0, Math.ceil(fields.length / 2)).map((field) => (
            <div className="form-check montserrat-font " key={field}>
              <input
                type="checkbox"
                className="form-check-input"
                id={field}
                value={field}
                checked={selectedFields.includes(field)}
                onChange={() => handleFieldChange(field)}
              />
              <label className="form-check-label" htmlFor={field}>{field}</label>
            </div>
          ))}
        </div>
        <div className="col">
          {fields.slice(Math.ceil(fields.length / 2)).map((field) => (
            <div className="form-check" key={field}>
              <input
                type="checkbox"
                className="form-check-input montserrat-font "
                id={field}
                value={field}
                checked={selectedFields.includes(field)}
                onChange={() => handleFieldChange(field)}
              />
              <label className="form-check-label" htmlFor={field}>{field}</label>
            </div>
          ))}
        </div>
      </div>
    </div>
    <div className="div-botones-matriz">
      <button className="btn-asignar" onClick={() => { fetchSolicitudes();}}>Mostrar</button>
    </div>
  </div>
  );
};

export default ModalSeleccion;
