import axios from 'axios';
import React, { useEffect, useState } from 'react';
import API_GESTOR from '../../../../../../assets/Api/ApisGestor';
import Cookies from "js-cookie";
import { validateToken } from '../../../../../Components/Services/TokenService';
import IconoLeft from '../../../../../../assets/SVG/IconoLeft';
import IconoRight from '../../../../../../assets/SVG/IconoRight';

const anioActual = new Date().getFullYear();

const ListaContratos = ({ Suscripcion, idUsuario,formatDate }) => {
  const [aniosSeleccionados, setAniosSeleccionados] = useState(anioActual);
  const [solicitudes, setSolicitudes] = useState([]);
  const token = Cookies.get("Token");


  const [currentPage, setCurrentPage] = useState(1);
  const solicitudesPorPagina = 8;
  const indiceUltimaSolicitud = currentPage * solicitudesPorPagina;
  const indicePrimeraSolicitud = indiceUltimaSolicitud - solicitudesPorPagina;
  const solicitudesActuales = solicitudes.slice(
    indicePrimeraSolicitud,
    indiceUltimaSolicitud
  );
  const handlePageChange = (pageNumber: React.SetStateAction<number>) => {
    setCurrentPage(pageNumber);
  };
  const totalPaginas = Math.ceil(solicitudes.length / solicitudesPorPagina);

  const renderNumeritosPaginacion = () => {
    const numeritos = [];
    for (let i = 1; i <= totalPaginas; i++) {
      numeritos.push(
        <button
          key={i}
          className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }
    return numeritos;
  };
  const fetchSolicitudes = async () => {
await validateToken();
    if (aniosSeleccionados) {
      const response = await axios.get(API_GESTOR["SolicitudesxFinalizar"](Suscripcion, aniosSeleccionados, idUsuario),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      setSolicitudes(response.data);
    }
  };
  const handleAnioChange = (e) => {
    setAniosSeleccionados(e.target.value); 
  };
  const anios = Array.from({ length: 21 }, (_, i) => 2010 + i);
  useEffect(() => {
    fetchSolicitudes()
  }, [aniosSeleccionados])
  
  return (
    <div className="div-tabla-solicitudes-finalizar">
            <div className="div-boton-descargar-csv">
            <select
              className="form-select select-anio_finSolicitudes"
              aria-label="Default select example"
              value={aniosSeleccionados}
              onChange={handleAnioChange}
              name="aniosSeleccionados"
            >
              <option disabled>Año</option>
              {anios.map((anio) => (
                <option key={anio} value={anio}>
                  {anio}
                </option>
              ))}
            </select>
      </div>
    <table className="tabla-inicio-solicitante">
      <thead>
        <tr>
          <th>Num. Solicitud</th>
          <th>Cliente</th>
          <th>Fech. Registro</th>
          <th>Fech. Firma</th>
          <th>Fecha Finalizar</th>
          <th>Empresa</th>
          <th>Monto del contrato</th>
        </tr>
      </thead>
      <tbody>
        {solicitudesActuales.length === 0 ? (
          <tr>
            <td colSpan={7} style={{ textAlign: "center" }}>
              No se encontraron registros
            </td>
          </tr>
        ) : (
          solicitudesActuales.map((solicitud, index) => (
            <tr
              key={index}
            >
              <td>{solicitud["Cod.Solicitud"]}</td>
                <td>{solicitud["Cliente"]}</td>
              <td>{formatDate(solicitud["Fecha Registro"])}</td>
              <td>{formatDate(solicitud["Fecha Firma"])}</td>
              <td className="colum-empresa-tabla-solicitante">
   
                  {formatDate(solicitud["Fecha Fin"])}
              </td>
              <td>{solicitud["Empresa"]}$</td>
              <td>{solicitud["Honorarios"]}$</td>

            </tr>
          ))
        )}
      </tbody>
      <tfoot>
        <tr>
          <td colSpan={7}>
            <div className="paginacion-tabla-solicitante">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="numero-pagina"
              >
                                      <IconoLeft  size={"1.3rem"} color={"#000"}/>

              </button>
              {renderNumeritosPaginacion()}
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={indiceUltimaSolicitud >= solicitudes.length}
                className="numero-pagina"
              >
                <IconoRight size={"1.5rem"} color={"#000"}/>
              </button>
            </div>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
  );
};

export default ListaContratos;