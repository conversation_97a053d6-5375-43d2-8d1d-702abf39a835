import React, { useEffect, useState } from "react";
import { <PERSON>, Step, Step<PERSON>abe<PERSON>, <PERSON>per, TextField } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import axios from "axios";
import Select from "react-select";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import BarraLateralCrearSolicitud from "../../BarraLateral/BarraLateralCrearSolicitud";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import IconoBasureroEliminar from "../../../../../../assets/SVG/IconoBasureroEliminar";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import getLogs from "../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
interface CrearConsorcioProps {
  Nombres: string;
  Apellidos: string;
  UsuarioId: number;
  idAplicacion: number;
  Suscriptor: string;
  Suscripcion: string;
  idTipoSolicitud: number;
  tipoModelo: string;
}

interface FormDataSolicitud {
  int_idUsuarioModificacion: number | string;
  str_idSuscriptor: string;
  int_idEmpresa: string;
  int_idUnidadNegocio: string;
  int_idTipoSol: number;
  int_SolicitudGuardada: null | number;
  str_DeTerceros: string;
  dt_FechaEsperada: string;
  db_Honorarios: number;
}

interface FormDataContenido {
  str_DocAdjuntos: string;
  str_ObjetivoContrato: string;
  str_ObligacionesConjuntas: string;
  str_TipoServicio: string;
  str_InfoAdicional: string;
  int_idInterlocutor: number | null;
  int_idInterlocutorComprador: number | null;
  str_idSuscriptor: string;
  str_PlazoSolicitud: string;
  str_RentaPactada: string;
  str_Moneda: string;
}

const EditarConsorcio: React.FC<CrearConsorcioProps> = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  esEdicion,
  ver,
  NomTipoSolicitud
}) => {
  const [files, setFiles] = useState([]);
  const [newFiles, setNewFiles] = useState([]);
  const token = Cookies.get("Token");
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [unidadesNegocios, setUnidadesNegocios] = useState([]);
  const [selectedUnidadesNegocios, setSelectedUnidadesNegocios] = useState(
    selectedSolicitud.int_idUnidadNegocio
  );
  const [selectedEmpresa, setSelectedEmpresa] = useState(
    selectedSolicitud.int_idEmpresa
  );
  const [errors, setErrors] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [idContenido, setidContenido] = useState(null);
  const [FechaMinima , setFechaMinima] = useState(getLocalDate());

  const navigate = useNavigate();
  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };
  const [asociados, setAsociados] = useState([createInitialFormData()]);

  function createInitialFormData() {
    return {
      str_TipoDoc: "Documento de identidad personal",
      str_RLTipoDocumento: "",
      str_Documento: "",
      str_RLDocumento: "",
      str_Interlocutor: "",
      str_RepLegal: "",
      str_Domicilio: "",
      int_RLPartida: "",
      str_Obligacion: "",
      int_idInterlocutor: "",
      str_idSuscripcion: Suscripcion,
    };
  }

  const handleAddAsociado = () => {
    setAsociados([...asociados, createInitialFormData()]);
  };
  const handleRemoveAsociado = (index) => {
    if (asociados.length === 1) {
      Swal.fire(
        "",
        "No se puede eliminar el único asociado existente",
        "error"
      );
      return;
    }
    setAsociados(asociados.filter((_, i) => i !== index));
  };
  const [formDataSolicitud, setFormDataSolicitud] = useState<FormDataSolicitud>(
    {
      int_idUsuarioModificacion: UsuarioId,
      int_idEmpresa: selectedEmpresa,
      int_idUnidadNegocio: selectedUnidadesNegocios,
      int_idTipoSol: selectedSolicitud.int_idTipoSol,
      int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
      str_DeTerceros: selectedSolicitud.str_DeTerceros,
      dt_FechaEsperada:
        selectedSolicitud.dt_FechaEsperada ||
        new Date().toISOString().split("T")[0],
      db_Honorarios: selectedSolicitud.db_Honorarios,
      str_idSuscriptor: Suscripcion,
    }
  );
  const [formDataContenido, setFormDataContenido] = useState<FormDataContenido>(
    {
      str_DocAdjuntos: files.length >= 1 ? "si" : "no",
      str_ObjetivoContrato: "",
      str_ObligacionesConjuntas: "",
      str_TipoServicio: "",
      str_InfoAdicional: "",
      int_idInterlocutor: null,
      int_idInterlocutorComprador: null,
      str_idSuscriptor: Suscripcion,
      str_RentaPactada: "",
      str_Moneda: "",
    }
  );

  const validateForm = (): boolean => {
    let newErrors: { [key: string]: string } = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.str_ObligacionesConjuntas)
      newErrors.str_ObligacionesConjuntas =
        "Las obligaciones conjuntas son requeridas";
    if (!formDataContenido.str_ObligacionesConjuntas)
      newErrors.str_ObligacionesConjuntas =
        "Las obligaciones conjuntas son requeridas";
    if (!formDataContenido.str_PlazoSolicitud)
      newErrors.str_PlazoSolicitud = "El plazo del consorcio es requerido";
    
    asociados.forEach((asociado, index) => {
      if (!asociado.str_TipoDoc) {
        newErrors[`asociado_${index}_str_TipoDoc`] =
          "El tipo de documento es requerido";
      }
      if (!asociado.str_Documento) {
        newErrors[`asociado_${index}_str_Documento`] =
          "El documento es requerido";
      }
      if (!asociado.str_Interlocutor) {
        newErrors[`asociado_${index}_str_Interlocutor`] =
          "El asociado es requerido";
      }
      if (!asociado.str_RepLegal) {
        newErrors[`asociado_${index}_str_RepLegal`] =
          "El representante legal es requerido";
      }
      if (!asociado.str_Domicilio) {
        newErrors[`asociado_${index}_str_Domicilio`] =
          "El domicilio es requerido";
      }
      if (!asociado.str_RLTipoDocumento) {
        newErrors[`asociado_${index}_str_RLTipoDocumento`] =
          "El tipo de documento  es requerido";
      }
      if (!asociado.int_RLPartida) {
        newErrors[`asociado_${index}_int_RLPartida`] =
          "La partida registral  es requerido";
      }
      if (!asociado.str_RLDocumento) {
        newErrors[`asociado_${index}_str_RLDocumento`] =
          "El representante legal es requerido";
      }
      if (!asociado.str_Obligacion) {
        newErrors[`asociado_${index}_str_Obligacion`] =
          "La obligación del asociado es requerido  es requerido";
      }
      if (!asociado.int_RLPartida) {
        newErrors[`asociado_${index}_int_RLPartida`] =
          "El representante legal es requerido";
      }

    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const steps = ["DATOS GENERALES", "DATOS DE CONTRATO"];
  const handleChangeEmpresa = (selectedOption: { value: string } | null) => {
    if (selectedOption) {
      setSelectedEmpresa(selectedOption.value);
    }
  };

  const handleChangeUnidadesNegocios = (
    selectedOption: { value: string } | null
  ) => {
    if (selectedOption) {
      setSelectedUnidadesNegocios(selectedOption.value);
    }
  };
  useEffect(() => {
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      int_idEmpresa: selectedEmpresa,
      int_idUnidadNegocio: selectedUnidadesNegocios,
    }));
  }, [selectedEmpresa, selectedUnidadesNegocios, formDataContenido]);
  const fetchArchivos = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["ListarArchivosEditar"](
          Suscripcion,
          selectedSolicitud.int_idSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setFiles(response.data);
    } catch (error) {
      console.error("Error al obtener las empresas:", error);
    }
  };
  const buscarAsociado = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      return response.data; // Retorna los datos del interlocutor
    } catch (error) {
      
      return null; // Retorna null si hay un error
    }
  };

  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            str_PlazoSolicitud: response.data.str_PlazoSolicitud,
            str_InfoAdicional: response.data.str_InfoAdicional,
            int_idInterlocutor: response.data.int_idInterlocutor,
            int_idInterlocutorComprador:
              response.data.int_idInterlocutorComprador,
            str_BienDescripcion: response.data.str_BienDescripcion,
            str_BienUso: response.data.str_BienUso,
            str_BienDireccion: response.data.str_BienDireccion,
            str_RentaPactada: response.data.str_RentaPactada,
            str_BienMuebleInmueble: response.data.str_BienMuebleInmueble,
            str_Moneda: response.data.str_Moneda,
            str_ObligacionesConjuntas: response.data.str_ObligacionesConjuntas,
          }));
          setidContenido(response.data.int_idSolicitudCont);
          const responseConsorcio = await axios.get(
            API_SOLICITANTE["ObtenerDatosConsorcio"](
              response.data.int_idSolicitudCont
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseConsorcio.data) {
             // Log de documentos a procesar
            const nuevosAsociados = await Promise.all(
              responseConsorcio.data.map(async (data) => {
                try {
                  
                  const interlocutorData = await buscarAsociado(
                    data.str_Documento
                  );
                  
                  if (interlocutorData) {
                    
                    return {
                      str_TipoDoc: interlocutorData.str_TipoDoc || "Documento de identidad personal",
                      str_RLTipoDocumento:
                        interlocutorData.str_RLTipoDocumento || "Documento de identidad personal",
                      str_Documento: data.str_Documento,
                      str_RLDocumento: interlocutorData.str_RLDocumento || "",
                      str_Interlocutor: interlocutorData.str_Interlocutor || "",
                      str_RepLegal: interlocutorData.str_RepLegal || "",
                      str_Domicilio: interlocutorData.str_Domicilio || "",
                      int_RLPartida: interlocutorData.int_RLPartida || "",
                      int_idInterlocutor:
                        interlocutorData.int_idInterlocutor || "",
                      str_Obligacion: data.str_Obligacion || "",
                    };
                  } else {
                    console.warn(
                      "No se encontraron datos del interlocutor para:",
                      data.str_Documento
                    );
                    return null; // Retornamos null si no hay datos
                  }
                } catch (error) {
                  
                  return null; // Retornamos null en caso de error
                }
              })
            );

            // Filtramos los valores nulos
            const filteredAsociados = nuevosAsociados.filter(Boolean);
             // Log de nuevos asociados

            // Limpia el estado antes de añadir nuevos asociados
            setAsociados(filteredAsociados); // Reemplaza el estado con los nuevos asociados
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_InfoAdicional: "",
            str_ObligacionesConjuntas: "",
          }));
        }
        
      } catch (error) {
        
      }
    };

    const fetchEmpresas = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEmpresas"](idAplicacion, Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
            try {
              const response = await axios.get(
                API_GESTOR["ObtenerTiempoRespuestaTS"](Suscripcion,selectedSolicitud.int_idTipoSol,selectedEmpresa),{
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                  }
                }
              );
              const responseData = response.data;
              const ultimoObjeto = responseData.length > 0 ? responseData[responseData.length - 1] : null;
      
              const nuevaFechaEsperada = addDaysToDate(new Date(), ultimoObjeto.int_TiempoRespuesta ? ultimoObjeto.int_TiempoRespuesta : 0);
              setFormDataSolicitud(prevState => ({
                ...prevState,
                dt_FechaEsperada: nuevaFechaEsperada,
              }));
              setFechaMinima(nuevaFechaEsperada);

            } catch (error) {
              console.error("Error al obtener tipos de solicitud:", error);
            }
          };
    ObtenerTiempoRespuestaTipoSolicitud();
    fetchEmpresas();
    DatosContenidoSolicitud();
    fetchArchivos();
  }, []);
  useEffect(() => {
    const fetchUnidadesNegocios = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerUnidadesNegocios"](Suscripcion,selectedEmpresa),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idUnidadesNegocio: any; str_Descripcion: any }) => ({
            value: tipo.int_idUnidadesNegocio,
            label: tipo.str_Descripcion,
          })
        );
        setUnidadesNegocios(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    fetchUnidadesNegocios();

  } , [selectedEmpresa]);
  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setNewFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };

  const EliminarArchivo = async (file) => {
    await validateToken();
    try {
      const response = await axios.delete(
        API_SOLICITANTE["EliminarArchivo"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          file.nombre_archivo,
          file.int_idArchivos,
          file.tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        fetchArchivos();
      }
    } catch (error) {
      console.error("Error al eliminar la solicitud:", error);
    }
  };
  const handleFileRemove = (index, isStoredFile, file) => {
    if (isStoredFile) {
      EliminarArchivo(file);
    } else {
      setNewFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      dt_FechaEsperada: `${selectedDate}T00:00:00`,
    }));
  };
  const handleInputChangeAsociados = (index, event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    const newAsociados = [...asociados];
    newAsociados[index][name] = value;

    if (name.startsWith("str_TipoDoc")) {
        newAsociados[index].str_TipoDoc = value;
    }
    if (name.startsWith("str_RLTipoDocumento")) {
        newAsociados[index].str_RLTipoDocumento = value;
    }

     if (name === "str_Documento") {
        const dniCount = newAsociados.filter((asociado, i) => 
            i !== index && asociado.str_Documento === value 
        ).length;

        setErrors((prevErrors) => {
            const newErrors = { ...prevErrors };
            if (dniCount > 0) {
                newErrors[`asociado_${index}_str_Documento`] = "Este asociado ya está registrado";
                Swal.fire("", "Este asociado ya está registrado", "error");
                 setTimeout(() => {
                  delete newErrors[`asociado_${index}_str_Documento`];
                  handleRemoveAsociado(index);
                }
                , 1500);
              } else {
                delete newErrors[`asociado_${index}_str_Documento`];
            }
            return newErrors;
        });
    }

    setAsociados(newAsociados);
};
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string, index: number) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      if (response.data) {
        // Actualiza solo el asociado correspondiente
        const newAsociados = [...asociados];
        newAsociados[index] = {
          ...newAsociados[index],
          str_Interlocutor: response.data.str_Interlocutor,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio,
          str_RLDocumento: response.data.str_RLDocumento,
          int_idInterlocutor: response.data.int_idInterlocutor,
        };
        setAsociados(newAsociados);
      } else {
        // Manejo si no se encuentra el interlocutor
      }
    } catch (error) {
      
    }
  };
  const resetInterlocutor = (index: number) => {
    const newAsociados = [...asociados];
    newAsociados[index] = {
      ...newAsociados[index],
      str_Interlocutor: "",
      str_Correo: "",
      str_RepLegal: "",
      int_RLPartida: "",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Domicilio: "",
      str_RLDocumento: "",
      str_TipoDoc: "Documento de identidad personal",
    };
    setAsociados(newAsociados);
  };
  const obtenerGestorConMenorSolicitudes = async () => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["ObtenerGestoresTipoSolicitud"](
          Suscripcion,
          selectedSolicitud.int_idTipoSol,
          selectedEmpresa
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = response.data;

      if (!gestores || gestores.length === 0) {
        
        return null;
      }

      // Obtener el número de solicitudes activas para cada gestor
      const solicitudesPorGestor = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            const contadorResponse = await axios.get(
             API_SOLICITANTE["ContadorPorGestor"](gestor.int_idGestor,Suscripcion), {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            const solicitudes_Activas = contadorResponse.data.total_no_firmado;

            

            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas,
            };
          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idGestor}:`,
              error
            );
            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas: Infinity, // Asignar un valor alto para evitar seleccionarlo
            };
          }
        })
      );

      // Encontrar el gestor con menor número de solicitudes activas
      const gestorConMenorSolicitudes = solicitudesPorGestor.reduce(
        (minGestor, currentGestor) => {
          return currentGestor.solicitudes_Activas <
            minGestor.solicitudes_Activas
            ? currentGestor
            : minGestor;
        },
        { int_idGestor: null, solicitudes_Activas: Infinity }
      );

      

      return gestorConMenorSolicitudes.int_idGestor;
    } catch (error) {
      
      return null;
    }
  };
  const handleAsignarGestor = async ( gestorSeleccionado) => {
    await validateToken();
    try {
      const response = await axios.put(
        API_SOLICITANTE["AsignarGestor"](),
        {
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
          int_idGestor: gestorSeleccionado,
          int_idUsuarioModificacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        console.error(`Gestor Asignado Correctamente ${response}`);
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }
    } catch (error) {
      return error;
    }
  };
  const EstadoNuevo = async ( estado) => {
    await validateToken();
    try {
      await axios.put(API_SOLICITANTE["ActualizarEstado"](), {
        str_idSuscriptor: Suscripcion,
        nombre_estado: estado,
        int_idUsuarioCreacion: UsuarioId,
        int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
    }
  };
  const handleSubmitGuardarSolicitud = async () => {
    await validateToken();
    if (!selectedEmpresa || !selectedUnidadesNegocios) {
      Swal.fire(
        "Error",
        "Debe seleccionar una empresa y una unidad de negocio para registrar",
        "error"
      );
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 1,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitConfirmarSolicitud = async () => {
    await validateToken();
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop(); 
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 0,
      };

      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitFiles = async () => {
    await validateToken();
    try {
      for (const file of newFiles) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          selectedSolicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          selectedSolicitud.int_idSolicitudes
        );
                     // Enviar el tipo_adjunto como campo separado
        if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_SOLICITANTE["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }

        
      }

      
    } catch (error) {
      
    }
  };
  const handleSubmitContenidoSolicitud = async () => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarContenidoSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        await handleSubmitInterlocutores(idContenido);
      }
      
    } catch (error) {
      return error
    }
  };
  const handleSubmitInterlocutores = async (idContenidoSolicitud: number) => {
    await validateToken();
    const response = await axios.delete(
      API_SOLICITANTE["EliminarAsociados"](idContenidoSolicitud),
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.status >= 200 && response.status < 300) {
      try {
        for (const asociado of asociados) {
          console.log(asociado.int_idInterlocutor);

          if (asociado.int_idInterlocutor) {
            const formDataModificado = {
              ...asociado,
              int_idUsuarioModificacion: UsuarioId,
            };
            delete formDataModificado.int_idUsuarioCreacion;

            const response = await axios.put(
              API_SOLICITANTE["ActualizarInterlocutor"](
                asociado.int_idInterlocutor
              ),
              formDataModificado,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.status !== 200) {
              throw new Error("No se pudo actualizar el interlocutor");
            }
            await handleCrearConsorcio(
              asociado.int_idInterlocutor,
              asociado.str_Obligacion,
              idContenidoSolicitud
            );
            
          } else {
            const formDataNuevo = {
              ...asociado,
              int_idUsuarioCreacion: UsuarioId,
            };

            const response = await axios.post(
              API_SOLICITANTE["AgregarInterlocutor"](),
              formDataNuevo,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            await handleCrearConsorcio(
              response.data.int_idInterlocutor,
              asociado.str_Obligacion,
              idContenidoSolicitud
            );
            if (response.status !== 201) {
              throw new Error("No se pudo ingresar el interlocutor");
            }
            asociado.int_idInterlocutor = response.data.int_idInterlocutor;
            
          }
        }
      } catch (error) {
        
      }
    }
  };
  const handleCrearConsorcio = async (
    idAsociado: number | string,
    obligacion: string,
    idContenidoSolicitud: number
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        int_idInterlocutor: idAsociado,
        str_Obligacion: obligacion,
        int_idUsuarioCreacion: UsuarioId,
      };

      const response = await axios.post(
        API_SOLICITANTE["AgregarConsorcio"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        handlerConsorcioSolicitud(response.data.id, idContenidoSolicitud);
      }

      
    } catch (error) {
      return error
    }
  };
  const handlerConsorcioSolicitud = async (
    idConsorcio: number,
    idContenidoSolicitud: number
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        int_idConsorcio: idConsorcio,
        int_idSolicitudCont: idContenidoSolicitud,
        int_idUsuarioCreacion: UsuarioId,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarConsorcioSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        
      }
      
    } catch (error) {
      return error
    }
  };
  const isInvalidLength = (type, value, requiredLength) =>
    type === "RUC" && value.length < requiredLength && value.length > 0;
  
  const isInvalidDNILength = (type, value, requiredLength) =>
    type === "DNI" && value.length < requiredLength && value.length > 0;
  
  const handleSubmitSolicitudGuardar =async () => {
     
    handleSubmitGuardarSolicitud();
    handleSubmitContenidoSolicitud();
    handleSubmitFiles();
    await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
    Swal.fire("", "Solicitud Guardada ", "success");
    navigate(RoutesPrivate.INICIOSOLICITANTE);
  };
  const handleSubmitSolicitudConfirmar = async() => {
     
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop(); 
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }
    if(asociados.length < 2){
      Swal.fire("", "No pueden haber menos de 2 asociados en la solicitud", "error");
      return
    }
    handleSubmitConfirmarSolicitud();
    handleSubmitContenidoSolicitud();
    const idGestorAsignado = await obtenerGestorConMenorSolicitudes();
          if (!idGestorAsignado) {
            Swal.fire(
              "Error",
              "No hay gestores disponibles para asignar la solicitud",
              "error"
            );
            return;
          }
    await handleAsignarGestor(idGestorAsignado);
    await EstadoNuevo("Asignado")
    if (newFiles.length >= 1) {
    handleSubmitFiles();
    
    }
    await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
    Swal.fire("Confirmado", "Solicitud Guardada Correctamente ", "success");
    navigate(RoutesPrivate.INICIOSOLICITANTE);
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={selectedSolicitud.nombre_completo}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <Select
                        options={empresasFiltro}
                        value={empresasFiltro.find(
                          (option) => option.value === selectedEmpresa
                        )}
                        onChange={handleChangeEmpresa}
                        placeholder="Empresa"
                        isDisabled={ver}
                      />
                      {errors.int_idEmpresa && (
                        <span className="error-message">
                          {errors.int_idEmpresa}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <Select
                        options={unidadesNegocios}
                        value={unidadesNegocios.find(
                          (option) => option.value === selectedUnidadesNegocios
                        )}
                        onChange={handleChangeUnidadesNegocios}
                        placeholder="Unidad de Negocio"
                        isDisabled={ver}
                      />
                      {errors.int_idUnidadNegocio && (
                        <span className="error-message">
                          {errors.int_idUnidadNegocio}
                        </span>
                      )}
                    </div>
                  </div>
                  {asociados.map((asociado, index) => (
                    <div key={index}>
                      <hr className="separador-linea" />

                      <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Datos del Asociado {index + 1}</h5>
                        <div className="div-eliminarAsociado">
                          <div
                            className="eliminar-asociado"
                            onClick={() => handleRemoveAsociado(index)}
                          >
                            <IconoBasureroEliminar
                              size={"1rem"}
                              color={"#294FCF"}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                      <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name={`str_TipoDoc_${index}`} id="tipoDocumento" className="form-select" onChange={(event)=>handleInputChangeAsociados(index,event)} value={asociado.str_TipoDoc || ""} >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name={`str_RLTipoDocumento${index}`} id="tipoDocumento" className="form-select" onChange={( )=>handleInputChangeAsociados(index,event)} value={asociado.str_RLTipoDocumento || ""} >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <input
                            type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                            onWheel={(e) => e.target.blur()}
                            className={`form-control ${errors[`asociado_${index}_str_Documento`] && "is-invalid"}`}
                            name="str_Documento"
                            value={asociado.str_Documento}
                            onChange={(e) => {
                              const maxLength =  15;
                              const value = e.target.value;
                            
                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index,e);
                            
                                if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value,index);
                            }

                              }
                            }}
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <input
                            type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                            onWheel={(e) => e.target.blur()}
                            name="str_RLDocumento"
                            className={`form-control ${errors[`asociado_${index}_str_RLDocumento`] && "is-invalid"}`}
                            value={asociado.str_RLDocumento}
                            placeholder=""
                            onChange={(e) => {
                              const maxLength =  15;
                              const value = e.target.value;
                            
                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index,e);
                            
                                 
 
                              }
                            }}
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Asociado {index + 1}:
                          </label>
                          <input
                            type="text"
                            className={`form-control ${errors[`asociado_${index}_str_Interlocutor`] && "is-invalid"}`}
                            name="str_Interlocutor"
                            value={asociado.str_Interlocutor}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Representante Legal:
                          </label>
                          <input
                            type="text"
                            name="str_RepLegal"
                            className={`form-control ${errors[`asociado_${index}_str_RepLegal`] && "is-invalid"}`}
                            value={asociado.str_RepLegal}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">Domicilio:</label>
                          <input
                            type="text"
                            className={`form-control ${errors[`asociado_${index}_str_Domicilio`] && "is-invalid"}`}
                            name="str_Domicilio"
                            value={asociado.str_Domicilio}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Partida Registral :{" "}
                          </label>
                          <input
                            type="text"
                            name="int_RLPartida"
                            className={`form-control ${errors[`asociado_${index}_int_RLPartida`] && "is-invalid"}`}
                            value={asociado.int_RLPartida}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Obligaciones del Asociado {index + 1}
                          </label>
                          <textarea
                            className={`form-control ${errors[`asociado_${index}_str_Obligacion`] && "is-invalid"}`}
                            id=""
                            value={asociado.str_Obligacion}
                            name="str_Obligacion"
                            rows={3}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="boton-crear-asociado">
                    <button
                      type="button"
                      className="btn-asignar"
                      onClick={handleAddAsociado}
                    >
                      Agregar Asociado
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font 
                    collapsed
                  `}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Datos del contrato
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Plazo del consorcio:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_PlazoSolicitud && "is-invalid"}`}
                        name="str_PlazoSolicitud"
                        value={formDataContenido.str_PlazoSolicitud}
                        onChange={handleInputChange}
                      />
                  
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Objeto del Consorcio
                      </label>
                      <textarea
                        className={`form-control ${errors.str_ObjetivoContrato && "is-invalid"}`}
                        id=""
                        value={formDataContenido.str_ObjetivoContrato}
                        name="str_ObjetivoContrato"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>
                  
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Obligaciones Conjuntas:
                      </label>
                      <textarea
                        className={`form-control ${errors.str_ObligacionesConjuntas && "is-invalid"}`}
                        id=""
                        value={formDataContenido.str_ObligacionesConjuntas}
                        name="str_ObligacionesConjuntas"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Condiciones del Servicio
                      </label>
                      <textarea
                        className="form-control"
                        id=""
                        value={formDataContenido.str_InfoAdicional}
                        name="str_InfoAdicional"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>
                      {errors.str_InfoAdicional && (
                        <span className="error-message">
                          {errors.str_InfoAdicional}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha Esperada:{" "}
                      </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        min={FechaMinima}
                        className="form-control"
                        value={
                            formDataSolicitud.dt_FechaEsperada
                              ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                              : ""
                          }
                        onChange={handleDateChange}
                        name="dt_FechaEsperada"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        esEdicion={esEdicion}
        newFiles={newFiles}
        ver={ver}
             NomTipoSolicitud={NomTipoSolicitud}
      />
    </div>
  );
};

export default EditarConsorcio;
