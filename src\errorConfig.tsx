import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import Header from './Pages/Components/Partials/Header/Header';
import IconoLogout from './assets/SVG/IconoLogout';
import fotoUsuario from "./assets/Img/300-7.jpg";
import { logout } from './Pages/Components/Services/TokenService';
import logoPrisma from "./assets/Img/prisma_200x78.png";
import './errorPage.css'; // Asegúrate de crear este archivo CSS

// Custom error UI component
const ErrorFallback = ({ error, resetErrorBoundary }) => {
 
  const volver = () => {
    window.location.href = '/Prisma-Contratos/';
  }
  const [showUserModal, setShowUserModal] = useState(false);


  return (
   <>
    <div className="global-header">
      <div className="container-header">
        <div className="container-logo-header">
          <img
            src={logoPrisma}
            alt="Logo Prisma"
            className="logo-header"
            onClick={volver}
          />
        </div>
        <div className="container-Usuario-header montserrat-font">
  

       
          <span>
            Prisma Legal
          </span>
          <div
            className="profile-container"
            onClick={() => setShowUserModal((showUserModal) => !showUserModal)}
            style={{ position: "relative" }}
          >
            <img
               src={
                 
                   `${fotoUsuario}`
              }
              alt=""
              className="img-perfil"
            />
         
          </div>
        </div>
      </div>
 
    </div>
    <div className="error-page-container">
      <div className="error-content">
        <div className="error-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
            <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-7v2h2v-2h-2zm0-8v6h2V7h-2z" fill="#FF5252"/>
          </svg>
        </div>
        
        <h1 className="error-title">Algo salió mal</h1>
        
        <p className="error-description">
          Ha ocurrido un error en la aplicación. Nuestro equipo ha sido notificado y estamos trabajando para solucionarlo.
        </p>
        
        <div className="error-details">
          <h3>Detalles del error:</h3>
          <div className="error-code">
            <code>{error.message}</code>
          </div>
        </div>
        
        <div className="error-actions">
          <button 
            onClick={volver}
            className="btn-volver"
          >
            <i className="fas fa-home"></i> Volver al Inicio
          </button>
          <button 
            onClick={resetErrorBoundary}
            className="btn-reintentar"
          >
            <i className="fas fa-redo"></i> Intentar de nuevo
          </button>
        </div>
      </div>
    </div>
   </>
  );
};

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  resetErrorBoundary = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      return <ErrorFallback error={this.state.error} resetErrorBoundary={this.resetErrorBoundary} />;
    }
    return this.props.children;
  }
}

// Custom error handlers for React 19
export const errorHandlers = {
  onUncaughtError: (error, errorInfo) => {
    console.error('Uncaught error:', error);
    console.error('Component stack:', errorInfo.componentStack);
  
    Swal.fire({
      title: 'Error',
      text: 'Ha ocurrido un error inesperado. Por favor, intente nuevamente.',
      icon: 'error',
      confirmButtonText: 'Entendido',
      confirmButtonColor: '#4361ee',
      customClass: {
        popup: 'error-popup-custom',
        title: 'error-title-custom',
        content: 'error-content-custom'
      }
    });
  },

  onCaughtError: (error, errorInfo) => {
    console.error('Caught error:', error);
    console.error('Component stack:', errorInfo.componentStack);
  }
};

export { ErrorBoundary, ErrorFallback };
