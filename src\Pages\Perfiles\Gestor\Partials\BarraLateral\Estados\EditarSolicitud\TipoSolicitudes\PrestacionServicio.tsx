import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>per, TextField } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import Cookies from "js-cookie";
import axios from "axios";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import BarraLateralCrearSolicitud from "../../../../../../Solicitante/CrearSolicitud/BarraLateral/BarraLateralCrearSolicitud";
import API_GESTOR from "../../../../../../../../assets/Api/ApisGestor";
import { RoutesPrivate } from "../../../../../../../../Security/Routes/ProtectedRoute";
import { validateToken } from "../../../../../../../Components/Services/TokenService";
import API_SOLICITANTE from "../../../../../../../../assets/Api/ApisSolicitante";
import IconoMoneda from "../../../../../../../../assets/SVG/IconoMoneda";
import IconoDolares from "../../../../../../../../assets/SVG/IconoSoles";
import getLogs from "../../../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");

interface Errors {
  int_idEmpresa?: string;
  int_idUnidadNegocio?: string;
  str_Documento?: string;
  str_Interlocutor?: string;
  db_Honorarios?: string;
  dt_FechaEsperada?: string;
  str_Correo?: string;
  str_RepLegal?: string;
  str_ObjetivoContrato?: string;
  db_Presupuesto?: string;
  str_PlazoSolicitud?: string;
  str_TipoServicio?: string;
  str_Margen?: string;
  str_CondicionPago?: string;
  str_plazoForzoso?: string;
  str_servicioIniciado?: string;
  db_inversion?: string;
}
interface PrestacionServicio {
  UsuarioId: number | null;
  idAplicacion: number | null;
  Suscriptor: number | null;
  Suscripcion: string | null;
  selectedSolicitud: object | null;
  Asignado: boolean;
}

const PrestacionServicio: React.FC<PrestacionServicio> = ({
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  Asignado,
  documentoSubido,
  ver,
}) => {
  const [files, setFiles] = useState([]);
  const [newFiles, setNewFiles] = useState([]);
  const token = Cookies.get("Token");
  const [interlocutorEncontrado, setInterlocutorEncontrado] = useState(false);
  const [
    interlocutorCliAsociadoEncontrado,
    setInterlocutorCliAsociadoEncontrado,
  ] = useState(false);
  const [idInterlocutor, setIdInterlocutor] = useState(null);
  const [idInterlocutorCliAsociado, setIdInterlocutorCliAsociado] =
    useState(null);
  const [errors, setErrors] = useState({});
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");
  const [activeStep, setActiveStep] = useState(0);
  const [clausulasIncluidas, setClausulasIncluidas] = useState([]);
  const [clausulasSeleccionadas, setClausulasSeleccionadas] = useState([]);
  const [TiposMoneda, setTiposMoneda] = useState({
    str_Moneda: "",
    str_Pais: "",
  });

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };

  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioModificacion: UsuarioId,
    int_idEmpresa: selectedSolicitud.int_idEmpresa,
    int_idUnidadNegocio: selectedSolicitud.int_idUnidadNegocio,
    int_idTipoSol: selectedSolicitud.int_idTipoSol,
    int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
    str_DeTerceros: selectedSolicitud.str_DeTerceros,
    dt_FechaEsperada: selectedSolicitud.dt_FechaEsperada,
    db_Honorarios: selectedSolicitud.db_Honorarios,
    str_idSuscriptor: Suscripcion,
    int_idClienteAsociado: selectedSolicitud.int_idClienteAsociado,
  });
  const [formDataContenido, setFormDataContenido] = useState({
    str_DocAdjuntos: files ? "si" : newFiles.length >= 1 ? "si" : "no",
    str_ObjetivoContrato: "",
    db_Presupuesto: 0,
    str_PlazoSolicitud: "",
    str_Moneda: selectedTipoMoneda,
    str_TipoServicio: "",
    str_Margen: "",
    str_InfoAdicional: "",
    int_idInterlocutor: null,
    str_CondicionPago: "",
    str_RenovacionAuto: "no",
    str_AjusteHonorarios: "no",
    str_DetalleAjusteHonorarios: "",
    str_DetalleRenovAuto: "",
    str_Garantia: "no",
    str_DetalleGarantia: "",
    str_ResolucionAnticipada: "no",
    str_DetalleResolucionAnticipada: "",
    str_Penalidades: "no",
    str_DetallePenalidades: "",
    dt_FechaRenAut: null,
    str_plazoForzoso: "no",
    str_servicioIniciado: "no",
    db_inversion: null,
    str_ans: "no",
  });

  const [formDataInterlocutor, setFormDataInterlocutor] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: selectedSolicitud.int_idSolicitante,
    str_Correo: "",
    str_RepLegal: "",
    str_Domicilio: "",
  });
  const [formDataClienteAsociado, setFormDataClienteAsociado] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_RazonSocial: "",

  });
  const validateForm = (): boolean => {
    let newErrors: Errors = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataInterlocutor.str_Documento)
      newErrors.str_Documento = "El Documento es requerido";
    if (!formDataInterlocutor.str_Interlocutor)
      newErrors.str_Interlocutor = "El proveedor es requerido";
    if (!formDataSolicitud.db_Honorarios)
      newErrors.db_Honorarios = "Los honorarios son requeridos";

    if (!formDataInterlocutor.str_Correo) {
      newErrors.str_Correo = "El Correo es requerido";
    } else {
      const correoValido = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(
        formDataInterlocutor.str_Correo.trim()
      );
      if (!correoValido) {
        newErrors.str_Correo = "El Correo no es válido";
      }
    }
    if (!formDataInterlocutor.str_RepLegal)
      newErrors.str_RepLegal = "El Representante legal es requerido";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.db_Presupuesto)
      newErrors.db_Presupuesto = "El Presupuesto es requerido";
    if (!formDataContenido.str_PlazoSolicitud)
      newErrors.str_PlazoSolicitud = "El Plazo es requerido";
    if (!formDataContenido.str_TipoServicio)
      newErrors.str_TipoServicio = "El Tipo de servicio es requerido";
    if (!formDataContenido.str_Margen)
      newErrors.str_Margen = "El Margen es requerido";
    if (!formDataContenido.str_CondicionPago)
      newErrors.str_CondicionPago = "La condición de pago es requerida";
    if(!formDataContenido.str_plazoForzoso)
      newErrors.str_plazoForzoso = "El plazo forzoso es requerido";
    
    if(formDataContenido.str_plazoForzoso === "si" && !formDataContenido.db_inversion)
      newErrors.db_inversion = "La inversión es requerida";
    
    if(!formDataContenido.str_servicioIniciado)
      newErrors.str_servicioIniciado = "El servicio iniciado es requerido";
    
    setErrors(newErrors);

    // Retorna true si no hay errores
    return Object.keys(newErrors).length === 0;
  };
  const steps = [
    "DATOS GENERALES",
    "DATOS DE CONTRATO",
    "DATOS LEGALES",
    "INFORMACIÓN ADICIONAL",
  ];
  const fetchArchivos = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ListarArchivosEditar"](
          Suscripcion,
          selectedSolicitud.int_idSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setFiles(response.data);
    } catch (error) {
      console.error("Error al obtener las empresas:", error);
    }
  };
  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            db_Presupuesto: response.data.db_Presupuesto,
            str_PlazoSolicitud: response.data.str_PlazoSolicitud,
            str_TipoServicio: response.data.str_TipoServicio,
            str_InfoAdicional: response.data.str_InfoAdicional,
            str_Margen: response.data.str_Margen,
            int_idInterlocutor: response.data.int_idInterlocutor,
            str_Moneda: response.data.str_Moneda || "dolares",
            db_Honorarios: response.data.db_Honorarios,
            str_CondicionPago: response.data.str_CondicionPago,
            str_RenovacionAuto: response.data.str_RenovacionAuto || "no",
            str_AjusteHonorarios: response.data.str_AjusteHonorarios  || "no",
            str_DetalleAjusteHonorarios:
              response.data.str_DetalleAjusteHonorarios,
            str_DetalleRenovAuto: response.data.str_DetalleRenovAuto,
            str_Garantia: response.data.str_Garantia  || "no",
            str_DetalleGarantia: response.data.str_DetalleGarantia,
            str_ResolucionAnticipada: response.data.str_ResolucionAnticipada  || "no",
            str_DetalleResolucionAnticipada:
              response.data.str_DetalleResolucionAnticipada,
            str_Penalidades: response.data.str_Penalidades  || "no",
            str_DetallePenalidades: response.data.str_DetallePenalidades,
            dt_FechaRenAut: response.data.dt_FechaRenAut,
            str_plazoForzoso: response.data.str_plazoForzoso,
            str_servicioIniciado: response.data.str_servicioIniciado,
            db_inversion: response.data.db_inversion,
             str_ans: response.data.str_ans,
          }));
          setSelectedTipoMoneda(response.data.str_Moneda || "dolares");
          const responseInterlocutor = await axios.get(
            API_GESTOR["BuscarInterlocutorID"](
              response.data.int_idInterlocutor
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseInterlocutor.data) {
            setFormDataInterlocutor((prevData) => ({
              ...prevData,
              str_Documento: responseInterlocutor.data.str_Documento,
            }));
            buscarInterlocutor(responseInterlocutor.data.str_Documento, 1);
          } else {
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            db_Presupuesto: 0,
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_Margen: "",
            str_InfoAdicional: "",
          }));
        }
      } catch (error) {}
    };
    const ObtenerInterlocutorAsociado = async () => {
      await validateToken();
      try {
        const responseInterlocutor = await axios.get(
          API_GESTOR["BuscarInterlocutorID"](
            selectedSolicitud.int_idClienteAsociado
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (responseInterlocutor.data) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_Documento: responseInterlocutor.data.str_Documento,
            str_Interlocutor: responseInterlocutor.data.str_Interlocutor,
          }));
          buscarInterlocutor(responseInterlocutor.data.str_Documento, 2);
        } else {
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    const fetchClausulasIncluidas = async () => {
      await validateToken();
      try {
        const responseIncluidas = await axios.get(
          API_GESTOR["ObtenerClausulasIncluidas"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedSolicitud.int_idEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasIncluidas(responseIncluidas.data);

        const responseActivas = await axios.get(
          API_GESTOR["ObtenerClausulasActivas"](
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasSeleccionadas(responseActivas.data.clausulas_activas);
      } catch (error) {
        console.error("Error al obtener las cláusulas:", error);
      }
    };
    fetchClausulasIncluidas();

    DatosContenidoSolicitud();
    ObtenerInterlocutorAsociado();
    fetchArchivos();
  }, []);
  useEffect(() => {
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](
            selectedSolicitud.int_idEmpresa,
            Suscripcion
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    fetchMoneda();
  }, [selectedSolicitud.int_idEmpresa]);
  const renderClausulas = () => {
    if (!clausulasIncluidas || clausulasIncluidas.length === 0) {
      return (
        <div className="no-clausulas-mensaje">
          No hay cláusulas asignadas para esta solicitud
        </div>
      );
    }
  
    const groupedClausulas = [];
  
    for (let i = 0; i < clausulasIncluidas.length; i += 2) {
      groupedClausulas.push(clausulasIncluidas.slice(i, i + 2));
    }
  
    return groupedClausulas.map((group, index) => (
      <div className="inputs-crear-solicitud" key={index}>
        {group.map((clausula, idx) => (
          <div className="div-input-crear-solicitud" key={idx}>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="checkbox"
                value={clausula.int_idClausulasIncluidas}
                checked={clausulasSeleccionadas.includes(
                  clausula.int_idClausulasIncluidas
                )}
                onChange={(e) =>
                  handleCheckboxChange(
                    clausula.int_idClausulasIncluidas,
                    e.target.checked
                  )
                }
                 disabled={ver}
              />
              <label className="form-check-label">
                {clausula.clausula_legal_nombre}
              </label>
            </div>
          </div>
        ))}
      </div>
    ));
  };
  const handleCheckboxChange = async (clausulaId, isChecked) => {
    let updatedClausulas;
    if (isChecked) {
      updatedClausulas = [...clausulasSeleccionadas, clausulaId];
    } else {
      updatedClausulas = clausulasSeleccionadas.filter(
        (id) => id !== clausulaId
      );
    }
    setClausulasSeleccionadas(updatedClausulas);

    try {
      if (isChecked) {
        await axios.post(API_GESTOR["ActivarClausulas"](), {
          str_idSuscripcion: Suscripcion,
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
          int_idClausulaIncluida: clausulaId,
          int_idUsuarioCreacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });
      } else {
        // Llamada a la API para eliminar la cláusula deseleccionada
        await axios.delete(
          API_GESTOR["EliminarClausula"](
            clausulaId,
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
      }
    } catch (error) {
      console.error("Error al actualizar la cláusula:", error);
    }
  };

  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);

    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setNewFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };
  const EliminarArchivo = async (file) => {
    try {
      const response = await axios.delete(
        API_GESTOR["EliminarArchivo"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          file.nombre_archivo,
          file.int_idArchivos,
          file.tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        fetchArchivos();
      }
    } catch (error) {
      console.error("Error al eliminar la solicitud:", error);
    }
  };
  const handleFileRemove = (index, isStoredFile, file) => {
    if (isStoredFile) {
      EliminarArchivo(file);
    } else {
      setNewFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    
    // Caso especial para str_plazoForzoso
    if (name === "str_plazoForzoso") {
      setFormDataContenido((prevData) => ({
        ...prevData,
        [name]: value,
        // Si plazo forzoso es "no", establecer db_inversion como null
        ...(value === "no" ? { db_inversion: null } : {})
      }));
    } 
    // Para otros campos
    else {
      setFormDataSolicitud((prevData) => ({
        ...prevData,
        [name]: value,
      }));
      
      setFormDataInterlocutor((prevData) => ({
        ...prevData,
        [name]: value,
      }));
      
      if (name.startsWith("CliAsociado_")) {
        const fieldName = name.replace("CliAsociado_", "");
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
          [fieldName]: value,
        }));
      }
      
      setFormDataContenido((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    }
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string, value: number) => {
    try {
      const response = await axios.get(
        API_GESTOR["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        if (value === 1) {
          setFormDataInterlocutor((prevData) => ({
            ...prevData,
            str_Interlocutor: response.data.str_Interlocutor,
            str_Correo: response.data.str_Correo,
            str_RepLegal: response.data.str_RepLegal,
            int_RLPartida: response.data.int_RLPartida,
            str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
            str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
            str_Domicilio: response.data.str_Domicilio,
            str_RLDocumento: response.data.str_RLDocumento,
          }));
          setInterlocutorEncontrado(true);
          setIdInterlocutor(response.data.int_idInterlocutor);
        } else if (value === 2) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_RazonSocial: response.data.str_RazonSocial,
          }));
          setIdInterlocutorCliAsociado(response.data.int_idInterlocutor);
          setInterlocutorCliAsociadoEncontrado(true);
        }
      } else {
        if (value === 1) {
          setFormDataInterlocutor((prevData) => ({
            ...prevData,
            str_Interlocutor: "",
            str_Correo: "",
            str_RepLegal: "",
            int_RLPartida: "",
            str_Domicilio: "",
            str_RLDocumento: "",
          }));
          setInterlocutorEncontrado(false);
          setIdInterlocutor(null);
        } else if (value === 2) {
          setInterlocutorCliAsociadoEncontrado(false);
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_RazonSocial: "",
          }));
          setIdInterlocutorCliAsociado(null);
        }
      }
    } catch (error) {
      if (value === 1) {
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_Correo: "",
          str_RepLegal: "",
          int_RLPartida: "",
          str_RLTipoDocumento: "",
          str_Domicilio: "",
          str_RLDocumento: "",
        }));
        setInterlocutorEncontrado(false);
        setIdInterlocutor(null);
      } else if (value === 2) {
        setInterlocutorCliAsociadoEncontrado(false);
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
          str_RazonSocial: "",
        }));
        setIdInterlocutorCliAsociado(null);
      }
    }
  };

  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  };

  const updateInterlocutor = async (id, data) => {
    return axios.put(API_GESTOR["ActualizarInterlocutor"](id), data, {
      headers,
    });
  };

  const addInterlocutor = async (data) => {
    return axios.post(API_GESTOR["AgregarInterlocutor"](), data, { headers });
  };

  const handleSubmitInterlocutores = async () => {
    await validateToken();

    try {
      const formDataModificadaComprador = {
        ...formDataClienteAsociado,
        int_idUsuarioModificacion: UsuarioId,
      };
      const formDataModificada = {
        ...formDataInterlocutor,
        int_idUsuarioModificacion: UsuarioId,
      };

      delete formDataModificadaComprador.int_idUsuarioCreacion;
      delete formDataModificada.int_idUsuarioCreacion;

      if (interlocutorCliAsociadoEncontrado && interlocutorEncontrado) {
        await updateInterlocutor(
          idInterlocutorCliAsociado,
          formDataModificadaComprador
        );
        await updateInterlocutor(idInterlocutor, formDataModificada);

        handleSubmitContenidoSolicitud(idInterlocutor);

        return idInterlocutorCliAsociado;
      } else if (!interlocutorCliAsociadoEncontrado && interlocutorEncontrado) {
        let response;
        if (formDataClienteAsociado.str_Documento) {
          response = await addInterlocutor(formDataClienteAsociado);
        }
        await updateInterlocutor(idInterlocutor, formDataModificada);

        handleSubmitContenidoSolicitud(idInterlocutor);
        return response ? response.data.int_idInterlocutor : null;
      } else if (interlocutorCliAsociadoEncontrado && !interlocutorEncontrado) {
        await updateInterlocutor(
          idInterlocutorCliAsociado,
          formDataModificadaComprador
        );
        const response = await addInterlocutor(formDataInterlocutor);

        setIdInterlocutor(response.data.int_idInterlocutor);
        handleSubmitContenidoSolicitud(response.data.int_idInterlocutor);

        return idInterlocutorCliAsociado;
      } else {
        const response2 = await addInterlocutor(formDataClienteAsociado);
        let response1;
        if (formDataClienteAsociado.str_Documento) {
          response1 = await addInterlocutor(formDataInterlocutor);
        }
        setIdInterlocutor(response1 ? response1.data.int_idInterlocutor : null);
        handleSubmitContenidoSolicitud(response2.data.int_idInterlocutor);

        return response1 ? response1.data.int_idInterlocutor : null;
      }
    } catch (error) {
      console.error(
        "Error al gestionar el interlocutor:",
        error.response?.data || error.message
      );
    }
  };

  //--------------------------------------------------------------------------------

  const handleSubmitGuardarSolicitud = async (
    idInterlocutorCliAsociado: number
  ) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarSolicitud"](selectedSolicitud.int_idSolicitudes),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitConfirmarSolicitud = async (idInterlocutorCliAsociado) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarSolicitud"](selectedSolicitud.int_idSolicitudes),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitContenidoSolicitud = async (idInterlocutor) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idInterlocutor: idInterlocutor,
      };
      const response = await axios.put(
        API_GESTOR["ActualizarContenidoSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitFiles = async () => {
    await validateToken();
    try {
      for (const file of newFiles) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          selectedSolicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          selectedSolicitud.int_idSolicitudes
        );
             if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }
      }
    } catch (error) {}
  };
  const CambiarEstado = async () => {
    try {
      const responseCambiarEstado = await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: !Asignado ? "En Validación" : "En Proceso",
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitSolicitudGuardar = async () => {
    await validateToken();
    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        await handleSubmitGuardarSolicitud(null).catch((err) => {
          success = false;
          errorMessages.push("Error al guardar la solicitud: " + err.message);
        });
      } else {
        await handleSubmitGuardarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al guardar la solicitud: " + err.message);
        });
      }

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Guardada Correctamente..",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
                await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };

  const handleSubmitSolicitudConfirmar = async () => {
    await validateToken();
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop();
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }

    let success = true;
    let errorMessages = [];

    try {
      const idInterlocutor = await handleSubmitInterlocutores();

      if (!idInterlocutor) {
        await handleSubmitConfirmarSolicitud(null).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });
      } else {
        await handleSubmitConfirmarSolicitud(idInterlocutor).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });
      }
      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (!Asignado) {
        await CambiarEstado().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }
      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Confirmada.",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
                await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };
  const handleDownload = async (nombreArchivo: string,tipo_adjunto:string) => {
    try {
      const response2 = await axios.get(
        API_GESTOR["DescargarArchivoNombre"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          nombreArchivo,
          tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          responseType: "blob",
        }
      );
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        : `${nombreArchivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={selectedSolicitud.nombre_completo}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutor.str_TipoDoc || ""} >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_NombreEmpresa"
                        value={selectedSolicitud.str_NombreEmpresa}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="text"
                        name="str_Documento"
                        className="form-control"
                        value={formDataInterlocutor.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value, 1);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Descripcion_UnidadNegocio"
                        value={selectedSolicitud.str_Descripcion_UnidadNegocio}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label"> Cliente</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_Interlocutor && "is-invalid"
                        }`}
                        name="str_Interlocutor"
                        value={formDataInterlocutor.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    
                  </div>
                        <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud"></div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio/Dirección:</label>
                      <input
                        type="text"
                        name="str_Domicilio"
                        className={`form-control ${
                          errors.str_Domicilio && "is-invalid"
                        }`}
                        value={formDataInterlocutor.str_Domicilio}
                        placeholder=""
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Objeto del contrato
                      </label>
                      <textarea
                        className={`form-control ${
                          errors.str_ObjetivoContrato && "is-invalid"
                        }`}
                        id=""
                        name="str_ObjetivoContrato"
                        rows={3}
                        value={formDataContenido.str_ObjetivoContrato}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">

                    <div className="div-input-fecha-solicitud">
                    <label className="form-label">Representante:</label>
                      <input
                        type="text"
                        name="str_RepLegal"
                        className={`form-control ${
                          errors.str_RepLegal && "is-invalid"
                        }`}
                        value={formDataInterlocutor.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    <div className="div-input-fecha-solicitud">
                    <label className="form-label">Correo Electrónico:</label>
                      <input
                        type="email"
                        name="str_Correo"
                        className={`form-control ${
                          errors.str_Correo && "is-invalid"
                        }`}
                        placeholder=""
                        value={formDataInterlocutor.str_Correo}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    </div>
               
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Condiciones del Servicio</label>
                      <textarea
                        className="form-control"
                        name="str_InfoAdicional"
                        id="exampleFormControlTextarea1"
                        rows={3}
                        value={formDataContenido.str_InfoAdicional}
                        onChange={handleTextareaChange}
                        readOnly={ver}
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha de Registro:{" "}
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={new Date().toISOString().split("T")[0]}
                          className="form-control"
                          value={
                            selectedSolicitud.dt_FechaRegistro.split("T")[0] ||
                            ""
                          }
                          name="dt_FechaRegistro"
                          readOnly
                        />
                      </div>
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha Esperada de entrega:{" "}
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={new Date().toISOString().split("T")[0]}
                          className="form-control"
                          value={
                            formDataSolicitud.dt_FechaEsperada
                              ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                              : ""
                          }
                          name="dt_FechaEsperada"
                          onChange={handleInputChange}

                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Datos del contrato
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Monto del contrato:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="db_Honorarios"
                        value={formDataSolicitud.db_Honorarios}
                        onChange={handleInputChange}
                        required
                        readOnly={ver}
                      />
                      {errors.db_Honorarios && (
                        <span className="error-message">
                          {errors.db_Honorarios}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Moneda:</label>
                      <div className="radio-inputs-crear-solicitud">
                        {TiposMoneda.str_Moneda !== "Dolares" && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "dolares"
                                ? "check-selected"
                                : ""
                            }`}
                          >
                            <input
                              className="form-check-input"
                              type="radio"
                              name="inlineRadioOptions"
                              id="monedaDolares"
                              value="dolares"
                              checked={selectedTipoMoneda === "dolares"}
                              onChange={handleChangeTipoMoneda}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="monedaDolares"
                            >
                              <IconoDolares size={"1.5rem"} color={"#156CFF"} />{" "}
                              Dólares
                            </label>
                          </div>
                        )}

                        {Object.keys(TiposMoneda).length > 0 && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "empresa"
                                ? "check-selected"
                                : ""
                            }`}
                          >
                            {" "}
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoMoneda"
                              id="tipoMoneda"
                              value={"empresa"}
                              checked={selectedTipoMoneda === "empresa"}
                              onChange={handleChangeTipoMoneda}
                              disabled={ver}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="tipoMoneda"
                            >
                              <IconoMoneda size={"1.5rem"} color={"#156CFF"} />{" "}
                              {TiposMoneda.str_Moneda}
                            </label>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Presupuesto:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="db_Presupuesto"
                        value={formDataContenido.db_Presupuesto}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.db_Presupuesto && (
                        <span className="error-message">
                          {errors.db_Presupuesto}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Condición de pago:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_CondicionPago"
                        value={formDataContenido.str_CondicionPago}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_CondicionPago && (
                        <span className="error-message">
                          {errors.str_CondicionPago}
                        </span>
                      )}
                    </div>
                        <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Acuerdo de Nivel de Servicio
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ans === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ans"
                            id="sians"
                            value="si"
                            checked={formDataContenido.str_ans === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="sians">
                            Sí
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ans === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ans"
                            id="Noans"
                            value="no"
                            checked={formDataContenido.str_ans === "no"}
                            onChange={handleInputChange}
                             disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="Noans">
                            No
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Plazo de ejecución:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_PlazoSolicitud"
                        value={formDataContenido.str_PlazoSolicitud}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_PlazoSolicitud && (
                        <span className="error-message">
                          {errors.str_PlazoSolicitud}
                        </span>
                      )}
                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Margen:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_Margen"
                        value={formDataContenido.str_Margen}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Margen && (
                        <span className="error-message">
                          {errors.str_Margen}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo Servicio:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_TipoServicio"
                        onChange={handleInputChange}
                        value={formDataContenido.str_TipoServicio}
                        readOnly={ver}
                      />
                      {errors.str_TipoServicio && (
                        <span className="error-message">
                          {errors.str_TipoServicio}
                        </span>
                      )}
                    </div>
                  </div>
                      <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Servicio Iniciado:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_servicioIniciado === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_servicioIniciado"
                            id="servicioIniciado"
                            value={"si"}
                            checked={formDataContenido.str_servicioIniciado === "si"} 
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="servicioIniciado"
                          >
                            Si
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_servicioIniciado === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_servicioIniciado"
                            id="noServicioIniciado"
                            value={"no"}
                            checked={formDataContenido.str_servicioIniciado === "no"} 
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="noServicioIniciado"
                          >
                            No
                          </label>
                        </div> 
                      </div>
                    </div>
<div className="div-input-crear-solicitud">
  <label className="form-label">Plazo Forzoso</label>
                            <div className="radio-inputs-crear-solicitud">
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_plazoForzoso === "si"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_plazoForzoso"
                            id="siPlazoForzoso"
                            value="si"
                            checked={
                              formDataContenido.str_plazoForzoso === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                           />
                          <label className="form-check-label" htmlFor="siPlazoForzoso">Sí</label>
                        </div>
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_plazoForzoso === "no"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_plazoForzoso"
                            id="NoplazoForzoso"
                            value="no"
                            checked={
                              formDataContenido.str_plazoForzoso === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                           />
                          <label className="form-check-label" htmlFor="NoplazoForzoso">No</label>
                        </div>
                      </div>
</div>
 
                         <div className="div-input-crear-solicitud">
                         <label className="form-label">Inversión</label>
                         <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${
                          errors.db_inversion && "is-invalid"
                        }`}
                        placeholder=""
                        name="db_inversion"
                        value={formDataContenido.str_plazoForzoso === "no" ? "" : formDataContenido.db_inversion || ""}
                        onChange={handleInputChange}
                        disabled={formDataContenido.str_plazoForzoso === "no" || ver}
                         
                      /></div>
                       

                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Renovación Automática:
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_RenovacionAuto === "si"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_RenovacionAuto"
                            id="SiRenovAuto"
                            value="si"
                            checked={
                              formDataContenido.str_RenovacionAuto === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiRenovAuto">Sí</label>
                        </div>
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_RenovacionAuto === "no"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_RenovacionAuto"
                            id="NoRenovAuto"
                            value="no"
                            checked={
                              formDataContenido.str_RenovacionAuto === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoRenovAuto">No</label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Ajuste de Monto del contrato:
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_AjusteHonorarios === "si"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_AjusteHonorarios"
                            id="SiAjusteHonorarios"
                            value="si"
                            checked={
                              formDataContenido.str_AjusteHonorarios === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiAjusteHonorarios">Sí</label>
                        </div>
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_AjusteHonorarios === "no"
                              ? "check-selected"
                              : ""
                          }`}>                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_AjusteHonorarios"
                            id="NoAjusteHonorarios"
                            value="no"
                            checked={
                              formDataContenido.str_AjusteHonorarios === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoAjusteHonorarios">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                          formDataContenido.dt_FechaRenAut
                            ? formDataContenido.dt_FechaRenAut.split("T")[0]
                            : ""
                        }
                        onChange={handleInputChange}
                        name="dt_FechaRenAut"
                        disabled={
                          formDataContenido.str_RenovacionAuto === "no" || ver
                        }
                      />
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleRenovAuto"
                        rows={2}
                        value={formDataContenido.str_DetalleRenovAuto}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_RenovacionAuto === "no" || ver
                        }
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleAjusteHonorarios"
                        rows={3}
                        value={formDataContenido.str_DetalleAjusteHonorarios}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_AjusteHonorarios === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Garantía:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Garantia === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Garantia"
                            id="SiGarantia"
                            value="si"
                            checked={formDataContenido.str_Garantia === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="SiGarantia"
                          >
                            Sí
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Garantia === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Garantia"
                            id="NoGarantia"
                            value="no"
                            checked={formDataContenido.str_Garantia === "no"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="NoGarantia"
                          >
                            No
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleGarantia"
                        rows={3}
                        value={formDataContenido.str_DetalleGarantia}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_Garantia === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(2)}
                >
                  Datos Legales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Resolución Anticipada:
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                      <div className={`check-group form-check-inline ${
                            formDataContenido.str_ResolucionAnticipada === "si"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ResolucionAnticipada"
                            id="SiResolucionAnticipada"
                            value="si"
                            checked={
                              formDataContenido.str_ResolucionAnticipada ===
                              "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiResolucionAnticipada">Sí</label>
                        </div>
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_ResolucionAnticipada === "no"
                              ? "check-selected"
                              : ""
                          }`}>                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ResolucionAnticipada"
                            id="NoResolucionAnticipada"
                            value="no"
                            checked={
                              formDataContenido.str_ResolucionAnticipada ===
                              "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoResolucionAnticipada">No</label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Penalidades:</label>
                      <div className="radio-inputs-crear-solicitud">
                      <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >                            <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="SiPenalidades"
                            value="si"
                            checked={formDataContenido.str_Penalidades === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiPenalidades">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >  
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="NoPenalidades"
                            value="no"
                            checked={formDataContenido.str_Penalidades === "no"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoPenalidades">No</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetalleResolucionAnticipada"
                        rows={3}
                        value={
                          formDataContenido.str_DetalleResolucionAnticipada
                        }
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_ResolucionAnticipada === "no" ||
                          ver
                        }
                      ></textarea>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <textarea
                        className="form-control"
                        id=""
                        name="str_DetallePenalidades"
                        rows={3}
                        value={formDataContenido.str_DetallePenalidades}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_Penalidades === "no" || ver
                        }
                      ></textarea>
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Inclusión de Clausulas
                  </div>
                  {renderClausulas()}
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingFour">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseFour"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseFour"
                  onClick={() => handleStepChange(3)}
                >
                  Condiciones del Servicio
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseFour"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingFour"
              >
                <div className="accordion-body">
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Asociación a cuenta del cliente
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Interlocutor"
                        value={formDataClienteAsociado.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="number"
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Documento"
                        value={formDataClienteAsociado.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value, 2);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Razón Social:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_RazonSocial"
                        value={formDataClienteAsociado.str_RazonSocial}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        newFiles={newFiles}
        esEdicion={true}
        ver={ver}
        gestor={true}
        handleDownload={handleDownload}
        documentoSubido={documentoSubido}
      />
    </div>
  );
};

export default PrestacionServicio;
