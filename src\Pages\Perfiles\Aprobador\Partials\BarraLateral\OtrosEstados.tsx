import { Tooltip } from "@mui/material";
import ListaAprobadores from "./ListaAprobadores";
import axios from "axios"; // Para realizar solicitudes HTTP
import { useState } from "react"; // Para manejar el estado local
import Swal from "sweetalert2";
import API_APROBADOR from "../../../../../assets/Api/ApisAprobador";
import ModalAceptar from "../Modals/ModalAceptar";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import Cookies from "js-cookie";
import {
  getTokenFromCookie,
  validateToken,
} from "../../../../Components/Services/TokenService";
import IconoCheck from "../../../../../assets/SVG/IconoCheck";
import IconoDownload from "../../../../../assets/SVG/IconoDownload";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
import { Navigate, useNavigate } from "react-router-dom";
import IconoVer from "../../../../../assets/SVG/IconoVer";

// Definición de tipos para las props
interface SolicitudSeleccionada {
  int_idSolicitudes: number;
  str_CodSolicitudes: string;
  nombre_TipoSolicitud: string;
}

interface OtrosEstadosProps {
  solicitudSeleccionada: SolicitudSeleccionada;
  aprobadores: any[]; // Puedes definir un tipo más específico según la estructura de `aprobadores`
  idUsuario: string | number;
  obtenerSolicitudesUsuario: () => void;
  obtenerAprobadores: () => void;
  Suscripcion: string | number;
  setSelectedSolicitud: () => void;
  Suscriptor: string | number;
  idAplicacion: string | number;
}

const OtrosEstados: React.FC<OtrosEstadosProps> = ({
  solicitudSeleccionada,
  aprobadores,
  idUsuario,
  obtenerSolicitudesUsuario,
  obtenerAprobadores,
  Suscripcion,
  setSelectedSolicitud,
  Suscriptor,
  idAplicacion,
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAceptar: false,
  });
  const token = Cookies.get("Token");
  const navigate = useNavigate();
  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const FirmarAprobación = async () => {
    await validateToken();
    try {
      await axios.post(
        API_SOLICITANTE["FirmarAceptacion"](),
        {
          str_idSuscriptor: Suscripcion,
          str_CodSolicitudes: solicitudSeleccionada.str_CodSolicitudes,
          int_idUsuarioCreacion: idUsuario,
          str_CodTipoDocumento: "COAP",
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return true;
    } catch (error) {
      Swal.fire(
        "",
        "Hubo un error al firmar el contrato , por favor verifique su firma",
        "error"
      );
      return false;
    }
  };

  // Función para manejar la aprobación
  const handleAprobarSolicitud = async () => {
    await validateToken();
 
    try {
      const response = await axios.put(
        API_APROBADOR["AprobarSolicitud"](
          solicitudSeleccionada.int_idSolicitudes,
          idUsuario,
          Suscripcion
        ),
        {}, 
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );

      // const firma = await FirmarAprobación();

      if (response.status >= 200 && response.status < 300 ) {
        Swal.fire("", "Solicitud Aprobada", "success");
        setSelectedSolicitud(null);
        obtenerSolicitudesUsuario();
        setModales((prev) => ({ ...prev, isModalVisibleAceptar: false }));
      } else {
        Swal.fire("", "No se pudo Firmar la Aprobación", "error");
      }
    } catch (error) {
      console.error("Error al aprobar la solicitud:", error);
      Swal.fire(
        "",
        "Alguno de los aprobadores anteriores no ha aprobado aún la solicitud",
        "error"
      ).then(() => {
        toggleModal("isModalVisibleAceptar");
      });
    }
  };
  const EstadoRechazar = async () => {
    await validateToken();
    try {
      await axios.put(
        API_SOLICITANTE["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: solicitudSeleccionada.str_CodSolicitudes.includes(
            "-EJ"
          )
            ? "Aceptado"
            : "En Proceso",
          int_idUsuarioCreacion: idUsuario,
          int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );
      Swal.fire("Rechazado", "Documento Rechazado", "success");
              setSelectedSolicitud(null);
      obtenerSolicitudesUsuario();
 
     } catch (error) {
      console.error("Error al cambiar el estado:", error);
      Swal.fire("", "No se pudo cambiar el estado de la solicitud", "error");
    }
  };
  const handleRechazarSolicitud = async () => {
    await validateToken();
    try {
      const response = await axios.put(
        API_APROBADOR["RechazarSolicitud"](
          solicitudSeleccionada.int_idSolicitudes,
          idUsuario,
          Suscripcion
        ),
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );

      if (response.status === 200) {
        setModales((prev) => ({ ...prev, isModalVisibleAceptar: false }));
        EstadoRechazar();
        obtenerSolicitudesUsuario();
        obtenerAprobadores();
        setSelectedSolicitud(solicitudSeleccionada);
      }
    } catch (error) {
       Swal.fire(
        "",
        "Alguno de los aprobadores anteriores no ha aprobado aún la solicitud",
        "error"
      );
    }
  };
  const handleDownload = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_APROBADOR["ListarArchivo"](
          Suscripcion,
          solicitudSeleccionada.str_CodSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.data) {
        const response2 = await axios.get(
          API_APROBADOR["DescargarArchivo"](
            Suscripcion,
            solicitudSeleccionada.str_CodSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            responseType: "blob",
          }
        );
        const contentDisposition = response2.headers["content-disposition"];
        const filename = contentDisposition
          ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
          : `${response.data.nombre_archivo}`;

        const url = window.URL.createObjectURL(new Blob([response2.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
     const handleEditarSolicitud = () =>{
          navigate(RoutesPrivate.VERSOLICITUD, {state:{solicitudSeleccionada,Suscripcion,idAplicacion,idUsuario,Suscriptor, ver:true}})
        }
  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        {solicitudSeleccionada.dt_FechaAceptacion ? (
          ""
        ) : (
          <Tooltip title="Aprobar Solicitud" placement="top">
            <div
              className="icono-barralateral-acciones"
              onClick={() => toggleModal("isModalVisibleAceptar")}
            >
              <IconoCheck size={"1.3rem"} color={"#000"} />
            </div>
          </Tooltip>
        )}
        <Tooltip title="Descargar" placement="top">
          <div className="icono-barralateral-acciones" onClick={handleDownload}>
            <IconoDownload size={"1.3rem"} color={"#000"} />
          </div>
        </Tooltip>
        <Tooltip title="Ver Solicitud" placement="top">
    
        <div
              onClick={handleEditarSolicitud}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
        </Tooltip>
      </div>
      <ListaAprobadores aprobadores={aprobadores} />
      <ModalAceptar
        isModalVisibleAceptar={modales.isModalVisibleAceptar}
        CerrarModalEliminar={() => toggleModal("isModalVisibleAceptar")}
        solicitudSeleccionada={solicitudSeleccionada}
        SubmitObtenerDatos={obtenerSolicitudesUsuario}
        accion={handleAprobarSolicitud}
        handleRechazarSolicitud={handleRechazarSolicitud}
      />
    </>
  );
};

export default OtrosEstados;
