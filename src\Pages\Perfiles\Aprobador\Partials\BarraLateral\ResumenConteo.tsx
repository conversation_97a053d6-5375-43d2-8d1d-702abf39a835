import React from 'react'

const ResumenConteo = ({data}) => {
  const enAprobacion = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Aprobación").length;

  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">Solicitudes</span>
      <ul className="list-barra-Lateral montserrat-font">
        <li>
          <span>Por Aprobar</span> <span className="montserrat-font-500">{enAprobacion}</span>
        </li>

      </ul>
    </>
  );
}

export default ResumenConteo