import { Route, Routes, Navigate } from "react-router-dom";
import { RoutesPrivate } from "./ProtectedRoute";
import InicioSolicitante from "../../Pages/Perfiles/Solicitante/Inicio";
import InicioGestor from "../../Pages/Perfiles/Gestor/Inicio";
import InicioAprobador from "../../Pages/Perfiles/Aprobador/Inicio";
import InicioGerenteLegal from "../../Pages/Perfiles/GerenteLegal/Inicio";
import TablaGerenteLegal from "../../Pages/Perfiles/GerenteLegal/TablaSolicitudes/Tabla";
import EditarSolicitudGL from "../../Pages/Perfiles/GerenteLegal/Partials/BarraLateral/Estados/EditarSolicitud/EditarSolicitud";
import GestionSolicitud from "../../Pages/Perfiles/Solicitante/CrearSolicitud/GestionSolicitud";
import EditarSolicitud from "../../Pages/Perfiles/Gestor/Partials/BarraLateral/Estados/EditarSolicitud/EditarSolicitud";
import Reportes from "../../Pages/Perfiles/Gestor/Reportes/Reportes";
import ReportesGerente from "../../Pages/Perfiles/GerenteLegal/Reportes/Reportes";
import ReportesSolicitante from "../../Pages/Perfiles/Solicitante/Reportes/Reportes";

import Administracion from "../../Pages/Perfiles/GestorAdmin/Administracion/Administracion";
import CrearAdenda from "../../Pages/Perfiles/Solicitante/CrearAdendas/CrearAdenda";
import ExtraJudicial from "../../Pages/Perfiles/Gestor/Partials/BarraLateral/Estados/EditarSolicitud/TipoSolicitudes/ExtraJudicial";
import CrearEJ from "../../Pages/Perfiles/Gestor/Partials/BarraLateral/Estados/EditarSolicitud/CrearEJ";

interface PrivateRoutesProps {
  perfil: string;
}

const PrivateRoutes: React.FC<PrivateRoutesProps> = ({ perfil }) => {
  return (
    <Routes>
      {perfil === "Solicitante" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Solicitante" />}
          />
          <Route
            path={RoutesPrivate.INICIOSOLICITANTE}
            element={<InicioSolicitante />}
          />
          <Route
            path={RoutesPrivate.GESTIONSOLICITUD}
            element={<GestionSolicitud />}
          />
          <Route
            path={RoutesPrivate.CREARADENDA}
            element={<CrearAdenda />}
          />
           <Route
            path={RoutesPrivate.EDITARSOLICITUD}
            element={<EditarSolicitud />}
          />
          <Route
            path={RoutesPrivate.REPORTESSOLICITANTE}
            element={<ReportesSolicitante />}
          />
          <Route path="*" element={<Navigate to="/error/404" />} />
        </>
      ) : perfil === "Gestor" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Gestor" />}
          />
          <Route
            path={RoutesPrivate.INICIOGESTOR}
            element={<InicioGestor perfil={perfil} />}
          />
          <Route
            path={RoutesPrivate.EDITARSOLICITUD}
            element={<EditarSolicitud />}
          />
          <Route
            path={RoutesPrivate.CREARADENDA}
            element={<CrearAdenda />}
          />
          <Route
            path={RoutesPrivate.CREAREJ}
            element={<CrearEJ />}
          />
          <Route path={RoutesPrivate.REPORTESGESTOR} element={<Reportes />} />
        </>
      ) : perfil === "Aprobador Gerente" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Gerente-Legal" />}
          />
          <Route
            path={RoutesPrivate.INICIOGERENTELEGAL}
            element={<InicioGerenteLegal />}
          />
          <Route
            path={RoutesPrivate.VERSOLICITUD}
            element={<EditarSolicitudGL />}
          />
          <Route
            path={RoutesPrivate.REPORTESGESTOR}
            element={<ReportesGerente />}
          />
          <Route
            path={RoutesPrivate.CREARADENDA}
            element={<CrearAdenda />}
          />
 
          <Route
            path={RoutesPrivate.CREAREJ}
            element={<CrearEJ />}
          />
        </>
      ) : perfil === "Aprobador" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Aprobador" />}
          />
          <Route
            path={RoutesPrivate.INICIOAPROBADOR}
            element={<InicioAprobador />}
          />
          <Route
            path={RoutesPrivate.VERSOLICITUD}
            element={<EditarSolicitudGL />}
          />
        </>
      ) : perfil === "Gestor Administrador" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Gestor" />}
          />
          <Route
            path={RoutesPrivate.INICIOGESTOR}
            element={<InicioGestor perfil={perfil} />}
          />
          <Route
            path={RoutesPrivate.EDITARSOLICITUD}
            element={<EditarSolicitud />}
          />
          <Route path={RoutesPrivate.REPORTESGESTOR} element={<Reportes />} />
          <Route
            path={RoutesPrivate.ADMINISTRACION}
            element={<Administracion />}
          />
          <Route
            path={RoutesPrivate.CREARADENDA}
            element={<CrearAdenda />}
          />
        </>
      ) : perfil === "Gestor Controller" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Gestor" />}
          />
          <Route
            path={RoutesPrivate.INICIOGESTOR}
            element={<InicioGestor perfil={perfil} />}
          />
          <Route
            path={RoutesPrivate.EDITARSOLICITUD}
            element={<EditarSolicitud />}
          />
          <Route
            path={RoutesPrivate.CREARADENDA}
            element={<CrearAdenda />}
          />
 
          <Route
            path={RoutesPrivate.CREAREJ}
            element={<CrearEJ />}
          />
          <Route path={RoutesPrivate.REPORTESGESTOR} element={<Reportes />} />
        </>
      ) : perfil === "Asistente" ? (
        <>
          <Route
            path="/*"
            element={<Navigate to="/Prisma-Contratos/Inicio-Asistente" />}
          />
          <Route
            path={RoutesPrivate.INICIOASISTENTE}
            element={<InicioGerenteLegal />}
          />
          <Route
            path={RoutesPrivate.VERSOLICITUD}
            element={<EditarSolicitudGL />}
          />
          <Route
            path={RoutesPrivate.REPORTESGESTOR}
            element={<ReportesGerente />}
          />
        </>
      ) : (
        <>""</>
      )}
    </Routes>
  );
};

export { PrivateRoutes };
