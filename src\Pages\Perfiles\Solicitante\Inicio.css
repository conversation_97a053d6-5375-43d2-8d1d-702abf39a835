/*CONTENEDOR GLOBAL*/
.global-inicio-solicitante {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9feff;
  margin-bottom: 1.508rem;
}

/*CONTENEDOR GENERADOR NUEVA SOLICITUD*/
.container-nueva-solicitud {
  width: 95%;
  display: flex;
  justify-content: center;
  gap: 1rem;
  align-items: stretch;
  margin: auto;
}

.card-nueva-solicitud {
  width: 50%;
  border: .0625rem solid #d9d9d9;
  background: #fdfdfd;
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.titulo-card-nueva-solicitud {
  width: 100%;
  margin-bottom: 1rem;
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  display: flex;
}

.titulo-card-nueva-solicitud span {
  font-size: 1.5rem;
  color: #4b4b4b;
  text-align: center;
}

.select-card-nueva-solicitud {
  width: 90%;
}

.css-b62m3t-container {
  font-size: 1rem;
  box-shadow: none;
}

 .css-13cymwt-control {
  background-color: #f7f7f7;
  border: none;
}
 .css-hlgwow{
  padding: 0.125rem 0.5rem;
}
.css-1xc3v61-indicatorContainer{
  padding: 0.5rem !important;
}
.css-1xc3v61-indicatorContainer svg{
  width: 1.25rem;
  height: 1.25rem;
}
.css-13cymwt-control{
  min-height: 2.375rem !important;
}
.css-1u9des2-indicatorSeparator{
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
  width: 0.0625rem;
}

.css-19bb58m{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-size: 1rem !important;
}
.css-hlgwow{
  font-size: 1rem !important;

}
.css-b62m3t-container{
  min-height: 2.375rem !important;
  border-radius: 0.25rem !important;
  border-width: 0.0625rem !important;
}
.css-t3ipsp-control{
  min-height: 2rem !important;
  border-radius: 0.6rem !important;
  border-width: 0.0625rem !important;
 
}
.modelo-card-nueva-solicitud {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 90%;
}

.radio-card-nueva-solicitud {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.span-radio-card-NS {
  font-size: 1rem;
  color: #4b4b4b;
}

.radio-card-nueva-solicitud .form-check-label {
  margin-left: 0.3125rem;
}

.boton-card-nueva-solicitud {
  display: flex;
  background: #294fcf;
  border-radius: 1.5rem;
  padding: 0.3125rem 0.95rem;
  color: white;
  font-size: 1rem;
  cursor: pointer;
}

.boton-card-nueva-solicitud:active {
  background: #3f62d4;
}

.div-boton-filtrar-inicio-solicitante {
  width: 100%;
  margin: auto;
  display: flex;
  justify-content: left;
  align-items: center;
  margin-top: 0.5rem;
}
.div-boton-filtrar-solicitudes-inicio-solicitante {
  width: 95%;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.3rem;
  padding:  0 1rem;
}
.texto-solicitudes-inicio-gestor{
 color: #272727;
 font-size: 1.75rem;
}
.boton-filtrar-inicio-solicitante {
  display: flex;
  background: #fff;
  border-radius: 1.5rem;
  padding: 0.3125rem 0.95rem;
  color: #294fcf;
  font-size: 1rem;
  cursor: pointer;
  border: 0.0625rem solid #294fcf;
  gap: 0.3125rem;
  justify-content: center;
  align-items: center;
}

.boton-filtrar-inicio-solicitante:active {
  background: #294fcf;
  color: white;
}

/*MODAL DE FILTROS*/
.modal-filtros {
  position: absolute;
  top: 47%;
  right: 100px;
  background: white;
  padding: 1rem 2.5rem;
  box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
  border-radius: 1.5rem;
  z-index: 10;
  width: 25%;
  
}
.div-btn-filtrar{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.3rem;
}
.btn-filtrar {
  background-color: #294fcf;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  cursor: pointer;
}
.btn-filtrar:hover{
  background-color: #294fcf;
  color: white;
}
.btn-filtrar:active{
  background-color: #3c60da !important;
  color: white !important;
}
.boton-cerrar-modal-filtros {
  display: flex;
  justify-content: right;
  width: 100%;
}

.titulo-modal-filtros {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0.9375rem;
}

.text-titulo-modal-filtros {
  font-size: 1.4rem;
  text-align: left;
  color: #4b4b4b;
  padding: 0.9375rem;
}

.borrarTodo-modal-filtros {
  color: #2b67f6;
  font-size: .9rem;
  cursor: pointer;
}

.inputs-modal-filtros {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
/*MODAL ASIGNAR*/
.modal-asignar {
  position: absolute;
  top: 62%;
  right: 100px;
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
  border-radius: 1.5rem;
  z-index: 10;
  width: 25%;
  border: 0.0625rem solid #ccc;

}
.modal-aceptar-solicitud{
  position: absolute;
  top: 45%;
  right: 100px;
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
  border-radius: 1.5rem;
  z-index: 10;
  width: 25%;
  border: 0.0625rem solid #ccc;
}
.modal-asignar-controller{
  position: absolute;
  top: 35%;
  right: 100px;
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
  border-radius: 1.5rem;
  z-index: 10;
  width: 25%;
  border: 0.0625rem solid #ccc;

}
.div-btn-asignar{
  display: flex;
  justify-content: right;
}
.btn-asignar {
  background-color: #294fcf;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  cursor: pointer;
  margin-top: 1.3rem;
}
.btn-asignar:hover{
  background-color: #294fcf;
  color: white;
}
.btn-asignar:active{
  background-color: #3c60da !important;
  color: white !important;
}
.boton-cerrar-modal-filtros {
  display: flex;
  justify-content: right;
  width: 100%;
}


.input-select-gestor{
  padding-bottom: 0.625rem;
  border-bottom: 0.0625rem solid #ccc;
}
.labels-datos-gestor{
  display: flex;
  justify-content: space-between;
  width: 80%;
  margin-left: 0.3125rem;
}
.label-nombre-gestor{
  font-size: 1rem;
}
/*MODAL ELIMINAR */

.pregunta-modal-solicitante{
  width: 100%;
  margin-top: 0.625rem;
  margin-bottom: 1.508rem;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pregunta-modal-solicitante span{
  text-align: center;
}
.botones-modal-solicitante{
  display: flex;
  gap: 0.9375rem;
  width: 100%;
  padding: 0.625rem;
}
/*INPUT DE TAGS*/
.tag-input-container {
  margin-top: 0.3125rem;
  width: 100%;
  max-width: 100%;
}

.tag-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  border: 0.0625rem solid #ccc;
  padding: 0.25rem;
  border-radius: 4px;
  max-width: 100%;
  width: 100%;
box-sizing: border-box;
}

.tag {
  background-color: #f7f7f7;
  color: #4b4b4b;
  border-radius: 4px;
  padding: 0.2rem 0.625rem;
  display: inline-flex;
  align-items: center;
  max-width: calc(100% - 1rem); 
  box-sizing: border-box;
  white-space: nowrap;  
  overflow: hidden;
  flex-wrap: wrap;
  text-overflow: ellipsis; 
 }

.tag-remove {
  background: none;
  border: none;
  color: #4b4b4b;
  font-size: 1rem;
  margin-left: 0.5rem;
  cursor: pointer;
}

.tag-remove:hover {
  color: #ffcccc;
}

input {
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  outline: none;
  flex: 1;
}

/*INPUT DE AÑO*/
.anno-mes-modal-filtros {
  display: flex;
  gap: 0.9375rem;
  margin: 0.625rem 0;
}
/*TABLA DE CONTENIDO*/
.div-container-tabla-inicio-solicitante{
  width: 95%;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: stretch;
  gap: 1.508rem;
  margin-top: 1rem;
}
.div-tabla-inicio-solicitante{
  width: 80%;
  display: flex;
  height: 100%;
}
.tabla-inicio-solicitante{
  width: 100%;
  background-color: white;
  border-collapse: separate;
  border-spacing: 0;
  border: 0.0625rem solid #ddd;
  border-radius: 0.625rem;
  overflow: hidden;
  box-shadow: 0px 4px 4px 0px #00000040;

}
.tabla-inicio-solicitante thead{
  width: 100%;
  background-color: #F5F5F5;
  display: table-header-group;
}
.tabla-inicio-solicitante th{ 
  padding: 1.3rem;
  text-align: center;
  border-bottom: 2px solid #ddd;
}
.tabla-inicio-solicitante td{ 
  padding: 0.9375rem;
  text-align: center;
  border-bottom: 0.0625rem solid #ddd;
}
.colum-empresa-tabla-solicitante{
  max-width: 23.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow:ellipsis;
}
.tabla-inicio-solicitante tbody tr:hover{ 
  background-color: #f9f9f9;
  cursor: pointer;
}

.tabla-inicio-solicitante th:first-child {
  border-top-left-radius: 0.625rem;
}

.tabla-inicio-solicitante th:last-child {
  border-top-right-radius: 0.625rem;
}
.identificador-modelo{
  color: #294FCF;
  margin: auto;
  padding: 0 0.15625rem;
  margin-right: 0.15625rem;
  background-color: #294FCF;
  border-radius: 0.9375rem;
}
/*BARRA LATERAL*/
.barraLateral-inicio-solicitante{
  width: 20%;
  border: 0.0625rem solid #D9D9D9;
  background-color: #fff;
  padding: 1.3rem;
  border-radius: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: start;
  max-height: 39rem;
}
.titulo-barra-Lateral{
  font-size: 1.5rem;
  color: #4B4B4B;

}
.subtitulo-barra-Lateral{
  font-size: 1.3rem;
  margin-top: 0.9375rem;
  color: #294FCF;
  text-indent: 0.9375rem;
}
.subtitulo-barra-Lateral svg{
  cursor: pointer;
}
.nombre-solicitante-barra-lateral{
  font-size: 1rem;
  width: 98%;
  margin-left: 0.9375rem;
  margin-top: 0.3125rem;
}
.list-barra-Lateral{
  list-style: none;
  font-size: 1rem;
  width: 75%;
}
.list-barra-Lateral li{
  display: flex;
  justify-content: space-between;
  margin-top: 0.3125rem;
}
.opcionesSolicitud-barra-Lateral{
  display: flex;
  justify-content: left;
  align-items: center;
  gap: 0.625rem;
  margin-left: 1.3rem;
  margin-top: 0.9375rem;
}
.opcionesSolicitud-barra-Lateral i{
  color: #4B4B4B;
  cursor: pointer;
}
.opcionesSolicitud-barra-Lateral i:hover{
  color: #294FCF;
  cursor: pointer;
}
/* STEPPER HISTORIAL*/
.stepper-container {
  width: 300px;
  margin: 0 auto;
}

.stepper {
  list-style-type: none;
  padding: 0;
  position: relative;
  margin-top: 1.3rem;
  max-height: 31.508rem;
  overflow-y: auto;
  width: 100%;
}

.step {
  display: flex;
  align-items: flex-start;
  position: relative;
  margin-bottom: 1.3rem;
}

.step::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0.625rem;
  bottom: -0.625rem;
  width: 2px;
  height: 3.75rem;
  background-color: #ccc;
}

.step:last-child::before {
  display: none;
}

.circle {
  height: 0.75rem;
  width: 0.75rem;
  border-radius: 50%;
  background-color: #ccc;
  position: relative;
  z-index: 1;
  margin-right: 1.3rem;
  margin-left: 7px;
}

.text {
  color: #757575;
}

.text p {
  margin: 0;
  font-size: 1rem;
  color: #757575;
}

.text span {
  font-size: 0.75rem;
  color: #aaa;
}

.completed .circle {
  background-color: #ccc;
}

.active .circle {
  background-color: #333;
}

.active .text p, 
.active .text span {
  color: #333;
}
.div-estado{
  max-width:100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.9375rem;
}

.div-estado span{
  text-align: right;
  display: block;
  width: 100%;
}
.div-estado svg{
  text-align: right;
  margin-right: 1.9rem;
}
.paginacion-tabla-solicitante {
  display: flex;
  justify-content: flex-end;
  gap: 0.0625rem;
  margin-bottom: 1.3rem;
  width: 100%;

}

.numero-pagina {
  background-color: #f0f0f0;
  border: 0.0625rem solid #ccc;
  padding: 0.3125rem 0.625rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-radius: 0.3125rem;

}

.numero-pagina.activo {
  background-color: #007bff;
  color: white;
  border: 0.0625rem solid #007bff;
}

.numero-pagina:hover {
  background-color: #007bff;
  color: white;
}
.aprobadores-list-barra-laretal{
  margin-left: 2.5rem;
  color: #757575;
}
.aprobadores-list-barra-laretal ul{
  display: flex;
  justify-content: center;
  align-items: start;
  flex-direction: column;
  gap: 8px;
  font-size: 13px;
  padding-left: 0 !important;
  margin-bottom: 0.625rem;

}
.aprobadores-list-barra-laretal ul li{
  list-style: none;
  margin-left: 0.3125rem;
}
.barra-limpia{
  width: 100%;
  height: 38.5rem;
}
.contador-estados-inicio-solicitante{
  display: flex;
  justify-content: start;
  align-items: stretch;
  gap: 0.625rem;
  width: 100%;
  height: 100%;
}
.estado-contador-IS{
  width: 20%;
  max-width: 20%;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  gap: 0.3rem;
  padding: 1rem;
}
.valor-contador-estados-IS{
  font-size: 1.5rem;
  color: #272727;
  width: 100%;
  justify-content: start;
}
.color-estado-IS-Nuevas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #D9D9D9;
}
.color-estado-IS-Asignadas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #A6E0FF;
}
.color-estado-IS-EnProceso{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #EFF928;
}
.color-estado-IS-Aceptadas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #FFA20E;
}
.color-estado-IS-EnAprobacion{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #85EE85;
}
.color-estado-IS-Aprobadas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #0E880E;
}
.color-estado-IS-EnValidacion{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #FFD071;
}
.color-estado-IS-Firmadas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #156CFF;
}
.color-estado-IS-Retrasadas{
  width: 100%;
  height: 0.625rem;
  border-radius: 0.625rem;
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
  background-color: #e6483d;
}
.estado-flecha-estadisticas{
  width: 10%;
  max-width: 10%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  padding: 1rem;
  cursor: pointer;
}
.contenedor-footer-tabla{
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.625rem;
  width: 100%;
  height: 100%;
}
.boton-recargar{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.3125rem;
  min-width: 7rem; 
  height: 100%;
  cursor: pointer;
  border-radius: 0.625rem;
  border: 0.0625rem solid #294FCF;
  color: #294FCF;
  background-color: white;
  font-size: 1rem;
  padding: 0.5rem;
}
.boton-recargar:active{
  background-color: #294FCF;
  color: white;
}