import { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2"; // Cambiar de Pie a Bar
import ChartDataLabels from "chartjs-plugin-datalabels";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend,
} from "chart.js";
import { validateToken } from "../../../../../Components/Services/TokenService";
import { Modal } from "@mui/material";

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend,ChartDataLabels);

interface Grafico2Props {
  data: any;
  isLoading: boolean;
  selectedEmpresaFiltro: any;
}

const Grafico2: React.FC<Grafico2Props> = ({ data, isLoading,selectedEmpresaFiltro }) => {
  const [openModal, setOpenModal] = useState(false);
  const [expandedUnits, setExpandedUnits] = useState<{[key: string]: boolean}>({});
  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);

  const toggleUnit = (unitName: string) => {
    setExpandedUnits(prev => ({
      ...prev,
      [unitName]: !prev[unitName]
    }));
  };
  
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [
      {
        label: "Monto del contrato",
        data: [],
        backgroundColor: "rgba(44, 76, 179, 0.5)",
        borderColor: "rgba(44, 76, 179, 1)",
        borderWidth: 1,
      },
    ],
  });
  const [chartDataModal, setChartDataModal] = useState(chartData);

  useEffect(() => {
    const resumenData = data?.resumen;
    if (resumenData && resumenData.length > 0) {
      const labels = resumenData.map((item: any) => item.unidad_negocio);
      const chartDataValues = resumenData.map((item: any) => item.total_honorarios);
      const maxIndex = chartDataValues.indexOf(Math.max(...chartDataValues));

      const backgroundColorNormal = chartDataValues.map((_: any, index: number) =>
        index === maxIndex ? "#2C4CB3" : `rgba(200, 200, 200, ${0.5 + Math.random() * 0.5})`
      );

      const backgroundColorModal = chartDataValues.map((_: any, index: number) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgb(${Math.floor(Math.random() * 170)}, ${Math.floor(
              Math.random() * 170
            )}, ${Math.floor(Math.random() * 170)})`
      );
      setChartData({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorNormal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });

      setChartDataModal({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorModal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });
    }
  }, [data]);

  return (
    <div className="card-grafico-reportes-gestor">
      <div className="header-card-grafico-reportes-gestor lato-font">
        <div className="titulo-card" onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
          Top 10 presupuestos por unidad de negocio
        </div>
      </div>

      <div className="grafico-graficos-gestor">
        <Bar
          data={chartData}

          options={{
            responsive: true,
            maintainAspectRatio: false,
            aspectRatio: 1.2,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) => {
                    const dataset = tooltipItem.dataset;
                    const dataIndex = tooltipItem.dataIndex;
                    const value = dataset.data[dataIndex];
                    const total = dataset.data.reduce(
                      (acc, val) => acc + val,
                      0
                    );
                    const percentage = ((value / total) * 100).toFixed(0);
                    return `${percentage}%`;
                  },
                },
              },
              datalabels: {
                display: (context) => context.dataIndex === chartData.datasets[0].data.indexOf(Math.max(...chartData.datasets[0].data)),
                color: "#fff",
                font: {
                  weight: "bold",
                  size: 12,
                },
                formatter: (value, context) => {
                  const total = context.chart.data.datasets[0].data.reduce((acc, val) => acc + val, 0);
                  const percentage = ((value / total) * 100).toFixed(0);
                  return `${percentage}%`; // Mostrar porcentaje
                },
              },
            },
            scales: {
              x: {
                beginAtZero: true,
                grid: {
                  display: false
                }
              },
              y: {
                beginAtZero: true,
                grid: {
                  display: false
                }
              },
            },
          }}
        />
      </div>

      <Modal open={openModal} onClose={handleCloseModal}>
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
            <div className="titulo-card" onClick={() => handleOpenModal()}>
            Top 10 presupuestos por unidad de negocio
            </div>
          </div>
          
          <div className="grafico-pie-gestor-modal">
            <Bar
              data={chartDataModal}
              style={{maxWidth:'30rem',maxHeight:'20rem'}}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    callbacks: {
                      label: (tooltipItem) => {
                        const dataset = tooltipItem.dataset;
                        const dataIndex = tooltipItem.dataIndex;
                        const value = dataset.data[dataIndex];
                        const total = dataset.data.reduce(
                          (acc, val) => acc + val,
                          0
                        );
                        const percentage = ((value / total) * 100).toFixed(0);
                        return `${percentage}%`;
                      },
                    },
                  },
                  datalabels: {
                    display: true,
                    color: "#fff",
                    font: {
                      weight: "bold",
                      size: 12,
                    },
                    formatter: (value, context) => {
                      const total = context.chart.data.datasets[0].data.reduce((acc, val) => acc + val, 0);
                      const percentage = ((value / total) * 100).toFixed(0);
                      return `${percentage}%`; // Mostrar porcentaje
                    },
                  },
                },
                scales: {
                  x: {
                    beginAtZero: true,
                    grid: {
                      display: false
                    }
                  },
                  y: {
                    beginAtZero: true,
                    grid: {
                      display: false
                    }
                  },
                },
                
              }}
            />
            {/* Leyenda expandible por unidad de negocio */}
            <div className="leyenda-graficos-gestor">
              {chartDataModal.labels.map((label, index) => {
                const isExpanded = expandedUnits[label];
                const unitContracts = data?.detalle?.filter((item: any) => item.unidad_negocio === label) || [];

                return (
                  <div key={index} className="mb-2">
                    {/* Header de la unidad con flecha */}
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        padding: "8px",
                        backgroundColor: "#f8f9fa",
                        borderRadius: "4px",
                        border: "1px solid #dee2e6"
                      }}
                      onClick={() => toggleUnit(label)}
                    >
                      <div
                        style={{
                          width: "0.75rem",
                          height: "0.75rem",
                          backgroundColor: chartDataModal.datasets[0].backgroundColor[index],
                          borderRadius: "20%",
                          marginRight: "8px",
                        }}
                      />
                      <span className="texto-leyenda-graficos-gestor flex-grow-1">
                        {label}: {selectedEmpresaFiltro.moneda} {Number(chartDataModal.datasets[0].data[index]).toLocaleString("es-PE")}
                      </span>
                      <i
                        className={`fas ${isExpanded ? 'fa-chevron-up' : 'fa-chevron-down'}`}
                        style={{ marginLeft: "8px", fontSize: "12px", color: "#6c757d" }}
                      ></i>
                    </div>

                    {/* Lista de contratos expandible */}
                    {isExpanded && (
                      <div style={{
                        marginTop: "8px",
                        marginLeft: "16px",
                        maxHeight: "200px",
                        overflowY: "auto",
                        border: "1px solid #e9ecef",
                        borderRadius: "4px",
                        backgroundColor: "#ffffff"
                      }}>
                        <table className="table table-sm table-striped mb-0">
                          <thead className="table-light">
                            <tr>
                              <th style={{ fontSize: "11px", padding: "4px 8px" }}>Código</th>
                              <th style={{ fontSize: "11px", padding: "4px 8px", textAlign: "right" }}>Honorarios</th>
                            </tr>
                          </thead>
                          <tbody>
                            {unitContracts.map((contract: any, contractIndex: number) => (
                              <tr key={contractIndex}>
                                <td style={{ fontSize: "10px", padding: "4px 8px" }}>{contract.codigo_contrato}</td>
                                <td style={{ fontSize: "10px", padding: "4px 8px", textAlign: "right" }}>
                                  {contract.str_SimboloMoneda} {Number(contract.honorarios).toLocaleString("es-PE")}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

           
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Grafico2;
