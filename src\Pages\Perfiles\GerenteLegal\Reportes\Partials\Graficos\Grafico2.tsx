import { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2"; // Cambiar de Pie a Bar
import ChartDataLabels from "chartjs-plugin-datalabels";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend,
} from "chart.js";
import { validateToken } from "../../../../../Components/Services/TokenService";
import { Modal } from "@mui/material";

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend,ChartDataLabels);

interface Grafico2Props {
  data: any;
  isLoading: boolean;
  selectedEmpresaFiltro: any;
}

const Grafico2: React.FC<Grafico2Props> = ({ data, isLoading,selectedEmpresaFiltro }) => {
  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);
  
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [
      {
        label: "Monto del contrato",
        data: [],
        backgroundColor: "rgba(44, 76, 179, 0.5)",
        borderColor: "rgba(44, 76, 179, 1)",
        borderWidth: 1,
      },
    ],
  });
  const [chartDataModal, setChartDataModal] = useState(chartData);

  useEffect(() => {
    const resumenData = data?.resumen;
    if (resumenData && resumenData.length > 0) {
      const labels = resumenData.map((item: any) => item.unidad_negocio);
      const chartDataValues = resumenData.map((item: any) => item.total_honorarios);
      const maxIndex = chartDataValues.indexOf(Math.max(...chartDataValues));

      const backgroundColorNormal = chartDataValues.map((_: any, index: number) =>
        index === maxIndex ? "#2C4CB3" : `rgba(200, 200, 200, ${0.5 + Math.random() * 0.5})`
      );

      const backgroundColorModal = chartDataValues.map((_: any, index: number) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgb(${Math.floor(Math.random() * 170)}, ${Math.floor(
              Math.random() * 170
            )}, ${Math.floor(Math.random() * 170)})`
      );
      setChartData({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorNormal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });

      setChartDataModal({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data: chartDataValues,
            backgroundColor: backgroundColorModal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });
    }
  }, [data]);

  return (
    <div className="card-grafico-reportes-gestor">
      <div className="header-card-grafico-reportes-gestor lato-font">
        <div className="titulo-card" onClick={() => handleOpenModal()}  style={{cursor:'pointer'}}>
          Top 10 presupuestos por unidad de negocio
        </div>
      </div>

      <div className="grafico-graficos-gestor">
        <Bar
          data={chartData}

          options={{
            responsive: true,
            maintainAspectRatio: false,
            aspectRatio: 1.2,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) => {
                    const dataset = tooltipItem.dataset;
                    const dataIndex = tooltipItem.dataIndex;
                    const value = dataset.data[dataIndex];
                    const total = dataset.data.reduce(
                      (acc, val) => acc + val,
                      0
                    );
                    const percentage = ((value / total) * 100).toFixed(0);
                    return `${percentage}%`;
                  },
                },
              },
              datalabels: {
                display: (context) => context.dataIndex === chartData.datasets[0].data.indexOf(Math.max(...chartData.datasets[0].data)),
                color: "#fff",
                font: {
                  weight: "bold",
                  size: 12,
                },
                formatter: (value, context) => {
                  const total = context.chart.data.datasets[0].data.reduce((acc, val) => acc + val, 0);
                  const percentage = ((value / total) * 100).toFixed(0);
                  return `${percentage}%`; // Mostrar porcentaje
                },
              },
            },
            scales: {
              x: {
                beginAtZero: true,
                grid: {
                  display: false
                }
              },
              y: {
                beginAtZero: true,
                grid: {
                  display: false
                }
              },
            },
          }}
        />
      </div>

      <Modal open={openModal} onClose={handleCloseModal}>
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
            <div className="titulo-card" onClick={() => handleOpenModal()}>
            Top 10 presupuestos por unidad de negocio
            </div>
          </div>
          
          <div className="grafico-pie-gestor-modal">
            <Bar
              data={chartDataModal}
              style={{maxWidth:'30rem',maxHeight:'20rem'}}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    callbacks: {
                      label: (tooltipItem) => {
                        const dataset = tooltipItem.dataset;
                        const dataIndex = tooltipItem.dataIndex;
                        const value = dataset.data[dataIndex];
                        const total = dataset.data.reduce(
                          (acc, val) => acc + val,
                          0
                        );
                        const percentage = ((value / total) * 100).toFixed(0);
                        return `${percentage}%`;
                      },
                    },
                  },
                  datalabels: {
                    display: true,
                    color: "#fff",
                    font: {
                      weight: "bold",
                      size: 12,
                    },
                    formatter: (value, context) => {
                      const total = context.chart.data.datasets[0].data.reduce((acc, val) => acc + val, 0);
                      const percentage = ((value / total) * 100).toFixed(0);
                      return `${percentage}%`; // Mostrar porcentaje
                    },
                  },
                },
                scales: {
                  x: {
                    beginAtZero: true,
                    grid: {
                      display: false
                    }
                  },
                  y: {
                    beginAtZero: true,
                    grid: {
                      display: false
                    }
                  },
                },
                
              }}
            />
            <div className="leyenda-graficos-gestor">
              {chartDataModal.labels.map((label, index) => (
                <div
                  key={index}
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div
                    style={{
                      width: "0.75rem",
                      height: "0.75rem",
                      backgroundColor:
                      chartDataModal.datasets[0].backgroundColor[index],
                      borderRadius: "20%",
                      marginRight: "8px",
                    }}
                  />
                  <span className="texto-leyenda-graficos-gestor">
                  {label}: {selectedEmpresaFiltro.moneda} {Number(chartDataModal.datasets[0].data[index]).toLocaleString("es-PE")}
                  </span>
                </div>
              ))}
            </div>

           
          </div>
           {/* Tabla de detalle agrupada por unidad de negocio */}
            <div className="mt-4">
              <h6 className="mb-3">Detalle por Unidad de Negocio</h6>
              <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                {data?.detalle && (() => {
                  // Agrupar datos por unidad de negocio
                  const groupedData = data.detalle.reduce((acc: any, item: any) => {
                    const unidad = item.unidad_negocio;
                    if (!acc[unidad]) {
                      acc[unidad] = [];
                    }
                    acc[unidad].push(item);
                    return acc;
                  }, {});

                  return Object.keys(groupedData).map((unidadNegocio, groupIndex) => (
                    <div key={groupIndex} className="mb-4">
                      {/* Encabezado de la unidad de negocio */}
                      <div className="bg-light p-2 rounded mb-2">
                        <h6 className="mb-0 text-primary fw-bold">{unidadNegocio}</h6>
                        <small className="text-muted">
                          {groupedData[unidadNegocio].length} contrato(s) -
                          Total: {groupedData[unidadNegocio][0]?.str_SimboloMoneda} {
                            groupedData[unidadNegocio]
                              .reduce((sum: number, item: any) => sum + Number(item.honorarios), 0)
                              .toLocaleString("es-PE")
                          }
                        </small>
                      </div>

                      {/* Tabla de contratos para esta unidad */}
                      <table className="table table-sm table-bordered">
                        <thead className="table-secondary">
                          <tr>
                            <th>Código Contrato</th>
                            <th>Tipo Solicitud</th>
                            <th>Honorarios</th>
                          </tr>
                        </thead>
                        <tbody>
                          {groupedData[unidadNegocio].map((item: any, index: number) => (
                            <tr key={index}>
                              <td>{item.codigo_contrato}</td>
                              <td>{item.tipo_solicitud}</td>
                              <td className="text-end">
                                {item.str_SimboloMoneda} {Number(item.honorarios).toLocaleString("es-PE")}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ));
                })()}
              </div>
            </div>
        </div>
      </Modal>
    </div>
  );
};

export default Grafico2;
