import React, { useEffect, useState } from "react";
import ConteoSolicitudes from "./ConteoSolicitudes";
import Aceptado from "./Estados/Aceptado";
import OtrosEstados from "./Estados/OtrosEstados";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import EnProceso from "./Estados/EnProceso";
import Aprobado from "./Estados/Aprobado";
import Firmado from "./Estados/Firmado";
import Asignado from "./Estados/Asignado";
import Nuevo from "./Estados/Nuevo";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
import BarraLimpia from "../../../Solicitante/Partials/BarraLateral/BarraLimpia";

const BarraLateral = ({
  solicitudes,
  pagesolicitudes,
  estado,
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  Suscriptor,
  perfil,
  CambiarBarraLateral,
  tipoController
  
}) => {
  const [aprobadores, setAprobadores] = useState([]);
  const [documentoSubido,setDocumentoSubido] = useState(false);
  const token = Cookies.get("Token");

  const obtenerAprobadores = async () => {
await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ListarAprobadores"](
          Suscripcion,
          solicitudSeleccionada.int_idSolicitudes
        ),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );
      const aprobadores = response.data;
      setAprobadores(aprobadores);
    } catch (error) {
      console.error("Error al obtener aprobadores:", error);
    }
  };
  const handleComprobarContrato = async () => {
await validateToken();
    try {
      

      const response = await axios.get(API_GESTOR["ListarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if(response.data || (response.status > 200 && response.status < 300)){

        setDocumentoSubido(true)
      }else{
        setDocumentoSubido(false)

      }
    } catch (error) {
      setDocumentoSubido(false)
      return error
    }
  };
  useEffect(() => {
    obtenerAprobadores();
    handleComprobarContrato();
  }, [solicitudSeleccionada]);

  return (
    <>
      {estado === "Aceptado" ? (
        <Aceptado
          historiales={historiales}
          solicitudSeleccionada={solicitudSeleccionada}
          Suscripcion={Suscripcion}
          idAplicacion={idAplicacion}
          idUsuario={idUsuario}
          SubmitObtenerDatos={SubmitObtenerDatos}
          setSelectedSolicitud={setSelectedSolicitud}
          aprobadores={aprobadores}
        />
      ) : estado === "En Proceso" ? (
        <EnProceso
          solicitudSeleccionada={solicitudSeleccionada}
          historiales={historiales}
          aprobadores={aprobadores}
          Suscripcion={Suscripcion}
          idUsuario={idUsuario}
          SubmitObtenerDatos={SubmitObtenerDatos}
          setSelectedSolicitud={setSelectedSolicitud}
          Suscriptor={Suscriptor}
          documentoSubido={documentoSubido}
          CambiarBarraLateral={CambiarBarraLateral}
          handleComprobarContrato = {handleComprobarContrato}
        />
      ) : estado === "En Aprobación" ||
        estado === "En Validación" ? (
        <OtrosEstados
          solicitudSeleccionada={solicitudSeleccionada}
          historiales={historiales}
          aprobadores={aprobadores}
          Suscripcion={Suscripcion}
          idAplicacion={idAplicacion}
          idUsuario={idUsuario}
         />
      ) : estado === "Aprobado" ? (
        <Aprobado
          solicitudSeleccionada={solicitudSeleccionada}
          historiales={historiales}
          aprobadores={aprobadores}
          Suscripcion={Suscripcion}
          idAplicacion={idAplicacion}
          idUsuario={idUsuario}
          SubmitObtenerDatos={SubmitObtenerDatos}
          setSelectedSolicitud={setSelectedSolicitud}
        />
      ) : estado === "Firmado" ? (
        <Firmado
          solicitudSeleccionada={solicitudSeleccionada}
          historiales={historiales}
          aprobadores={aprobadores}
          Suscripcion={Suscripcion}
          idAplicacion={idAplicacion}
          idUsuario={idUsuario}
          SubmitObtenerDatos={SubmitObtenerDatos}
          setSelectedSolicitud={setSelectedSolicitud}
        />
      ): estado === "Asignado" ? (
        <Asignado
        solicitudSeleccionada={solicitudSeleccionada}
        historiales={historiales}
        aprobadores={aprobadores}
        Suscripcion={Suscripcion}
        idAplicacion={idAplicacion}
        idUsuario={idUsuario}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
        Suscriptor={Suscriptor}
        tipoController={tipoController}
        perfil={perfil}
        />
      ):perfil === "Gestor Controller" ?estado === "Nuevo" ? (
        <Nuevo
        solicitudSeleccionada={solicitudSeleccionada}
        idUsuario={idUsuario}
        obtenerSolicitudesUsuario={SubmitObtenerDatos}
        Suscripcion={Suscripcion}
        idAplicacion={idAplicacion}
        setSelectedSolicitud={setSelectedSolicitud}
        
        />
      )
      :
      (
        <div className={`conteo-inicio-gestor-pageSolicitudes`}>
          </div>
      )
       :
       (
        <div className={`conteo-inicio-gestor-pageSolicitudes`}>
          </div>
      )}
    </>
  );
};

export default BarraLateral;
