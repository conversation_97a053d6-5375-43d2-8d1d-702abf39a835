import { Tooltip } from "@mui/material";
import axios from "axios"; // Para realizar solicitudes HTTP
import { useState } from "react"; // Para manejar el estado local
import ModalGestores from "../../../../Solicitante/Partials/Modals/ModalGestores";
import SolicitanteAsignado from "../SolicitanteAsignado";


// Definición de tipos para las props
interface SolicitudSeleccionada {
  int_idSolicitudes: number;
  str_CodSolicitudes: string;
  nombre_TipoSolicitud: string;
}

interface OtrosEstadosProps {
  solicitudSeleccionada: SolicitudSeleccionada;
  idUsuario: string | number;
  obtenerSolicitudesUsuario: () => void;
  Suscripcion: string | number;
  idAplicacion:string | number;
  setSelectedSolicitud:  () => void
}

const Nuevo: React.FC<OtrosEstadosProps> = ({
  solicitudSeleccionada,
  idUsuario,
  obtenerSolicitudesUsuario,
  Suscripcion,
  idAplicacion,
  setSelectedSolicitud
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAsignar: false,
  });

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };


  return (
    <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Reasignar Gestor" placement="top">
        <i className="fa-solid fa-user-plus" onClick={() => toggleModal('isModalVisibleAsignar')}></i>
        </Tooltip>
      </div>
      <SolicitanteAsignado solicitudSeleccionada={solicitudSeleccionada} />

        
        <ModalGestores
        isModalVisibleAsignar={modales.isModalVisibleAsignar}
        CerrarAsignar={() => toggleModal('isModalVisibleAsignar')}
        idAplicacion={idAplicacion}
        Suscripcion={Suscripcion}
        idSolicitud={solicitudSeleccionada.int_idSolicitudes}
        idUsuario={idUsuario}
        SubmitObtenerDatos={obtenerSolicitudesUsuario}
        setSelectedSolicitud={setSelectedSolicitud}
        Controller={true}
      />
      </div>
  );
};

export default Nuevo;