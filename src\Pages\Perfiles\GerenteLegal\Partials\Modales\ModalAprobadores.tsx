import React, { useEffect, useState } from "react";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import Swal from "sweetalert2";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";

function formatDate(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();
  return `${day} de ${month} de ${year}`;
}

const ModalAprobadores = ({
  isModalVisible,
  CerrarModal,
  idAplicacion,
  Suscripcion,
  solicitudSeleccionada,
  historiales,
  fecha,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
}) => {
  const [aprobadores, setAprobadores] = useState([]);
  const token = Cookies.get("Token");

  const [seleccionados, setSeleccionados] = useState([]);
  // Obtener aprobadores desde la API
  useEffect(() => {
    const obtenerGestores = async () => {
await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerAprobadores"](Suscripcion, idAplicacion),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const respuesta = response.data;
        setAprobadores(respuesta);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    obtenerGestores();
  }, [Suscripcion, idAplicacion]);

  const solicitudAceptada = historiales.find(
    (historial) => historial.Nombre_estado === "Aceptado"
  );

  const manejarSeleccion = (idAprobador) => {
    setSeleccionados((prevSeleccionados) => {
      if (prevSeleccionados.includes(idAprobador)) {
        return prevSeleccionados.filter((id) => id !== idAprobador);
      } else {
          return [...prevSeleccionados, idAprobador];

      }
    });
  };
  const insertarAprobadores = async () => {
await validateToken();

    try {
      const aprobadoresPromises = seleccionados.map((idAprobador, index) => {
        const aprobadorSeleccionado = aprobadores.find(
          (aprobador) => aprobador.int_idUsuario === idAprobador
        );
        const data = {
          str_idSuscripcion: Suscripcion,
          int_idUsuario: aprobadorSeleccionado.int_idUsuario,
          int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
          int_OrdenAprobacion: index + 1,
          int_idUsuarioCreacion: idUsuario,
        };

        return axios.post(API_GESTOR["InsertarAprobador"](), data,{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        });
      });

      await Promise.all(aprobadoresPromises);

      const responseCambiarEstado = await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: "En Aprobación",
          int_idUsuarioCreacion: idUsuario,
          int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
        },{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );

      if (
        responseCambiarEstado.status >= 200 &&
        responseCambiarEstado.status < 300
      ) {
        Swal.fire(
          "",
          "Se asignaron correctamente los aprobadores",
          "success"
        );
        SubmitObtenerDatos();
        setSelectedSolicitud({});
        CerrarModal();
      }
    } catch (error) {
      console.error("Error al insertar aprobadores:", error);
      Swal.fire("", "No se pudo insertar los aprobadores", "error");
    }
  };
  return (
    <>
      {isModalVisible && (
        <div className="modal-aprobadores">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarModal}
            ></button>
          </div>
          <div className="titulo-modal-aprobadores">
            <span className="lato-font">
              Aprobación de la solicitud <br />{" "}
              <strong>{solicitudSeleccionada.str_CodSolicitudes}</strong>
            </span>
          </div>
          <div className="subtitulo-modal-aprobadores">
            <span className="lato-font">Aceptado por:</span>
          </div>
          <ul className="lista-solicitante-gestor-aprobantes montserrat-font">
            <li>
              Gestor: <span>{formatDate(fecha)}</span>
            </li>
            <li>
              Solicitante:{" "}
              <span>{formatDate(solicitudAceptada.fecha_Cambio)}</span>
            </li>
          </ul>

          <div className="subtitulo-modal-aprobadores">
            <span className="lato-font">Siguientes Aprobadores:</span>
          </div>
          <div className="lista-aprobadores-checkbox">
            {aprobadores.map((aprobador, index) => (
              <div className="form-check" key={aprobador.int_idUsuario}>
                <input
                  className="form-check-input"
                  type="checkbox"
                  value=""
                  id={`aprobador-${aprobador.int_idUsuario}`}
                  checked={seleccionados.includes(aprobador.int_idUsuario)}
                  onChange={() => manejarSeleccion(aprobador.int_idUsuario)}
                />
                <label
                  className="form-check-label"
                  htmlFor={`aprobador-${aprobador.int_idUsuario}`}
                >
                  {aprobador.str_Nombres} {aprobador.str_Apellidos} -{" "}
                  {aprobador.nombre_especialidad}
                </label>
                {seleccionados.includes(aprobador.int_idUsuario) && (
                  <span className="orden-seleccionado">
                    {seleccionados.indexOf(aprobador.int_idUsuario) + 1}
                  </span>
                )}
              </div>
            ))}
          </div>
          <div className="botones-aprobadores">
            <div className="container-botones">
              <button className="btn btn-outline-primary" onClick={CerrarModal}>
                {" "}
                Cancelar
              </button>
              <button
                className="btn btn-primary"
                onClick={() => insertarAprobadores()}
              >
                Confirmar Solicitud
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalAprobadores;
