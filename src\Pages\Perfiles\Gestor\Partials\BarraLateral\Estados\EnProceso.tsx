import React, { useRef, useState, useEffect } from "react";
import Swal from "sweetalert2";
import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";
import ModalAceptar from "../../Modales/ModalAceptar";
import ModalAprobadores from "../../Modales/ModalAprobadores";
import axios from "axios";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import ModalTags from "../../Modales/ModalTags";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import IconoDocFirmado from "../../../../../../assets/SVG/IconoDocFirmado";
import IconoDocNoFirmado from "../../../../../../assets/SVG/IconoDocNoFirmado";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import IconoEditar from "../../../../../../assets/SVG/IconoEditar";
import IconoDownload from "../../../../../../assets/SVG/IconoDownload";
import IconoVer from "../../../../../../assets/SVG/IconoVer";

interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number | any;
    str_CodSolicitudes: string;
    str_DeTerceros: string;
    str_CodTipoSol: string;
    nombre_TipoSolicitud: string;
    int_idEmpresa: number;
  };
  historiales: any[];
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number | any;
  SubmitObtenerDatos: () => void;
  setSelectedSolicitud: any;
  Suscriptor: string;
  aprobadores: any[];
  documentoSubido: boolean;
}

const EnProceso: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  Suscriptor,
  aprobadores,
  documentoSubido,
  CambiarBarraLateral,
  handleComprobarContrato,
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAceptar: false,
    isModalVisible: false,
    isModalVisibleTags: false,
  });
  const token = Cookies.get("Token");

  const [file, setFile] = useState<File | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const navigate = useNavigate();
  const AbrirModalTags = () => {
    modales.isModalVisibleTags = true;
  };
  const CerrarModalTags = () => {
    modales.isModalVisibleTags = false;
    CambiarBarraLateral(solicitudSeleccionada);
    handleComprobarContrato();
  };
  const handleFileUploadConfirmation = (file: File) => {
    Swal.fire({
      title: "",
      text: `Está seguro de sobreescribir el documento cargado?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, subirlo",
    }).then((result) => {
      if (result.isConfirmed) {
        handleButtonClick();
      }
    });
  };
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const selectedFile = event.target.files[0];

      const validExtensions = [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];
      const fileType = selectedFile.type;

      if (!validExtensions.includes(fileType)) {
        Swal.fire("", "Solo se permiten archivos Word (.doc, .docx)", "error");
        event.target.value = ""; 
        return;
      }
      setFile(event.target.files[0]);
      event.target.value = "";
    }
  };

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };

  const AsignarAprobadores = () => {
    toggleModal("isModalVisibleAceptar");
    toggleModal("isModalVisible");
  };

  const handleDownload = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ListarPlantilla"](
          Suscripcion,
          solicitudSeleccionada.str_CodTipoSol,
          solicitudSeleccionada.int_idEmpresa,
          "Contratos"
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.data) {
        const response2 = await axios.get(
          API_GESTOR["DescargarPlantilla"](
            Suscripcion,
            solicitudSeleccionada.str_CodTipoSol,
            solicitudSeleccionada.int_idEmpresa,
            "Contratos"
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            responseType: "blob",
          }
        );
        const contentDisposition = response2.headers["content-disposition"];
        const filename = contentDisposition
          ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
          : `${response.data.nombre_archivo}`;

        const url = window.URL.createObjectURL(new Blob([response2.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };

  useEffect(() => {
    const handleSubmitFiles = async () => {
      await validateToken();
      if (!file) {
        Swal.fire("", "Por favor, selecciona un archivo primero", "error");
        return;
      }

      try {
        const formDataSolicitud = new FormData();
        formDataSolicitud.append("archivo", file);
        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          solicitudSeleccionada.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          solicitudSeleccionada.int_idSolicitudes
        );
        formDataSolicitud.append("str_CodTipoDocumento", "COAP");
        formDataSolicitud.append("int_idUsuarioCreacion", idUsuario);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status >= 200 && response.status < 300) {
          setFile(null);

          Swal.fire("", "Archivo subido correctamente", "success");
          AbrirModalTags();
          SubmitObtenerDatos();
          CambiarBarraLateral(solicitudSeleccionada);
          handleComprobarContrato();
        } else {
          throw new Error("No se pudo ingresar el archivo");
        }
      } catch (error) {
        
        Swal.fire("", "No se pudo subir el archivo", "error");
      }
    };

    if (file) {
      handleSubmitFiles();
    }
  }, [file, Suscripcion, solicitudSeleccionada, idUsuario]);
  useEffect(() => {
    if (tags.length > 0) {
      GuardarTags();
    }
  }, [tags]);
  const GuardarTags = async () => {
    await validateToken();
    const token = Cookies.get("Token");

    try {
 
      await axios.delete(
        API_GESTOR["EliminarTags"](solicitudSeleccionada.int_idSolicitudes)
      ,{
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      for (const tag of tags) {
        const response = await axios.post(
          API_GESTOR["IngresarTags"](),
          {
            int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
            str_descripcion: tag,
            int_idUsuarioCreacion: idUsuario,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        
      }
      setTags([]);
      setModales((prev) => ({ ...prev, isModalVisibleTags: false }));
      SubmitObtenerDatos();
      CambiarBarraLateral(solicitudSeleccionada);
      handleComprobarContrato();
    } catch (error) {
      console.error("Error al enviar los tags:", error);
     }
  };
  const handleEditarSolicitud = () => {
    navigate(RoutesPrivate.EDITARSOLICITUD, {
      state: {
        solicitudSeleccionada,
        Suscripcion,
        idAplicacion,
        idUsuario,
        Suscriptor,
        documentoSubido,
      },
    });
  };
      const handleVerSolicitud = () => {
      navigate(RoutesPrivate.EDITARSOLICITUD, {
        state: {
          solicitudSeleccionada,
          Suscripcion,
          idAplicacion,
          idUsuario,
          Suscriptor,
          ver: true,
        },
      });
    };
  return (
    <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Editar Solicitud" placement="top">
          <div className="icono-barralateral-acciones" onClick={handleEditarSolicitud}>
          <IconoEditar size={"1.3rem"} color={"#000"}/>
          </div>
        </Tooltip>
           <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
        {solicitudSeleccionada.str_DeTerceros === "si" ? (
          ""
        ) : (
          <Tooltip title="Descargar Plantilla" placement="top">
                      <div className="icono-barralateral-acciones" onClick={handleDownload}>

           <IconoDownload size={"1.3rem"} color={"#000"}/>
            </div>
          </Tooltip>
        )}

        {documentoSubido ? (
                                <div className="icono-barralateral-acciones"  >

          <IconoDocFirmado
            onClick={
              documentoSubido ? handleFileUploadConfirmation : handleButtonClick
            }
            
          />
           </div>
        ) : (
          <div className="icono-barralateral-acciones"  >

          <IconoDocNoFirmado
            onClick={
              documentoSubido ? handleFileUploadConfirmation : handleButtonClick
            }
          />
            </div>
        )}
      </div>
      <input
        ref={fileInputRef}
        className="form-control"
        type="file"
        accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores} />

      <ModalAceptar
        isModalVisibleAceptar={modales.isModalVisibleAceptar}
        CerrarModalEliminar={() => toggleModal("isModalVisibleAceptar")}
        solicitudSeleccionada={solicitudSeleccionada}
        accion={AsignarAprobadores}
      />
      <ModalAprobadores
        isModalVisible={modales.isModalVisible}
        CerrarModal={() => toggleModal("isModalVisible")}
        Suscripcion={Suscripcion}
        idAplicacion={idAplicacion}
        solicitudSeleccionada={solicitudSeleccionada}
        historiales={historiales}
        fecha={new Date().toISOString()}
        idUsuario={idUsuario}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
      />
      <ModalTags
        isModalVisibleTags={modales.isModalVisibleTags}
        CerrarModalTags={CerrarModalTags}
        setTags={setTags}
        accion={GuardarTags}
      />
    </div>
  );
};

export default EnProceso;
