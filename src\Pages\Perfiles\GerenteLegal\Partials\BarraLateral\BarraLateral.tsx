import React, { useEffect, useState } from "react";
import ConteoSolicitudes from "./ConteoSolicitudes";
import OtrosEstados from "./Estados/OtrosEstados";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import EnAprobacion from "./Estados/EnAprobacion";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";


const BarraLateral = ({
  solicitudes,
  pagesolicitudes,
  estado,
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  Suscriptor
  
}) => {
  const [aprobadores, setAprobadores] = useState([]);
  const token = Cookies.get("Token");

  const obtenerAprobadores = async () => {
await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ListarAprobadores"](
          Suscripcion,
          solicitudSeleccionada.int_idSolicitudes
        ),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );
      const aprobadores = response.data;
      setAprobadores(aprobadores);
    } catch (error) {
      return error    
}
  };
  useEffect(() => {
    obtenerAprobadores();
  }, [solicitudSeleccionada]);

  return (
    <>
      {estado === "Aceptado" || estado === "En Proceso" ||estado === "Asignado" ||
        estado === "En Validación" ||  estado === "Aprobado" || estado === "Firmado" ? (
        <OtrosEstados
        solicitudSeleccionada={solicitudSeleccionada}
        historiales={historiales}
        aprobadores={aprobadores}
        Suscripcion={Suscripcion}
        idUsuario={idUsuario}
        Suscriptor={Suscriptor}
          idAplicacion={idAplicacion}
        />
      ) : estado === "En Aprobación"?(
        <EnAprobacion
        solicitudSeleccionada={solicitudSeleccionada}
        aprobadores={aprobadores}
        Suscripcion={Suscripcion}
        idUsuario={idUsuario}
        Suscriptor={Suscriptor}
        SubmitObtenerDatos={SubmitObtenerDatos}
        obtenerAprobadores={obtenerAprobadores}
        setSelectedSolicitud={setSelectedSolicitud}
        idAplicacion={idAplicacion}
        />
      ):
      (
        <div className={`conteo-inicio-gestor-pageSolicitudes`}>
          </div>
      )
    }
    </>
  );
};

export default BarraLateral;
