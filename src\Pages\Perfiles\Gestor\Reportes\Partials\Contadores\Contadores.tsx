const Contadores = ({ data, isLoading }) => {
    // Si se pasan datos optimizados, usarlos
    const PromedioAtencion = data?.promedioAtencion || {};
    const PromedioPreparacion = data?.promedioPreparacion || {};
    const PromedioTotal = data?.promedioTotal || {};
    const SolicitudesTotal = data?.solicitudesTotal || {};
    const SolicitudesFirmadas = data?.contratosFirmados || {};

    const PromedioAtencionDias = PromedioAtencion?.total ? (PromedioAtencion.total / 24).toFixed(1) : 0;
    const PromedioPreparacionDias = PromedioPreparacion?.total ? (PromedioPreparacion.total / 24).toFixed(1) : 0;
    console.log(PromedioPreparacionDias)
    const PromedioTotalDias = PromedioTotal?.total ? (PromedioTotal.total / 24).toFixed(1) : 0;
  
  return (
    <div className="container-contador-gestor">

    <div className="contador-estados-inicio-solicitante montserrat-font">
      <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Promedio total de atención</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{PromedioAtencionDias}</div>
          <div className='titulo-conteo'>días</div>
          

        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Tiempo Promedio de firma</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{PromedioPreparacionDias}</div>
          <div className='titulo-conteo'>días</div>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Tiempo promedio total </div>
          <div className="valor-contador-estados-IS montserrat-font-500">{PromedioTotalDias}</div>
          <div className='titulo-conteo'>días</div>
          
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Contratos trabajados por año </div>
          <div className="valor-contador-estados-IS montserrat-font-500">{SolicitudesTotal.total}</div>
          <div className='titulo-conteo'>Contratos</div>
         
        </div>
        <div className="estado-contador-IS" >
          <div className='titulo-conteo-header'> Contratos Firmados </div>
          <div className="valor-contador-estados-IS montserrat-font-500"> {SolicitudesFirmadas ? SolicitudesFirmadas.total_contratos_firmados : "0"}</div>
          <div className='titulo-conteo'>Contratos</div>

        </div>
    
 
      
          </div>
          </div>
  )
}

export default Contadores
