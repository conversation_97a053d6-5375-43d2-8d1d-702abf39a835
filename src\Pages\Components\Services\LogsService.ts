import axios from 'axios'
import { decrypt, getTokenFromCookie } from './TokenService'
import Cookies from 'js-cookie'
const base_url = import.meta.env.VITE_SEGURIDAD_URL

const getLogs = async (json_valorNuevo: string|number | null, json_valorAnterior: string|number | null, int_idRegistro: number | null, str_pagina: string | null, str_tabla: string | null, str_operacion: string | null, str_modulo: string | null, str_request: string | null) => {
    const Suscripcion = decrypt(Cookies.get("suscripcion"));
    const IdUsuario = decrypt(Cookies.get("hora_llegada"));

    // Crear objeto base
    const requestBody: any = {
        str_idSuscripcion: Suscripcion,
        int_idUsuarios: IdUsuario,
    };

     if (json_valorNuevo !== null) requestBody.json_valorNuevo = json_valorNuevo;
    if (json_valorAnterior !== null) requestBody.json_valorAnterior = json_valorAnterior;
    if (int_idRegistro !== null) requestBody.int_idRegistro = int_idRegistro;
    if (str_pagina !== null) requestBody.str_pagina = str_pagina;
    if (str_tabla !== null) requestBody.str_tabla = str_tabla;
    if (str_operacion !== null) requestBody.str_operacion = str_operacion;
    if (str_modulo !== null) requestBody.str_modulo = str_modulo;
    if (str_request !== null) requestBody.str_request = str_request;

    try {
        const response = await axios.post(
            base_url + 'logs/',
            requestBody,
            {
                headers: {
                    Authorization: `Bearer ${getTokenFromCookie()}`
                }
            }
        );
        if(response.status >= 200 && response.status < 300){
            return true;
        }
    } catch {
        return null;
    }
}

export default getLogs