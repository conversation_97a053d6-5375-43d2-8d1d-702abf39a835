import React from "react";

interface Solicitud {
    int_idSolicitudes: number;
    str_CodSolicitudes: string;
  }
  
  interface ModalEliminarProps {
    isModalVisibleAceptar: boolean;
    CerrarModalEliminar: () => void;
    solicitudSeleccionada: Solicitud;
    SubmitObtenerDatos: () => void;
    accion: () => void;
    handleRechazarSolicitud:()=>void
  }
  
  const ModalAceptar: React.FC<ModalEliminarProps> = ({
    isModalVisibleAceptar,
    CerrarModalEliminar,
    solicitudSeleccionada,
    accion,
    handleRechazarSolicitud
  }) => {
    
  
    return (
      <>
        {isModalVisibleAceptar && (
          <div className="modal-aceptar-solicitante">
            <div className="boton-cerrar-modal-filtros">
              <button
                type="button"
                className="btn-close"
                aria-label="Close"
                onClick={CerrarModalEliminar}
              ></button>
            </div>
            <div className="pregunta-modal-solicitante lato-font">
              <span>
                ¿Seguro que quieres aceptar la solicitud{" "}
                <strong>{solicitudSeleccionada.str_CodSolicitudes}</strong>?
              </span>
            </div>
            <div className="botones-modal-solicitante">
              <button
                className="btn btn-outline-primary"
                onClick={handleRechazarSolicitud}
              >
                Rechazar
              </button>
              <button
                className="btn btn-primary"
                onClick={accion}
              >
                Aceptar
              </button>
            </div>
          </div>
        )}
      </>
    );
  };
  
  export default ModalAceptar;