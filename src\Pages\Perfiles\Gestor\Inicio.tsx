import React, { useEffect, useState } from "react";
import Header from "../../Components/Partials/Header/Header";
import Cookies from "js-cookie";
import Titulo from "../../Components/Partials/Seccion/Titulo";
import "./Inicio.css";
import axios from "axios";
import API_GESTOR from "../../../assets/Api/ApisGestor";
import CircleEstado from "../../../assets/SVG/CircleEstado";
import { Tooltip } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../Security/Routes/ProtectedRoute";
import Promedios from "./Partials/Promedios/Promedios";
import ConteoSolicitudes from "./Partials/BarraLateral/ConteoSolicitudes";
import Tabla from "./TablaSolicitudes/Tabla";
import { decrypt } from "../../Components/Services/TokenService";
import getLogs from "../../Components/Services/LogsService";

function formatDate(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}

const Inicio = ({ perfil }) => {
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const [solicitudes, setSolicitudes] = useState([]);
  const navigate = useNavigate()
  const token = Cookies.get("Token");

  useEffect(() => {
    const obtenerSolicitudesUsuario = async () => {
     
      try {
        const response = await axios.get(
          perfil === "Gestor Controller" ?    API_GESTOR["ObtenerSolicitudesGestor"](Suscripcion, idUsuario)       
            : API_GESTOR["ObtenerSolicitudesGestor"](Suscripcion, idUsuario)
            ,{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            });
        setSolicitudes(response.data);
        if (response.data) {
          await getLogs(null,null,null,"Listado Solicitudes","Solicitudes","Ver Solicitudes","Contratos","GET");
        }
      } catch (error: any) {
        console.error("Error al obtener datos:", error.message);
      }
    };
    obtenerSolicitudesUsuario();
  }, []);
  const verSolicitudes = () =>{
    navigate(RoutesPrivate.INICIOGESTOR, {state:{perfil}})
  }
  
  return (
    <div>
      <Header />
      <Titulo seccion={`Bienvenido ${Nombres} ${Apellidos} `} />
      <div className="container-nueva-solicitud" style={{ padding: "0 0.8rem" }}>
        <div className="container-contador-gestor">
          <ConteoSolicitudes
            idUsuario={idUsuario}
            Suscripcion={Suscripcion}
            perfil={perfil}
            solicitudes={solicitudes}
            
          />
        </div>
      </div>
      <div className="global-inicio-Gestor">
        <div className="contenedor-card-inicio-gestor">
          <Tabla perfil={perfil}/>
        </div>
      </div>
    </div>
  );
};

export default Inicio;
