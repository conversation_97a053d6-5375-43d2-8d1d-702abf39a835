import React from 'react'
import { useLocation } from 'react-router-dom';
import Header from '../../../../../../Components/Partials/Header/Header';
import Titulo from '../../../../../../Components/Partials/Seccion/Titulo';
import ExtraJudicial from './TipoSolicitudes/ExtraJudicial';
import { RoutesPrivate } from '../../../../../../../Security/Routes/ProtectedRoute';


const CrearEJ = () => {
    const location = useLocation();

    const solicitud = location.state?.solicitud
    const idUsuario = location.state?.idUsuario;
    const idAplicacion = location.state?.idAplicacion;
    const Suscriptor = location.state?.Suscriptor;
    const Suscripcion = location.state?.Suscripcion;
    const Nombres = location.state?.Nombres;
    const Apellidos = location.state?.Apellidos;
    const gestor = location.state?.gestor;
    return (
        <div>
          <Header />
          <Titulo seccion={`Acuerdo Extrajudicial del Contrato ${solicitud.str_CodSolicitudes}`} salir={true} paginaSalir={gestor ? RoutesPrivate.INICIOGESTOR : RoutesPrivate.INICIOSOLICITANTE}/>
         
          <ExtraJudicial
          idAplicacion = {idAplicacion}
          Suscriptor = {Suscriptor}
          Suscripcion = {Suscripcion}
          selectedSolicitud={solicitud}
          UsuarioId={idUsuario}
          Nombres={Nombres}
          Apellidos={Apellidos}
          gestor={gestor}/>
        </div>
      );
}

export default CrearEJ
