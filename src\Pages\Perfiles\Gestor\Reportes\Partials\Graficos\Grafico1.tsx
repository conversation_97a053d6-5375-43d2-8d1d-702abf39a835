import { useEffect, useState, useCallback } from "react";
import { Pie } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import axios from "axios";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
 import { decrypt, validateToken } from "../../../../../Components/Services/TokenService";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  ArcElement,
  Tooltip,
  Legend,
} from "chart.js";
import { Modal } from "@mui/material";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";

ChartJS.register(
  CategoryScale,
  LinearScale,
  ArcElement,
  Tooltip,
  Legend,
  ChartDataLabels
);

interface Grafico1Props {
  data: any[];
  isLoading: boolean;
  selectedEmpresaFiltro: any;
  aniosSeleccionados: any;
  selectedTipoSolicitudFiltro: any;
  idAplicacion: any;
  Suscripcion: any;
  idUsuario: any;
  rol: string;
}

const Grafico1 = ({ data, isLoading, selectedEmpresaFiltro, aniosSeleccionados, selectedTipoSolicitudFiltro ,idAplicacion, Suscripcion, idUsuario,rol}: Grafico1Props) => {
  const [openModal, setOpenModal] = useState(false);
  const [tipoGrafico, setTipoGrafico] = useState<'pendientes' | 'firmadas'>('pendientes');
  const [datosGrafico, setDatosGrafico] = useState(data || []);
  const [isLoadingLocal, setIsLoadingLocal] = useState(false);

  const handleCloseModal = () => setOpenModal(false);

  // Obtener datos del token
  const token = localStorage.getItem("token");
   

  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [
      {
        label: "Solicitudes",
        data: [],
        backgroundColor: [],
        borderColor: "rgba(255, 255, 255, 1)",
        borderWidth: 1,
      },
    ],
  });
  const [chartDataModal, setChartDataModal] = useState(chartData);
 console.warn("Datos necesarios no disponibles para cargar gráfico:", {
         aniosSeleccionados,
         selectedEmpresaFiltro,
        Suscripcion,
        idUsuario
      });
  // Función para obtener datos según el tipo seleccionado
  const fetchDataByType = useCallback(async (tipo: 'pendientes' | 'firmadas') => {
    // Validar que todos los datos necesarios estén disponibles
    if (!aniosSeleccionados?.grafico1 || !selectedEmpresaFiltro?.value || !Suscripcion || !idUsuario) {
     
      return;
    }

    await validateToken();
    setIsLoadingLocal(true);

    try {
      const apiCall = tipo === 'pendientes'
        ? (rol === "Gestor"
            ? API_GESTOR["PendientesporUN"](Suscripcion, aniosSeleccionados.grafico1, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro?.value || 0)
            : API_SOLICITANTE["PendientesporUN"](Suscripcion, aniosSeleccionados.grafico1, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro?.value || 0))
        : (rol === "Gestor"
            ? API_GESTOR["FirmadosporUN"](Suscripcion, aniosSeleccionados.grafico1, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro?.value || 0)
            : API_SOLICITANTE["FirmadosporUN"](Suscripcion, aniosSeleccionados.grafico1, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro?.value || 0));
      
      const response = await axios.get(apiCall, {
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
      });

      setDatosGrafico(response.data || []);
    } catch (error) {
      console.error("Error al obtener datos del gráfico:", error);
      setDatosGrafico([]);
    } finally {
      setIsLoadingLocal(false);
    }
  }, [aniosSeleccionados, selectedEmpresaFiltro, selectedTipoSolicitudFiltro, Suscripcion, idUsuario, token, rol]);

  // Manejar apertura del modal con datos según filtro
  const handleOpenModal = useCallback(async () => {
    // Cargar datos según el tipo seleccionado antes de abrir el modal
    if (tipoGrafico === 'firmadas') {
      await fetchDataByType('firmadas');
    } else {
      // Para pendientes, usar los datos ya cargados
      setDatosGrafico(data || []);
    }
    setOpenModal(true);
  }, [tipoGrafico, fetchDataByType, data]);

  // Manejar cambio de tipo de gráfico
  const handleTipoChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const nuevoTipo = event.target.value as 'pendientes' | 'firmadas';
    setTipoGrafico(nuevoTipo);

    if (nuevoTipo === 'pendientes') {
      setDatosGrafico(data || []);
    } else {
      fetchDataByType(nuevoTipo);
    }
  };

  // Efecto para cargar datos iniciales cuando cambian los filtros
  useEffect(() => {
    if (tipoGrafico === 'pendientes') {
      setDatosGrafico(data || []);
    } else if (aniosSeleccionados?.grafico1 && selectedEmpresaFiltro?.value) {
      fetchDataByType('firmadas');
    }
  }, [data, selectedEmpresaFiltro, aniosSeleccionados, selectedTipoSolicitudFiltro, tipoGrafico, fetchDataByType]);

  useEffect(() => {
    if (datosGrafico) {
      const labels = datosGrafico.map((item) => item.unidad_negocio);
      const data = datosGrafico.map((item) => item.cantidad_solicitudes);

      const maxIndex = data.indexOf(Math.max(...data));

      const backgroundColorNormal = data.map((_, index) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgba(200, 200, 200, ${0.5 + Math.random() * 0.5})`
      );

      const backgroundColorModal = data.map((_, index) =>
        index === maxIndex
          ? "#2C4CB3"
          : `rgb(${Math.floor(Math.random() * 170)}, ${Math.floor(
              Math.random() * 170
            )}, ${Math.floor(Math.random() * 170)})`
      );
      setChartData({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data,
            backgroundColor: backgroundColorNormal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });

      setChartDataModal({
        labels,
        datasets: [
          {
            label: "Solicitudes",
            data,
            backgroundColor: backgroundColorModal,
            borderColor: "rgba(255, 255, 255, 1)",
            borderWidth: 1,
          },
        ],
      });
    }
  }, [datosGrafico]);

   return (
    <div className="card-grafico-reportes-gestor">
      <div className="header-card-grafico-reportes-gestor lato-font">
        <div
          className="titulo-card"
          onClick={handleOpenModal}
          style={{ cursor: "pointer", flex: 1 }}
        >
          Solicitudes {tipoGrafico} por unidad de negocio
        </div>
        <select
          value={tipoGrafico}
          onChange={handleTipoChange}
          title="Seleccionar tipo de solicitudes"
          style={{
            padding: '0.25rem 0.5rem',
            borderRadius: '0.25rem',
            border: '1px solid #ddd',
            fontSize: '0.875rem',
            backgroundColor: 'white',
            cursor: 'pointer'
          }}
        >
          <option value="pendientes">Pendientes</option>
          <option value="firmadas">Firmados</option>
        </select>
      </div>
      <div className="grafico-pie-gestor">
        {(isLoading || isLoadingLocal) ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            flexDirection: 'column',
            gap: '1rem'
          }}>
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Cargando...</span>
            </div>
            <span style={{ fontSize: '0.875rem', color: '#666' }}>
              Cargando datos...
            </span>
          </div>
        ) : (
          <Pie
          data={chartData}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            aspectRatio: 0.8,
            plugins: {
              legend: {
                position: "none",
              },
              tooltip: {
                callbacks: {
                  label: (tooltipItem) => {
                    const dataset = tooltipItem.dataset;
                    const dataIndex = tooltipItem.dataIndex;
                    const value = dataset.data[dataIndex];
                    const total = dataset.data.reduce(
                      (acc, val) => acc + val,
                      0
                    );
                    const percentage = ((value / total) * 100).toFixed(0);
                    return `${percentage}%`;
                  },
                },
              },
              datalabels: {
                display: (context) =>
                  context.dataIndex ===
                  chartData.datasets[0].data.indexOf(
                    Math.max(...chartData.datasets[0].data)
                  ),
                color: "#fff",
                font: {
                  weight: "bold",
                  size: 16,
                },
                formatter: (value, context) => {
                  const total = context.chart.data.datasets[0].data.reduce(
                    (acc, val) => acc + val,
                    0
                  );
                  const percentage = ((value / total) * 100).toFixed(0);
                  return `${percentage}% `; // Mostrar porcentaje
                },
              },
            },
          }}
          />
        )}
      </div>
      <Modal open={openModal} onClose={handleCloseModal}    style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
        <div className="card-grafico-reportes-gestor-modal">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleCloseModal}
            ></button>
          </div>
          <div className="header-card-grafico-reportes-gestor lato-font">
            <div className="titulo-card">
              Solicitudes {tipoGrafico} por unidad de negocio
            </div>
          </div>
          <div className="grafico-pie-gestor-modal">
            <Pie
              data={chartDataModal}
              style={{ maxWidth: "20rem", maxHeight: "20rem" }}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: "none",
                  },
                  tooltip: {
                    callbacks: {
                      label: (tooltipItem) => {
                        const dataset = tooltipItem.dataset;
                        const dataIndex = tooltipItem.dataIndex;
                        const value = dataset.data[dataIndex];
                        const total = dataset.data.reduce(
                          (acc, val) => acc + val,
                          0
                        );
                        const percentage = ((value / total) * 100).toFixed(0);
                        return `${percentage}%`;
                      },
                    },
                  },
                  datalabels: {
                    display: true,
                    color: "#fff",
                    font: {
                      weight: "bold",
                      size: 10,
                    },
                    formatter: (value, context) => {
                      const total = context.chart.data.datasets[0].data.reduce(
                        (acc, val) => acc + val,
                        0
                      );
                      const percentage = ((value / total) * 100).toFixed(0);
                      return `${percentage}%`; // Mostrar porcentaje
                    },
                  },
                },
              }}
            />
            <div className="leyenda-graficos-gestor">
              {chartDataModal.labels.map((label, index) => (
                <div
                  key={index}
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <div
                    style={{
                      width: "0.75rem",
                      height: "0.75rem",
                      backgroundColor:
                        chartDataModal.datasets[0].backgroundColor[index],
                      borderRadius: "20%",
                      marginRight: "8px",
                    }}
                  />
                  <span className="texto-leyenda-graficos-gestor">
                    {label}: {chartDataModal.datasets[0].data[index]}{" "}
                    solicitudes
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Grafico1;
