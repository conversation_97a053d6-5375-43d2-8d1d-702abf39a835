import React, { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";
import Cookies from "js-cookie";

import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import axios from "axios";
import {
  decrypt,
  validateToken,
} from "../../../../../Components/Services/TokenService";
import ModalDocumentoFirmado from "../../Modales/ModalDocumentoFirmado";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import IconoFirmar from "../../../../../../assets/SVG/IconoFirmar";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
import IconoUpload from "../../../../../../assets/SVG/IconoUpload";
import IconoArchivar from "../../../../../../assets/SVG/IconoArchivar";
import IconoCrearAdenda from "../../../../../../assets/SVG/IconoCrearAdenda";

interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes: string;
  };
  historiales: any[];
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: () => void;
}

const Firmado: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  aprobadores,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [modales, setModales] = useState({
    isModalVisiblContratoFirmado: false,
  });
  const [solicitud, setSolicitud] = useState(null);
  const navigate = useNavigate();
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const Suscriptor = decrypt(Cookies.get("suscriptor"));
  const token = Cookies.get("Token");
  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const baseUrl = import.meta.env.VITE_BASE_URL;

  useEffect(() => {
    const obtenerSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerSolicitud"](
            solicitudSeleccionada.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setSolicitud(response.data);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
        setSolicitud(null);
      }
    };

    obtenerSolicitud();
  }, [solicitudSeleccionada]);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFile(event.target.files[0]);
      handleFileUploadConfirmation(event.target.files[0]);
      event.target.value = "";
    }
  };
  const handleFileUploadConfirmation = (file: File) => {
    Swal.fire({
      title: "¿Estás seguro?",
      text: `Está seguro de sobreescribir el documento firmado?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, subirlo",
    }).then((result) => {
      if (result.isConfirmed) {
        setModales((prev) => ({ ...prev, isModalVisiblContratoFirmado: true }));
      }
    });
  };

  const handleSubmitFiles = async (file: File) => {
    if (!file) {
      Swal.fire("", "Por favor, selecciona un archivo primero", "error");
      return;
    }

    try {
      const formDataSolicitud = new FormData();
      formDataSolicitud.append("archivo", file);
      formDataSolicitud.append("str_idSuscriptor", Suscripcion);
      formDataSolicitud.append(
        "str_CodSolicitudes",
        solicitudSeleccionada.str_CodSolicitudes
      );
      formDataSolicitud.append(
        "int_idSolicitudes",
        solicitudSeleccionada.int_idSolicitudes
      );
      formDataSolicitud.append("str_CodTipoDocumento", "COFI");
      formDataSolicitud.append("int_idUsuarioCreacion", idUsuario);

      const response = await axios.post(
        API_GESTOR["UploadArchivos"](),
        formDataSolicitud,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        setFile(null);
      } else {
        throw new Error("No se pudo ingresar el archivo");
      }
    } catch (error) {
      Swal.fire("", "No se pudo subir el archivo", "error");
    }
  };
  const handleArchivarConfirmation = () => {
    Swal.fire({
      title: "¿Estás seguro?",
      text: `Está seguro de archivar la solicitud?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, archivar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        handleArchivarSolicitud();
      }
    });
  };
  const handleArchivarSolicitud = async () => {
    await validateToken();

    try {
      const response = await axios.put(
        API_GESTOR["ArchivarSolicitud"](),
        {
          int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
          int_idUsuarioModificacion: idUsuario,
        },
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        SubmitObtenerDatos();
        setSelectedSolicitud({});
        Swal.fire("", "La solicitud se archivó correctamente", "success");
      } else {
        throw new Error("No se pudo ingresar el archivo");
      }
    } catch (error) {
      Swal.fire("", "No se pudo subir el archivo", "error");
    }
  };
  const CrearAdenda = async (codigoAdenda) => {
    try {
      const response = await axios.get(
        `${baseUrl}api/solicitudes/buscarxcodigo/${codigoAdenda}/${Suscripcion}/`
      ,{
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
      );

      if (response.status === 200) {
        const Estado = response.data.estado_str_Nombre;
        const solicitud = response.data;
        if (Estado === "Firmado") {
          navigate(RoutesPrivate.CREARADENDA, {
            state: {
              solicitud,
              idAplicacion,
              Suscriptor,
              Suscripcion,
              gestor: true,
              idUsuario,
              Apellidos,
              Nombres,
            },
          });
        } else {
          Swal.fire(
            "",
            "La referencia colocada no se encuentra en el Registro de Contratos ",
            "error"
          );
        }
      } else {
        Swal.fire(
          "",
          "El código ingresado no pertenece a ninguna solicitud",
          "error"
        );
      }
    } catch {
      Swal.fire(
        "",
        "El código ingresado no pertenece a ninguna solicitud",
        "error"
      );
    }
  };
  const CrearEJ = async (codigoEJ) => {
    try {
      const response = await axios.get(
        `${baseUrl}api/solicitudes/buscarxcodigo/${codigoEJ}/${Suscripcion}/`
      ,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        const Estado = response.data.estado_str_Nombre;
        const solicitud = response.data;
        if (Estado === "Firmado") {
          navigate(RoutesPrivate.CREAREJ, {
            state: {
              solicitud,
              idAplicacion,
              Suscriptor,
              Suscripcion,
              gestor: true,
              idUsuario,
              Apellidos,
              Nombres,
            },
          });
        } else {
          Swal.fire(
            "",
            "La referencia colocada no se encuentra en el Registro de Contratos ",
            "error"
          );
        }
      } else {
        Swal.fire(
          "",
          "El código ingresado no pertenece a ninguna solicitud",
          "error"
        );
      }
    } catch {
      Swal.fire(
        "",
        "El código ingresado no pertenece a ninguna solicitud",
        "error"
      );
    }
  };
  const handleVerSolicitud = () => {
    navigate(RoutesPrivate.EDITARSOLICITUD, {
      state: {
        solicitudSeleccionada,
        Suscripcion,
        idAplicacion,
        idUsuario,
        Suscriptor,
        ver: true,
      },
    });
  };
  return (
    <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Subir Documento Firmado" placement="top">
          <div
            className="icono-barralateral-acciones"
            onClick={handleButtonClick}
          >
            <IconoUpload size={" 1.3rem"} color="#000" />
            </div> 
        </Tooltip>
        <Tooltip title="Archivar Solicitud" placement="top">
        <div
            className="icono-barralateral-acciones"
            onClick={handleArchivarConfirmation}
          >
          <IconoArchivar size={" 1.3rem"} color="#000" />
          </div> 
        </Tooltip>

        {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") ? (
          ""
        ) : (
          <Tooltip title="Crear Adenda" placement="top">
            <div
            className="icono-barralateral-acciones"
            onClick={() =>
              CrearAdenda(solicitudSeleccionada.str_CodSolicitudes)
            }
          >
            <IconoCrearAdenda size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
        )}
        {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") ? (
          ""
        ) : (
          <Tooltip title="Crear acuerdo EJ" placement="top">
            <div
              onClick={() => CrearEJ(solicitudSeleccionada.str_CodSolicitudes)}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoFirmar size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
        )}
        {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") ? (
          ""
        ) : (
          <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
        )}
      </div>
      <input
        ref={fileInputRef}
        className="form-control"
        type="file"
        multiple
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores} />
      <ModalDocumentoFirmado
        isModalVisiblContratoFirmado={modales.isModalVisiblContratoFirmado}
        CerrarModalContratoFirmado={() =>
          toggleModal("isModalVisiblContratoFirmado")
        }
        file={file}
        idUsuario={idUsuario}
        handleSubmitFiles={() => handleSubmitFiles(file)}
        Suscripcion={Suscripcion}
        idSolicitud={solicitudSeleccionada.int_idSolicitudes}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
        solicitudActual={solicitud}
      />
    </div>
  );
};

export default Firmado;
