const BASE_URL = import.meta.env.VITE_BASE_URL;
const BASE_URL8000 = import.meta.env.VITE_SEGURIDAD_URL;
const BASE_PROCESOS = import.meta.env.VITE_URL_PROCESOS;

type Suscripcion = string | number | any;
type IdUsuario = string | number;
type IdSolicitud = string | number;
type idContenidoSolicitud = string | number;
type IdAplicacion = string | number;
type Ruc = string | number;
type IdInterlocutor = string | number;
type Solicitud = string | number | any;

const API_SOLICITANTE = {
  ObtenerTipoSolicitud: (Suscripcion: Suscripcion): string =>
    `${BASE_URL}api/tipo-solicitud/?str_idSuscripcion=${Suscripcion}`,

  ObtenerSolicitudesSolicitante: (
    Suscripcion: Suscripcion,
    idUsuario: IdUsuario
  ): string =>
    `${BASE_URL}api/solicitudes/solicitante/?str_idSuscriptor=${Suscripcion}&int_idSolicitante=${idUsuario}`,

  ObtenerEstadosSolicitud: (Suscripcion: Suscripcion): string =>
    `${BASE_URL}api/estados/?str_idSuscripcion=${Suscripcion}`,

  ObtenerEmpresas: (
    idAplicacion: IdAplicacion,
    Suscriptor: Suscripcion
  ): string =>
    `${BASE_URL8000}empresas/asignadas/aplicacion/${idAplicacion}/suscriptor/${Suscriptor}/`,

  ObtenerUnidadesNegocios: (
    Suscripcion: Suscripcion,
    int_idEmpresa: number
  ): string =>
    `${BASE_URL}api/unidades-negocios/?str_idSuscripcion=${Suscripcion}&int_idEmpresa=${int_idEmpresa}`,

  HistorialUsuario: (
    Suscripcion: Suscripcion,
    idSolicitud: IdSolicitud
  ): string =>
    `${BASE_URL}api/historial-estados/?str_idSuscriptor=${Suscripcion}&int_idSolicitudes=${idSolicitud}`,

  ObtenerGestores: (
    Suscripcion: Suscripcion,
    int_idAplicacion: IdAplicacion
  ): string =>
    `${BASE_URL8000}perfiles/nombre/Gestor/suscripcion/${Suscripcion}/aplicacion/${int_idAplicacion}/`,

  ObtenerSolicitud: (idSolicitud: IdSolicitud): string =>
    `${BASE_URL}api/solicitudes/${idSolicitud}/`,

  ObtenerContenidoSolicitud: (idSolicitud: IdSolicitud): string =>
    `${BASE_URL}api/ContenidoSolicitud/${idSolicitud}/`,
  ListarArchivosEditar: (
    Suscripcion: Suscripcion,
    idSolicitud: IdSolicitud
  ): string =>
    `${BASE_URL}api/archivos/?str_idSuscriptor=${Suscripcion}&int_idSolicitudes=${idSolicitud}&str_CodTipoDocumento=DOAD`,
  ListarArchivo: (
    Suscripcion: Suscripcion,
    str_CodSolicitudes: string,
    tipoDoc: string
  ): string =>
    `${BASE_URL}api/archivos/listar-archivo/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=${tipoDoc}`,
  DescargarArchivo: (
    Suscripcion: Suscripcion,
    str_CodSolicitudes: string,
    tipoDoc: string
  ): string =>
    `${BASE_URL}api/archivos/descargar-archivo/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=${tipoDoc}`,
  AsignarGestor: (): string => `${BASE_URL}api/solicitudes/asignar-gestor/`,

  ActualizarEstado: (): string => `${BASE_URL}api/solicitudes/editar-estado/`,

  BuscarInterlocutor: (ruc: Ruc, Suscripcion: Suscripcion): string =>
    `${BASE_URL}api/interlocutores/documento/${ruc}/?str_idSuscripcion=${Suscripcion}`,

  BuscarInterlocutorID: (idInterlocutor: IdInterlocutor): string =>
    `${BASE_URL}api/interlocutores/${idInterlocutor}/`,

  AgregarInterlocutor: (): string => `${BASE_URL}api/interlocutores/`,
  AgregarConsorcio: (): string => `${BASE_URL}api/consorcio/`,
  AgregarConsorcioSolicitud: (): string => `${BASE_URL}api/consorcio/asignar/`,
  ActualizarInterlocutor: (idInterlocutor: IdInterlocutor): string =>
    `${BASE_URL}api/interlocutores/${idInterlocutor}/`,

  UploadArchivos: (): string => `${BASE_URL}api/archivos/upload/`,

  AgregarSolicitud: (): string => `${BASE_URL}api/solicitudes/crear/`,

  ActualizarSolicitud: (idSolicitud: IdSolicitud): string =>
    `${BASE_URL}api/solicitudes/${idSolicitud}/`,
  ActualizarContenidoSolicitud: (
    idContenidoSolicitud: idContenidoSolicitud
  ): string => `${BASE_URL}api/ContenidoSolicitud/${idContenidoSolicitud}/`,
  InsertarContenidoSolicitud: (): string =>
    `${BASE_URL}api/ContenidoSolicitud/crear/`,

  EliminarSolicitud: (solicitud: Solicitud): string =>
    `${BASE_URL}api/solicitudes/${solicitud}/`,
  EliminarArchivo: (
    Suscripcion: Suscripcion,
    str_CodSolicitudes: string,
    nombre_archivo: string,
    int_idArchivos: string,
    tipo_adjunto: string | null
  ): string =>
    `${BASE_URL}api/archivos/eliminar/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=DOAD&nombre_archivo=${nombre_archivo}&int_idArchivos=${int_idArchivos}${
      tipo_adjunto ? "&tipo_adjunto=" + tipo_adjunto : ""
    }`,
  ListarAprobadores: (Suscripcion: Suscripcion, Solicitud: Solicitud): string =>
    `${BASE_URL}api/solicitudes/aprobadores/${Suscripcion}/${Solicitud}/`,

  ObtenerDatosConsorcio: (idSolicitudCont: string): string =>
    `${BASE_URL}api/consorcio/solicitud/?int_idSolicitudCont=${idSolicitudCont}`,
  EliminarAsociados: (idSolicitudCont: string | number): string =>
    `${BASE_URL}api/consorcio/asociado/eliminar/${idSolicitudCont}/`,
  ContadorPorGestor: (
    idGestor: string | number,
    Suscripcion: Suscripcion
  ): string =>
    `${BASE_URL}api/solicitudes/SolicitudesxGestor/?int_idGestor=${idGestor}&str_idSuscriptor=${Suscripcion}`,
  ContadorPorGestorAnio: (
    idGestor: string | number,
    Suscripcion: Suscripcion,
    anio: string
  ): string =>
    `${BASE_URL}api/solicitudes/SolicitudesxGestor/anio/?int_idGestor=${idGestor}&str_idSuscriptor=${Suscripcion}&anio=${anio}`,
  ContadorPorGestorAnioProcesos: (
    idGestor: string | number,
    Suscripcion: Suscripcion,
    anio: string
  ): string =>
    `${BASE_PROCESOS}procesos/gestor/${idGestor}/suscripcion/${Suscripcion}/anio/${anio}/`,
  ObtenerGestoresTipoSolicitud: (
    Suscripcion: Suscripcion,
    idTipoSolicitud: IdAplicacion,
    idEmpresa: number
  ): string =>
    `${BASE_URL}api/gestortiposolicitud/?str_idSuscripcion=${Suscripcion}&int_idTipoSolicitud=${idTipoSolicitud}&int_idEmpresa=${idEmpresa}`,

  //MONEDA
  ObtenerMoneda: (idEmpresa: number, Suscripcion: Suscripcion): string =>
    `${BASE_URL}api/empresas/moneda/${idEmpresa}/?str_idSuscripcion=${Suscripcion}`,

  //FIRMAR
  FirmarAceptacion: (): string => `${BASE_URL}api/archivo/firmado/`,

  //UPLOAD DATA
  //FIRMAR
  ImportarData: (): string => `${BASE_URL}api/solicitudes/crear/archivo/`,
  DescargarPlantilla: (Suscripcion, idUsuario): string =>
    `${BASE_URL}api/archivo/downdload-plantilla-upload-solicitudes/?str_idSuscriptor=${Suscripcion}&int_idUsuarios=${idUsuario}`,
  //GRAFICOS

  PromedioAtencion: (
    idUsuario: number,
    Suscripcion: number,
    selectedEmpresaFiltro: number,
    anio: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/promedio/Solicitante/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
  PromedioPreparacion: (
    idUsuario: number,
    Suscripcion: number,
    selectedEmpresaFiltro: number,
    anio: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/promedio/Preparacion/Solicitante/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
  PromedioTotal: (
    idUsuario: number,
    Suscripcion: number,
    selectedEmpresaFiltro: number,
    anio: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/promedio/Total/Solicitante/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
  SolicitudesTotal: (
    idUsuario: number,
    Suscripcion: number,
    selectedEmpresaFiltro: number,
    anio: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/calculos/solicitudes/Total/Solicitante/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
  ContratosFirmados: (
    idUsuario: number,
    Suscripcion: number,
    selectedEmpresaFiltro: number,
    anio: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/contratosFirmados/Total/Solicitante/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
  PendientesporUN: (
    Suscripcion: number,
    anio: number,
    idUsuario: number,
    selectedEmpresaFiltro: number,
    selectedTipoSolicitudFiltro: number
  ): string =>
    `${BASE_URL}api/graficos/UNFaltantes/Solicitante/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idSolicitante=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${
      selectedTipoSolicitudFiltro
        ? "&int_idTipoSol=" + selectedTipoSolicitudFiltro
        : ""
    }`,
      FirmadosporUN : (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNFirmados/Solicitante/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idSolicitante=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
     PresupuestoUN: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNPresupuesto/Solicitante/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idSolicitante=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
       SolicitudesPorEstado: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/DiferenciaEstados/Solicitante/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idSolicitante=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
        HorasUN: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/HorasTrabajadas/Solicitante/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idSolicitante=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,

        // MATRIZ
      MatrizSolicitudesSolicitante: (idUsuario:number,Suscripcion:number, anio:number,codSolicitud:string,filtroMatrizEstado:string): string => 
    `${BASE_URL}api/solicitudes/solicitante/matriz/?int_idSolicitante=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}${codSolicitud ? "&str_CodSolicitudes="+codSolicitud : ""}${filtroMatrizEstado ? "&firmado="+filtroMatrizEstado : ""}`,
      };

export default API_SOLICITANTE;
