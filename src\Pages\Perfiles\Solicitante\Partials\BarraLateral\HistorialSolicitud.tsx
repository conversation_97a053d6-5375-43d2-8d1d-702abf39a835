import React from "react";
import IconoDownload from "../../../../../assets/SVG/IconoDownload";
import * as XLSX from "xlsx";

function formaterFecha(dateString: string) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}

function formaterFechaHora(dateString: string) {
  const date = new Date(dateString);
  const hour = date.getHours();
  const minutes = date.getMinutes();

  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();
  const formattedHour = `${hour}:${minutes < 10 ? "0" : ""}${minutes}`;

  return `${day} de ${month} de ${year} a las ${formattedHour}`;
}

type Historial = {
  Nombre_estado: string;
  fecha_Cambio: string;
};

type Aprobador = {
  int_idUsuario: number;
  str_Nombres: string;
  str_Apellidos: string;
  int_EstadoAprobacion: number;
  dt_FechaAceptacion: string;
};

type HistorialSolicitudProps = {
  historiales: Historial[];
  aprobadores: Aprobador[] | null;
};

const HistorialSolicitud: React.FC<HistorialSolicitudProps> = ({
  historiales,
  aprobadores,
}) => {
  const getAprobadoresPorBloque = (numBloque: number) => {
    return aprobadores?.filter(
      (aprobador) => aprobador.int_NumBloque === numBloque
    );
  };

  const handleDownloadExcel = () => {
    const data = [];

    historiales.forEach((historial) => {
      data.push({
        Estado: historial.Nombre_estado,
        Fecha_Cambio: formaterFechaHora(historial.fecha_Cambio),
        "": "",
      });
    });

    if (aprobadores && aprobadores.length > 0) {
      data.push({});
      data.push({ Estado: "Aprobadores:" });
      data.push({
        Estado: "Estado:",
        Fecha_Cambio: "Fecha_Aprobación",
        "": "Aprobador",
      });
      aprobadores.forEach((aprobador) => {
        data.push({
          "": `${aprobador.str_Nombres} ${aprobador.str_Apellidos}`,
          Fecha_Cambio: aprobador.dt_FechaAceptacion
            ? formaterFechaHora(aprobador.dt_FechaAceptacion)
            : "Sin Aprobar",
          Estado:
            aprobador.int_EstadoAprobacion === 0 ? "Rechazado" : "Aprobado",
        });
      });
    }

    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Historial");

    XLSX.writeFile(workbook, "Historial_y_Aprobadores.xlsx");
  };

  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">
        Historial <IconoDownload onClick={handleDownloadExcel} color={"#294FCF"} size={"1.3rem"}/>
      </span>
      <ul className="stepper">
        {(() => {
          let bloqueActual = 1; // Controla el bloque asignado a los estados "En Aprobación"
          return historiales.map((historial, index) => (
            <React.Fragment key={index}>
              <li className="step completed">
                <span className="circle"></span>
                <div className="text montserrat-font">
                  <p>{historial.Nombre_estado}</p>
                  <span>{formaterFecha(historial.fecha_Cambio)}</span>
                </div>
              </li>
              {historial.Nombre_estado === "En Aprobación" && (
                <div
                  className="aprobadores-list-barra-laretal montserrat-font"
                  key={`aprobadores-${bloqueActual}`}
                >
                  <p>Aprobadores:</p>
                  <ul>
                    {getAprobadoresPorBloque(bloqueActual)?.map((aprobador) => (
                      <li key={aprobador.int_idUsuario}>
                        {aprobador.str_Nombres} {aprobador.str_Apellidos}
                        <br />
                        <span>
                          {aprobador.dt_FechaAceptacion
                            ? formaterFecha(aprobador.dt_FechaAceptacion || "")
                            : "Sin Aprobar"}
                        </span>
                        {aprobador.dt_FechaAceptacion &&
                        aprobador.int_EstadoAprobacion === 0 ? (
                          <>
                            <br /> <span>Rechazado</span>
                          </>
                        ) : (
                          ""
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
             <span style={{display: "none"}}> {historial.Nombre_estado === "En Aprobación" && bloqueActual++}</span>
            </React.Fragment>
          ));
        })()}
      </ul>
    </>
  );
};

export default HistorialSolicitud;