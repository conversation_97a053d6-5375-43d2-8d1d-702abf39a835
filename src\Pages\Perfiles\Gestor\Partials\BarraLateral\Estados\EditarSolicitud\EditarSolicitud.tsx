import Header from '../../../../../../Components/Partials/Header/Header'
import Cookies from "js-cookie";
import Titulo from '../../../../../../Components/Partials/Seccion/Titulo';
import { useLocation } from 'react-router-dom';
import PrestacionServicio from './TipoSolicitudes/PrestacionServicio';
import AcuerdoConfidencialidad from './TipoSolicitudes/AcuerdoConfidencialidad';
import LocacionServicio from './TipoSolicitudes/LocacionServicio';
import PrestacionServicioPro from './TipoSolicitudes/PrestacionServicioPro';
import CompraVenta from './TipoSolicitudes/CompraVenta';
import ArrendamientoBienes from './TipoSolicitudes/ArrendamientoBienes';
import Consorcio from './TipoSolicitudes/Consorcio';
import { RoutesPrivate } from '../../../../../../../Security/Routes/ProtectedRoute';


const EditarSolicitud = () => {
  
    const location = useLocation();
    const solicitudSeleccionada = location.state?.solicitudSeleccionada;
    const idAplicacion = location.state?.idAplicacion;
    const Suscripcion = location.state?.Suscripcion;
    const idUsuario = location.state?.idUsuario;
    const Suscriptor = location.state?.Suscriptor;
    const Asignado = location.state?.Asignado;
    const documentoSubido = location.state?.documentoSubido;
    const ver = location.state?.ver || false;

  console.log("solicitudSeleccionada", solicitudSeleccionada.nombre_TipoSolicitud);
  return (
    <div>
        <Header/>
        <Titulo seccion={`Solicitud ${solicitudSeleccionada.str_CodSolicitudes}` } salir={true} paginaSalir={RoutesPrivate.INICIOGESTOR}/>
        {solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de prestación de servicios" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Depósito Simple"  || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Administración Almacén On Site" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Depósito Temporal" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Transporte" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Distribución" || 
     solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Depósito y Transporte" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Servicio SILE" || solicitudSeleccionada.nombre_TipoSolicitud  === "Contrato de Depósito temporal y Transporte"?
         <PrestacionServicio
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}
         />:
         solicitudSeleccionada.nombre_TipoSolicitud=== "Acuerdo de Confidencialidad Cliente" || solicitudSeleccionada.nombre_TipoSolicitud=== "Acuerdo de Confidencialidad Proveedor" || solicitudSeleccionada.nombre_TipoSolicitud=== "Acuerdo de confidencialidad"
         ?
         <AcuerdoConfidencialidad
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}
         />
         :
         solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de locación de servicios de cliente" || solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de locación de servicios de proveedor"|| solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de locación de servicios"
         ?
         <LocacionServicio
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}
         />
         :
         solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de prestación de servicios profesionales"
         ?
         <PrestacionServicioPro
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}

         />
         :
         solicitudSeleccionada.nombre_TipoSolicitud==="Contrato de compra / venta de bienes muebles o inmuebles"
         ?
         <CompraVenta
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}

         />
         :
         solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de arrendamiento de bienes muebles o inmuebles"
         ?
         <ArrendamientoBienes
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}

         />: solicitudSeleccionada.nombre_TipoSolicitud=== "Contrato de consorcio"
         ?
         <Consorcio
         selectedSolicitud={solicitudSeleccionada}
         idAplicacion={idAplicacion}
         Suscriptor={Suscriptor}
         UsuarioId={idUsuario}
         Suscripcion={Suscripcion}
         Asignado={Asignado}
         documentoSubido={documentoSubido}
         ver={ver}

         />: ""}
    </div>
  )
}

export default EditarSolicitud