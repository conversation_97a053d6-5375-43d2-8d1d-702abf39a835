import React, { useEffect, useState } from 'react';
import API_SOLICITANTE from '../../../../../assets/Api/ApisSolicitante';
import axios from 'axios';
import Cookies from "js-cookie";
import { validateToken } from '../../../../Components/Services/TokenService';

interface Solicitud {
  nombre_gestor: string;
}

interface GestorAsignadoProps {
  solicitudSeleccionada: {
    int_idSolicitudes: number;
    int_idGestor: number | null; 
  };
}

const GestorAsignado: React.FC<GestorAsignadoProps> = ({ solicitudSeleccionada }) => {
  const [solicitud, setSolicitud] = useState<Solicitud | null>(null);
  const [error, setError] = useState<string | null>(null);
  const token = Cookies.get("Token");

  useEffect(() => {
    const obtenerGestores = async () => {
await validateToken();
      setError(null);
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerSolicitud"](solicitudSeleccionada.int_idSolicitudes),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        setSolicitud(response.data);
      } catch (error) {
        return error        
        setError("No se pudo obtener el gestor asignado.");
      } 
    };

    if (solicitudSeleccionada.int_idGestor) {
      obtenerGestores();
    }
  }, [solicitudSeleccionada]);

  if (error) {
    return <span>{error}</span>;
  }

  return (
    <>
      {solicitudSeleccionada.int_idGestor ? (
        <>
          <span className="subtitulo-barra-Lateral lato-font-400">Gestor</span>
          <span className="nombre-solicitante-barra-lateral">
            {solicitud?.nombre_gestor}
          </span>
        </>
      ) : null}
    </>
  );
};

export default GestorAsignado;