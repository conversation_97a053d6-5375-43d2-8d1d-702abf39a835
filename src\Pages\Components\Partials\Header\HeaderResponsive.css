@media screen and (max-width: 480px) {
    .global-header {
        padding: 0;
        margin: 0;
        height: 4rem;
        max-height: 5rem;
        width: 100%;
        background: linear-gradient(90deg, #E1F6FD 18.3%, #95B5E1 39.3%, #2C4CB3 79.8%);
        position: sticky;
        top: 0;
        z-index: 10;
        display: flex;
        align-items: center;
    }

    .titulo-mis-apps {
        color: #4B4B4B;
        font-size: 1.1rem;
        margin-top: 0.5rem;
    }

    .container-header {
        padding: 0.625rem 0.5rem;
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .container-logo-header {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .logo-header {
        width: 6rem;
        height: 2rem;
        object-fit: cover;
        margin-left: 0.5rem;
        cursor: pointer;
    }

    .container-Usuario-header {
        display: flex;
        gap: 0.3rem;
        justify-content: center;
        align-items: center;

    }

    .container-Usuario-header span {
        font-size: 0.9rem;
        color: #ffffff;
    }

    .modal-aplicaciones {
        position: absolute;
        top: 70%;
        left: 5%;
        background: white;
        padding: 1rem;
        box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
        border-radius: 0.8rem;
        z-index: 10;
        width: 50%;
        border: 0.0625rem solid #ccc;
        box-shadow: 0px 1px 3.8px 1px #4B4B4B33;

    }

    .card-aplicaciones {
        display: flex;
        flex-direction: column;
        /* Dos columnas */
        gap: 0.5rem;
    }

    .card-aplicacion-usuario {
        width: 100%;
        max-width: 100%;
        border-radius: 0.5rem;
        display: flex;
        justify-content: left;
        align-items: center;
        gap: 0.3125rem;
        cursor: pointer;
        padding: 0.5rem;
    }

    .card-aplicacion-usuario:hover {
        border: 0.0625rem solid rgb(221, 220, 220);
    }

    .card-aplicacion-usuario img {
        max-width: 2rem;
        max-height: 2rem;
        padding: 0.3125rem;
        border-radius: 0.5rem;
        border: 1px solid #D9D9D9
    }

    .titulo-app-usuario {
        color: black;
        font-size: 0.9rem;
        width: 100%;
        max-width: 100%;
        text-align: left;
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }

    .subtitulo-app-usuario {
        color: #7085c4;
        font-size: 0.8rem;
        font-weight: bold;
        width: 100%;
        max-width: 100%;
        text-align: left;
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }

    .div-button-aplicaciones {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        margin-top: 0.625rem;
        margin-bottom: 0.9375rem;
        font-size: 1rem;
    }

    .boton-administrar-app {
        width: 80%;
        margin: auto;
        padding: 0.2rem;
        border-radius: 0.3rem;
        border: none;
        color: #2C4CB3;
        border: 0.0625rem solid #2C4CB3;

    }

    .boton-administrar-app:hover {
        color: #2C4CB3;
        scale: 1.01;
    }

    .width-50-btn-aplicaciones {
        width: 100%;
        background-color: #F6F5F5 !important;
        color: #C9C1C1 !important;
        font-size: 1rem;
    }

    .width-50-btn-aplicaciones:hover {
        width: 100%;
        background-color: #2C4CB3 !important;
        color: #F6F5F5 !important;
        font-size: 1rem;
    }

    .img-perfil {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 0.9rem;
        cursor: pointer;
    }

    .modal-logout {
        position: absolute;
        top: 100%;
        left: 0;
        background-color: '#fff';
        box-shadow: '0px 4px 8px rgba(0,0,0,0.1)';
        padding: '0.625rem';
        border-radius: 0.5rem;
        z-index: 100;
    }

    .boton-cerrar-modal-eventos {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .leyenda-eventos-hoy {
        display: flex;
        max-width: 80%;
        gap: 0.3rem;
        justify-content: center;
        align-items: center;
    }

    .leyenda-evento-terminado {
        font-size: 0.6rem;
        padding: 0.2rem;
        border-radius: 1rem;
        font-weight: bold;
        color: white;
        background-color: red;
    }

    .leyenda-evento-proximo {
        font-size: 0.6rem;
        padding: 0.2rem;
        border-radius: 1rem;
        font-weight: bold;
        color: white;
        background-color: green;
    }

    .card-eventos-usuario {
        width: 100%;
        max-width: 100%;
        border-radius: 0.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: start;
        gap: 0.1rem;
        padding: 0.5rem;
        border: 1px solid rgb(221, 220, 220);

    }

    .evento-terminado {
        border: 1px solid rgb(211, 3, 3);

    }

    .evento-proximo {
        border: 1px solid rgb(35, 145, 1);

    }

    .card-eventos-usuario span {
        font-size: 0.8rem;
        color: #838383;

    }

    .modal-eventos-semana {
        position: absolute;
        top: 70%;
        right: 16%;
        background: white;
        padding: 1rem;
        box-shadow: 0px 4px 0.75rem rgba(0, 0, 0, 0.1);
        border-radius: 0.8rem;
        z-index: 10;
        width: 70%;
        border: 0.0625rem solid #ccc;
        box-shadow: 0px 1px 3.8px 1px #4B4B4B33;
        max-height: 30rem;
        overflow-y: auto;
        scrollbar-width: none;
    }

    @keyframes parpadeo {
        0%, 100% {
            opacity: 1;
        }

        50% {
            opacity: 0.8;
        }
    }

}