import React, { useEffect, useState } from "react";
import { <PERSON>, Step, Step<PERSON><PERSON><PERSON>, <PERSON>per, TextField } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import axios from "axios";
import Select from "react-select";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import BarraLateralCrearSolicitud from "../../BarraLateral/BarraLateralCrearSolicitud";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import IconoMoneda from "../../../../../../assets/SVG/IconoMoneda";
import IconoDolares from "../../../../../../assets/SVG/IconoSoles";
import IconoMueble from "../../../../../../assets/SVG/IconoMueble";
import IconoInmueble from "../../../../../../assets/SVG/IconoInmueble";
import getLogs from "../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
const CrearCompraVenta = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  NomTipoSolicitud
}) => {
  const [files, setFiles] = useState([]);
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [unidadesNegocios, setUnidadesNegocios] = useState([]);
  const [selectedUnidadesNegocios, setSelectedUnidadesNegocios] = useState("");
  const [selectedEmpresa, setSelectedEmpresa] = useState("");
  const [interlocutorEncontrado, setInterlocutorEncontrado] = useState(false);
  const [idInterlocutor, setIdInterlocutor] = useState(null);
  const [idInterlocutorComprador, setIdInterlocutorComprador] = useState(null);
  const [tipoDocumento, setTipoDocumento] = useState("");
  const [tipoDocumentoRepresentante, setTipoDocumentoRepresentante] =
    useState("");
  const [tipoDocumentoComprador, setTipoDocumentoComprador] = useState("");
  const [
    tipoDocumentoRepresentanteComprador,
    setTipoDocumentoRepresentanteComprador,
  ] = useState("");
  const [tipoBien, setTipoBien] = useState("Mueble");
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");
  const [interlocutorCompradorEncontrado, setInterlocutorCompradorEncontrado] =
    useState(false);
  const token = Cookies.get("Token");
  const [TiposMoneda, setTiposMoneda] = useState({str_Moneda: "", str_Pais: ""});

  const [errors, setErrors] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [FechaMinima , setFechaMinima] = useState(getLocalDate());

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };
  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioCreacion: UsuarioId,
    str_idSuscriptor: Suscripcion,
    int_idEmpresa: selectedEmpresa,
    int_idSolicitante: UsuarioId,
    int_idUnidadNegocio: selectedUnidadesNegocios,
    int_idTipoSol: idTipoSolicitud,
    int_SolicitudGuardada: null,
    str_DeTerceros: tipoModelo === "Propio" ? "no" : "si",
    dt_FechaEsperada: new Date().toISOString().split("T")[0],
    db_Honorarios: 0,
  });
  const [formDataContenido, setFormDataContenido] = useState({
    str_DocAdjuntos: files.length >= 1 ? "si" : "no",
    str_ObjetivoContrato: "",
    str_FormaPago: "",
    str_TipoServicio: "",
    str_InfoAdicional: "",
    int_idInterlocutor: null,
    int_idInterlocutorComprador: null,
    str_idSuscriptor: Suscripcion,
    int_idSolicitudes: null,
    str_BienDescripcion: "",
    str_BienMuebleInmueble: tipoBien,
    str_BienUso: "",
    str_BienDireccion: "",
    str_ImporteVenta: "",
    str_Moneda: selectedTipoMoneda,
    dt_FechaVenta: new Date().toISOString().split("T")[0],
    str_BienPartidaCertificada: "",
  });
  const [formDataInterlocutor, setFormDataInterlocutor] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_RepLegal: "",
    int_RLPartida: "",
    str_RLTipoDocumento: "Documento de identidad personal",
    str_Domicilio: "",
    str_RLDocumento: "",
  });
  const [formDataInterlocutorComprador, setFormDataInterlocutorComprador] =
    useState({
      str_idSuscripcion: Suscripcion,
      str_Interlocutor: "",
      str_TipoDoc: "Documento de identidad personal",
      str_Documento: "",
      int_idUsuarioCreacion: UsuarioId,
      str_RepLegal: "",
      int_RLPartida: "",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Domicilio: "",
      str_RLDocumento: "",
    });

  const handleChangeTipoBien = (event) => {
    const newTipo = event.target.value;
    setTipoBien(newTipo);
    setFormDataContenido((prevData) => ({
      ...prevData,
      str_BienMuebleInmueble: newTipo,
    }));
  };
  const handleChangeTipoDocumento = (event) => {
    const newTipo = event.target.value;
    setTipoDocumento(newTipo);
    setFormDataInterlocutor((prevData) => ({
      ...prevData,
      str_TipoDoc: newTipo,
    }));
  };
  const handleChangeTipoDocumentoRepresentante = (event) => {
    const newTipo = event.target.value;
    setTipoDocumentoRepresentante(newTipo);
    setFormDataInterlocutor((prevData) => ({
      ...prevData,
      str_RLTipoDocumento: newTipo,
    }));
  };
  const handleChangeTipoDocumentoComprador = (event) => {
    const newTipo = event.target.value;
    setTipoDocumentoComprador(newTipo);
    setFormDataInterlocutorComprador((prevData) => ({
      ...prevData,
      str_TipoDoc: newTipo,
    }));
  };
  const handleChangeTipoDocumentoRepresentanteComprador = (event) => {
    const newTipo = event.target.value;
    setTipoDocumentoRepresentanteComprador(newTipo);
    setFormDataInterlocutorComprador((prevData) => ({
      ...prevData,
      str_RLTipoDocumento: newTipo,
    }));
  };
  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);
    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  const validateForm = () => {
    let newErrors = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataInterlocutor.str_Documento)
      newErrors.str_Documento = "El Documento es requerido";
    if (!formDataInterlocutor.str_Interlocutor)
      newErrors.str_Interlocutor = "El proveedor es requerido";
   const today = new Date();
    today.setHours(0, 0, 0, 0); // Eliminamos la hora para comparar solo la fecha

    if (!formDataSolicitud.dt_FechaEsperada) {
        newErrors.dt_FechaEsperada = "La fecha estimada de entrega es requerida";
    } else if (new Date(formDataSolicitud.dt_FechaEsperada) < today) {
        newErrors.dt_FechaEsperada = "La fecha estimada no puede ser menor a hoy";
    }
    if (!formDataInterlocutor.str_RepLegal)
      newErrors.str_RepLegal = "El Representante legal es requerido";
    if (!formDataInterlocutor.str_Domicilio)
      newErrors.str_Domicilio = "el domicilio es requerido";
    if (!formDataInterlocutor.str_RLDocumento)
      newErrors.str_RLDocumento = "El documento del representante es requerido";
    if (!formDataInterlocutorComprador.str_Documento)
      newErrors.str_Documento2 = "El Documento es requerido";
    if (!formDataInterlocutorComprador.str_Interlocutor)
      newErrors.str_Interlocutor2 = "El proveedor es requerido";
    if (!formDataInterlocutorComprador.str_RepLegal)
      newErrors.str_RepLegal2 = "El Representante legal es requerido";
    if (!formDataInterlocutorComprador.str_Domicilio)
      newErrors.str_Domicilio2 = "el domicilio es requerido";
    if (!formDataInterlocutorComprador.str_RLDocumento)
      newErrors.str_RLDocumento2 = "El documento del representante es requerido";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.str_FormaPago)
      newErrors.str_FormaPago = "La forma de pago es requerida";
    if (!formDataContenido.str_BienDescripcion)
      newErrors.str_BienDescripcion = "La descripción del bien es requerida";
    if (!formDataContenido.str_BienUso)
      newErrors.str_BienUso = "El uso del Bien es Requerido ";
    if (!formDataContenido.str_BienDireccion)
      newErrors.str_BienDireccion = "La dirección del bien es requerido";
    if (!formDataContenido.str_BienPartidaCertificada)
      newErrors.str_BienPartidaCertificada =
        "La  partida registral del bien es requerido";
    if (!formDataContenido.str_ImporteVenta)
      newErrors.str_ImporteVenta = "La descripcion del bien es requerido";
    if (!formDataInterlocutorComprador.int_RLPartida)
      newErrors.int_RLPartida2 = "El proveedor es requerido";
    if (!formDataInterlocutor.int_RLPartida)
      newErrors.int_RLPartida= "El proveedor es requerido";
    setErrors(newErrors);
    // Retornar true si no hay errores
    return Object.keys(newErrors).length === 0;
  };

  const steps = [
    "DATOS GENERALES",
    "DATOS DE CONTRATO",
    "INFORMACIÓN ADICIONAL",
  ];
  useEffect(() => {
    const fetchEmpresas = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEmpresas"](idAplicacion, Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
        if (opciones.length > 0) {
          setSelectedEmpresa(opciones[0].value);
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    fetchEmpresas();
  }, []);
  useEffect(() => {
    const fetchUnidadesNegocios = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerUnidadesNegocios"](Suscripcion,selectedEmpresa),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idUnidadesNegocio: any; str_Descripcion: any }) => ({
            value: tipo.int_idUnidadesNegocio,
            label: tipo.str_Descripcion,
          })
        );
        setUnidadesNegocios(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](selectedEmpresa, Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
            try {
              const response = await axios.get(
                API_GESTOR["ObtenerTiempoRespuestaTS"](Suscripcion,idTipoSolicitud,selectedEmpresa),{
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                  }
                }
              );
              const responseData = response.data;
              const ultimoObjeto = responseData.length > 0 ? responseData[responseData.length - 1] : null;

              const nuevaFechaEsperada = addDaysToDate(new Date(), ultimoObjeto.int_TiempoRespuesta ? ultimoObjeto.int_TiempoRespuesta : 0);
              setFormDataSolicitud(prevState => ({
                ...prevState,
                dt_FechaEsperada: nuevaFechaEsperada,
              }));
              setFechaMinima(nuevaFechaEsperada);

            } catch (error) {
              console.error("Error al obtener tipos de solicitud:", error);
            }
          };
    ObtenerTiempoRespuestaTipoSolicitud();
    fetchUnidadesNegocios();
    fetchMoneda();

  } , [selectedEmpresa]);
  useEffect(() => {
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      int_idEmpresa: selectedEmpresa,
      int_idUnidadNegocio: selectedUnidadesNegocios,
    }));
  }, [selectedEmpresa, selectedUnidadesNegocios, formDataContenido]);
  const handleChangeEmpresa = (selectedOption: React.SetStateAction<null>) => {
    setSelectedEmpresa(selectedOption.value);
  };
  const handleChangeUnidadesNegocios = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedUnidadesNegocios(selectedOption.value);
  };

  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };

  const handleFileRemove = (index) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      dt_FechaEsperada: `${selectedDate}T00:00:00`,
    }));
  };

  const handleDateChangeCompra = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value;
    setFormDataContenido((prevData) => ({
      ...prevData,
      dt_FechaVenta: `${selectedDate}T00:00:00`,
    }));
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataInterlocutor((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    if (name.startsWith("Comprador_")) {
      const fieldName = name.replace("Comprador_", "");
      setFormDataInterlocutorComprador((prevData) => ({
        ...prevData,
        [fieldName]: value,
      }));
    }
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: response.data.str_Interlocutor,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio,
          str_RLDocumento: response.data.str_RLDocumento,
        }));
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);
        setTipoDocumento(response.data.str_TipoDoc);
        setTipoDocumentoRepresentante(response.data.str_RLTipoDocumento);
      } else {
        setInterlocutorEncontrado(false);
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_RepLegal: "",
          int_RLPartida: "",
          str_RLTipoDocumento: "Documento de identidad personal",
          str_Domicilio: "",
          str_RLDocumento: "",
        }));
        setTipoDocumento("");
        setIdInterlocutor(null);
        setTipoDocumentoRepresentante("");
      }
    } catch (error) {

      setInterlocutorEncontrado(false);
      setFormDataInterlocutor((prevData) => ({
        ...prevData,
        str_Interlocutor: "",
        str_RepLegal: "",
        int_RLPartida: "",
        str_RLTipoDocumento: "Documento de identidad personal",
        str_Domicilio: "",
        str_RLDocumento: "",
      }));
      setTipoDocumento("");
      setTipoDocumentoRepresentante("");
      setIdInterlocutor(null);
    }
  };
  const buscarInterlocutor2 = async (ruc: string) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        setFormDataInterlocutorComprador((prevData) => ({
          ...prevData,
          str_Interlocutor: response.data.str_Interlocutor,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_RLTipoDocumento: response.data.str_RLTipoDocumento || "Documento de identidad personal",
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio,
          str_RLDocumento: response.data.str_RLDocumento,
        }));
        setIdInterlocutorComprador(response.data.int_idInterlocutor);
        setInterlocutorCompradorEncontrado(true);
        setTipoDocumentoComprador(response.data.str_TipoDoc);
        setTipoDocumentoRepresentanteComprador(
          response.data.str_RLTipoDocumento
        );
      } else {
        setInterlocutorCompradorEncontrado(false);
        setFormDataInterlocutorComprador((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_RepLegal: "",
          int_RLPartida: "",
          str_RLTipoDocumento: "Documento de identidad personal",
          str_Domicilio: "",
          str_RLDocumento: "",
        }));
        setIdInterlocutorComprador(null);
        setTipoDocumentoRepresentanteComprador("");
        setTipoDocumentoComprador("");
      }
    } catch (error) {

      setInterlocutorCompradorEncontrado(false);
      setFormDataInterlocutorComprador((prevData) => ({
        ...prevData,
        str_Interlocutor: "",
        str_RepLegal: "",
        int_RLPartida: "",
        str_RLTipoDocumento: "Documento de identidad personal",
        str_Domicilio: "",
        str_RLDocumento: "",
      }));
      setIdInterlocutorComprador(null);
      setTipoDocumentoRepresentanteComprador("");
      setTipoDocumentoComprador("");
    }
  };

  const handleSubmitInterlocutores = async (idSolicitud,guardado) => {
    await validateToken();
    try {
      if (interlocutorEncontrado && interlocutorCompradorEncontrado) {
        const formDataModificado = {
          ...formDataInterlocutor,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificado.int_idUsuarioCreacion;

        const formDataModificadaComprador = {
          ...formDataInterlocutorComprador,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificadaComprador.int_idUsuarioCreacion;

        const response = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutor),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response2 = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutorComprador),
          formDataInterlocutorComprador,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }
        if (response2.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }
        handleSubmitContenidoSolicitud(
          idSolicitud,
          idInterlocutor,
          idInterlocutorComprador,
          guardado
        );

      } else if (!interlocutorEncontrado && interlocutorCompradorEncontrado) {
        const response = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response2 = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutorComprador),
          formDataInterlocutorComprador,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }

        handleSubmitContenidoSolicitud(
          idSolicitud,
          response.data.int_idInterlocutor,
          idInterlocutorComprador,
          guardado
        );
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);

      } else if (interlocutorEncontrado && !interlocutorCompradorEncontrado) {
        const response = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutor),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response2 = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutorComprador,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response2.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }
        handleSubmitContenidoSolicitud(
          idSolicitud,
          idInterlocutor,
          response2.data.int_idInterlocutor,
          guardado
        );
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);

      } else {
        const response = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response2 = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutorComprador,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        handleSubmitContenidoSolicitud(
          idSolicitud,
          response.data.int_idInterlocutor,
          response2.data.int_idInterlocutor,
          guardado
        );
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);

      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };

  const EstadoNuevo = async (idSolicitud, estado) => {
    await validateToken();
    try {
      await axios.put(
        API_SOLICITANTE["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: estado,
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: idSolicitud,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
    }
  };
  const obtenerGestorConMenorSolicitudes = async () => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["ObtenerGestoresTipoSolicitud"](
          Suscripcion,
          idTipoSolicitud,
         selectedEmpresa
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = response.data;

      if (!gestores || gestores.length === 0) {

        return null;
      }

      // Obtener el número de solicitudes activas para cada gestor
      const solicitudesPorGestor = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            const contadorResponse = await axios.get(
              API_SOLICITANTE["ContadorPorGestor"](gestor.int_idGestor,Suscripcion), {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            const solicitudes_Activas = contadorResponse.data.total_no_firmado;



            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas,
            };
          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idGestor}:`,
              error
            );
            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas: Infinity, // Asignar un valor alto para evitar seleccionarlo
            };
          }
        })
      );

      // Encontrar el gestor con menor número de solicitudes activas
      const gestorConMenorSolicitudes = solicitudesPorGestor.reduce(
        (minGestor, currentGestor) => {
          return currentGestor.solicitudes_Activas <
            minGestor.solicitudes_Activas
            ? currentGestor
            : minGestor;
        },
        { int_idGestor: null, solicitudes_Activas: Infinity }
      );



      return gestorConMenorSolicitudes.int_idGestor;
    } catch (error) {

      return null;
    }
  };
  const handleAsignarGestor = async (idSolicitud, gestorSeleccionado) => {
    await validateToken();
    try {
      const response = await axios.put(
        API_SOLICITANTE["AsignarGestor"](),
        {
          int_idSolicitudes: idSolicitud,
          int_idGestor: gestorSeleccionado,
          int_idUsuarioModificacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        console.error(`Gestor Asignado Correctamente ${response}`);
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitGuardarSolicitud = async () => {
    await validateToken();
    if (!selectedEmpresa || !selectedUnidadesNegocios) {
      Swal.fire(
        "Error",
        "Debe seleccionar una empresa y una unidad de negocio para registrar",
        "error"
      );
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 1,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        await handleSubmitInterlocutores(response.data.int_idSolicitudes,1);
        if (files.length >= 1) {
          handleSubmitFiles(
            response.data.int_idSolicitudes,
            response.data.str_CodSolicitudes
          );
        }
        await getLogs(response.data.int_idSolicitudes,null,response.data.int_idSolicitudes,"Solicitudes","Solicitudes","Crear Solicitud","Contratos","POST");

      }


    } catch (error) {
      return error
    }
  };
  const handleSubmitConfirmarSolicitud = async () => {
    await validateToken();
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop();
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 0,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        await handleSubmitInterlocutores(response.data.int_idSolicitudes,2);



        if (files.length >= 1) {
          handleSubmitFiles(
            response.data.int_idSolicitudes,
            response.data.str_CodSolicitudes
          );
        }
        await getLogs(response.data.int_idSolicitudes,null,response.data.int_idSolicitudes,"Solicitudes","Solicitudes","Crear Solicitud","Contratos","POST");

      }

    } catch (error) {
      return error
    }
  };
  const handleSubmitContenidoSolicitud = async (
    idSolicitud,
    idInterlocutor,
    idInterlocutorComprador,
    guardado
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idSolicitudes: idSolicitud,
        int_idInterlocutor: idInterlocutor,
        int_idInterlocutorComprador: idInterlocutorComprador,
      };
      const response = await axios.post(
        API_SOLICITANTE["InsertarContenidoSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        if (guardado === 1) {
          await EstadoNuevo(idSolicitud,"Nuevo")
          Swal.fire("", "Solicitud Guardada ", "success");
          navigate(RoutesPrivate.INICIOSOLICITANTE);

        }else{
          await EstadoNuevo(idSolicitud,"Nuevo")
          const idGestorAsignado = await obtenerGestorConMenorSolicitudes();
          if (!idGestorAsignado) {
            Swal.fire(
              "Error",
              "No hay gestores disponibles para asignar la solicitud",
              "error"
            );
            return;
          }
          await handleAsignarGestor(idSolicitud, idGestorAsignado);
          await EstadoNuevo(idSolicitud,"Asignado")
          Swal.fire("", "Solicitud Confirmada ", "success");
          navigate(RoutesPrivate.INICIOSOLICITANTE);
        }
      }

    } catch (error) {
      return error
    }
  };
  const handleSubmitFiles = async (idSolicitud, codSolicitud) => {
    await validateToken();
    try {
      for (const file of files) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);
        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append("str_CodSolicitudes", codSolicitud);
        formDataSolicitud.append("int_idSolicitudes", idSolicitud);

        // Enviar el tipo_adjunto como campo separado
        if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }

        // Mantener str_CodTipoDocumento para compatibilidad (usar "DOAD" por defecto)
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");

        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_SOLICITANTE["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }
      }
    } catch (error) {
      console.error("Error al subir archivos:", error);
    }
  };
  const isInvalidLength = (type, value, requiredLength) =>
    type === "RUC" && value.length < requiredLength && value.length > 0;

  const isInvalidDNILength = (type, value, requiredLength) =>
    type === "DNI" && value.length < requiredLength && value.length > 0;

  const handleSubmitSolicitudGuardar = () => {


    handleSubmitGuardarSolicitud();
  };
  const handleSubmitSolicitudConfirmar = () => {

    handleSubmitConfirmarSolicitud();
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder={`${Nombres} ${Apellidos}`}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <Select
                        options={empresasFiltro}
                        value={empresasFiltro.find(
                          (option) => option.value === selectedEmpresa
                        )}
                        onChange={handleChangeEmpresa}
                        placeholder="Empresa"
                      />
                      {errors.int_idEmpresa && (
                        <span className="error-message">
                          {errors.int_idEmpresa}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <Select
                        options={unidadesNegocios}
                        value={unidadesNegocios.find(
                          (option) => option.value === selectedUnidadesNegocios
                        )}
                        onChange={handleChangeUnidadesNegocios}
                        placeholder="Unidad de Negocio"
                      />
                      {errors.int_idUnidadNegocio && (
                        <span className="error-message">
                          {errors.int_idUnidadNegocio}
                        </span>
                      )}
                    </div>
                  </div>
                  <hr className="separador-linea" />
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Vendedor
                  </div>
                  <div className="inputs-crear-solicitud">
                  <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento de Representante:</label>
                      <select name="str_RLTipoDocumento" id="tipoDocumento" className="form-select" onChange={handleInputChange}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${errors.str_Documento && "is-invalid"}`}
                        name="str_Documento"
                        value={formDataInterlocutor.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value);
                            }
                          }
                        }}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="str_RLDocumento"
                        className={`form-control ${errors.str_RLDocumento && "is-invalid"}`}
                        value={formDataInterlocutor.str_RLDocumento}
                        placeholder=""
                        onChange={(e) => {
                          const maxLength =
                            15;
                          if (e.target.value.length <= maxLength) {
                            handleInputChange(e);
                          }
                        }}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Vendedor:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_Interlocutor && "is-invalid"}`}
                        name="str_Interlocutor"
                        value={formDataInterlocutor.str_Interlocutor}
                        onChange={handleInputChange}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante Legal:</label>
                      <input
                        type="text"
                        name="str_RepLegal"
                        className={`form-control ${errors.str_RepLegal && "is-invalid"}`}
                        value={formDataInterlocutor.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_Domicilio && "is-invalid"}`}
                        name="str_Domicilio"
                        value={formDataInterlocutor.str_Domicilio}
                        onChange={handleInputChange}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Partida Registral : </label>
                      <input
                        type="text"
                        name="int_RLPartida"
                        className={`form-control ${errors.int_RLPartida && "is-invalid"}`}
                        value={formDataInterlocutor.int_RLPartida}
                        placeholder=""
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <hr className="separador-linea" />
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos del Comprador
                  </div>

                  <div className="inputs-crear-solicitud">
                  <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="Comprador_str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento de Representante:</label>
                      <select name="Comprador_str_RLTipoDocumento" id="tipoDocumento" className="form-select" onChange={handleInputChange}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>

                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${errors.str_Documento2 && "is-invalid"}`}
                        name="Comprador_str_Documento"
                        value={formDataInterlocutorComprador.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor2(value);
                            }
                          }
                        }}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="Comprador_str_RLDocumento"
                        className={`form-control ${errors.str_RLDocumento2 && "is-invalid"}`}
                        value={formDataInterlocutorComprador.str_RLDocumento}
                        placeholder=""
                        onChange={(e) => {
                          const maxLength =
                            15;
                          if (e.target.value.length <= maxLength) {
                            handleInputChange(e);
                          }
                        }}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Comprador:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_Interlocutor2 && "is-invalid"}`}
                        name="Comprador_str_Interlocutor"
                        value={formDataInterlocutorComprador.str_Interlocutor}
                        onChange={handleInputChange}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante Legal:</label>
                      <input
                        type="text"
                        name="Comprador_str_RepLegal"
                        className={`form-control ${errors.str_RepLegal2 && "is-invalid"}`}
                        value={formDataInterlocutorComprador.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_Domicilio2 && "is-invalid"}`}
                        name="Comprador_str_Domicilio"
                        value={formDataInterlocutorComprador.str_Domicilio}
                        onChange={handleInputChange}
                      />

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Partida Registral : </label>
                      <input
                        type="text"
                        name="Comprador_int_RLPartida"
                        className={`form-control ${errors.int_RLPartida2 && "is-invalid"}`}
                        value={formDataInterlocutorComprador.int_RLPartida}
                        placeholder=""
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Datos del contrato
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Bien:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div className={`check-group form-check-inline ${tipoBien === "Mueble" && "check-selected"}`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_BienMuebleInmueble"
                            id="mueble"
                            value="Mueble"
                            checked={tipoBien === "Mueble"}
                            onChange={handleChangeTipoBien}
                          />
                          <label className="form-check-label" htmlFor="mueble"><IconoMueble size={"1.3rem"} color={tipoBien === "Mueble" ? "#156CFF" : "#C5DBFF"}/> Mueble</label>
                        </div>
                        <div className={`check-group form-check-inline ${tipoBien === "Inmueble" && "check-selected"}`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_BienMuebleInmueble"
                            id="inmueble"
                            value="Inmueble"
                            checked={tipoBien === "Inmueble"}
                            onChange={handleChangeTipoBien}
                          />
                          <label className="form-check-label" htmlFor="inmueble"><IconoInmueble size={"1.3rem"} color={tipoBien === "Inmueble" ? "#156CFF" : "#C5DBFF"}/> Inmueble</label>
                        </div>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Descripción del Bien</label>
                      <textarea
                        className={`form-control ${errors.str_BienDescripcion && "is-invalid"}`}
                        id=""
                        name="str_BienDescripcion"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Uso del Bien</label>
                      <textarea
                        className={`form-control ${errors.str_BienUso && "is-invalid"}`}
                        id=""
                        name="str_BienUso"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Domicilio/ Dirección del bien:
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_BienDireccion && "is-invalid"}`}
                        placeholder=""
                        name="str_BienDireccion"
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Objeto del contrato
                      </label>
                      <textarea
                        className={`form-control ${errors.str_ObjetivoContrato && "is-invalid"}`}
                        id=""
                        name="str_ObjetivoContrato"
                        rows={3}
                        onChange={handleTextareaChange}
                      ></textarea>

                    </div>

                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Partida Registral / Certificado:
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_BienPartidaCertificada && "is-invalid"}`}
                        placeholder=""
                        name="str_BienPartidaCertificada"
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Datos de la Compra / venta
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Importe Venta:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_ImporteVenta && "is-invalid"}`}
                        placeholder=""
                        name="str_ImporteVenta"
                        onChange={handleInputChange}
                      />

                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Forma de Pago:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_FormaPago && "is-invalid"}`}
                        placeholder=""
                        name="str_FormaPago"
                        onChange={handleInputChange}
                      />

                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Moneda:</label>
                      <div className="radio-inputs-crear-solicitud">
                      {TiposMoneda.str_Moneda !== "Dolares" && (
                          <div
        className={`check-group form-check-inline ${
          selectedTipoMoneda === "dolares" ? "check-selected" : ""
        }`}
      >
        <input
          className="form-check-input"
          type="radio"
          name="inlineRadioOptions"
          id="monedaDolares"
          value="dolares"
          checked={selectedTipoMoneda === "dolares"}
          onChange={handleChangeTipoMoneda}
        />
        <label className="form-check-label" htmlFor="monedaDolares">
          <IconoDolares size={"1.5rem"} color={"#156CFF"} /> Dólares
        </label>
      </div>
                        )}

                        {Object.keys(TiposMoneda).length > 0 && (
                          <div
        className={`check-group form-check-inline ${
          selectedTipoMoneda === "empresa" ? "check-selected" : ""
        }`}
      >
        <input
          className="form-check-input"
          type="radio"
          name="tipoMoneda"
          id="tipoMoneda"
          value={"empresa"}
          checked={selectedTipoMoneda === "empresa"}
          onChange={handleChangeTipoMoneda}
        />
        <label className="form-check-label" htmlFor="tipoMoneda">
          <IconoMoneda size={"1.5rem"} color={"#156CFF"} /> {TiposMoneda.str_Moneda}
        </label>
      </div>
                        )}
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Fecha de Venta: </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        min={new Date().toISOString().split("T")[0]}
                        className="form-control"
                        value={
                            formDataContenido.dt_FechaVenta
                              ? formDataContenido.dt_FechaVenta.split("T")[0]
                              : ""
                          }
                        onChange={handleDateChangeCompra}
                        name="dt_FechaVenta"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(2)}
                >
                  Información adicional
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="text-area-crear-solicitud">
                    <label className="form-label">Condiciones del Servicio</label>
                    <textarea
                      class="form-control"
                      name="str_InfoAdicional"
                      id="exampleFormControlTextarea1"
                      rows={3}
                      onChange={handleTextareaChange}
                    ></textarea>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha Esperada de entrega:{" "}
                      </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        min={FechaMinima}
                        className="form-control"
                        value={
                            formDataSolicitud.dt_FechaEsperada
                              ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                              : ""
                          }
                        onChange={handleDateChange}
                        name="dt_FechaEsperada"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        NomTipoSolicitud={NomTipoSolicitud}
      />
    </div>
  );
};

export default CrearCompraVenta;
