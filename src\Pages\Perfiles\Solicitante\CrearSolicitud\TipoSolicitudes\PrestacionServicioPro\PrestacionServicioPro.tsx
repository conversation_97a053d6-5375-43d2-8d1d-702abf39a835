import CrearPrestacionServicioPro from "./Crear"
import EditarPrestacionServicioPro from "./Editar"


const PrestacionServicioPro = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  esEdicion,
  selectedSolicitud,
  ver,
  NomTipoSolicitud
}) => {
  return (
    <>    
    {!esEdicion ? 
      <CrearPrestacionServicioPro Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} idTipoSolicitud={idTipoSolicitud} tipoModelo={tipoModelo} NomTipoSolicitud={NomTipoSolicitud} />
      :
      <EditarPrestacionServicioPro Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} selectedSolicitud={selectedSolicitud} tipoModelo={tipoModelo} esEdicion={esEdicion} ver={ver} NomTipoSolicitud={NomTipoSolicitud}/>

    }
    </>

  )
}

export default PrestacionServicioPro