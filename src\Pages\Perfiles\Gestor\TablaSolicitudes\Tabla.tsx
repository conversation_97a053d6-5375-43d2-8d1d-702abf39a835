import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import "./tabla.css";
import BarraLateral from "../Partials/BarraLateral/BarraLateral";
import API_GESTOR from "../../../../assets/Api/ApisGestor";
import axios from "axios";
import Select from "react-select";
import TagInput from "../../Solicitante/Partials/Filtro/TagInput";
import { Tooltip } from "@mui/material";
import CircleEstado from "../../../../assets/SVG/CircleEstado";
import { useLocation } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { decrypt } from "../../../Components/Services/TokenService";
import IconoLeft from "../../../../assets/SVG/IconoLeft";
import IconoRight from "../../../../assets/SVG/IconoRight";
import getLogs from "../../../Components/Services/LogsService";

const anios = Array.from({ length: 21 }, (_, i) => 2010 + i);
const meses = [
  { value: "01", label: "Enero" },
  { value: "02", label: "Febrero" },
  { value: "03", label: "Marzo" },
  { value: "04", label: "Abril" },
  { value: "05", label: "Mayo" },
  { value: "06", label: "Junio" },
  { value: "07", label: "Julio" },
  { value: "08", label: "Agosto" },
  { value: "09", label: "Septiembre" },
  { value: "10", label: "Octubre" },
  { value: "11", label: "Noviembre" },
  { value: "12", label: "Diciembre" },
];
const fechaHoy = new Date().toISOString().split("T")[0];
const fechaAyer = new Date(new Date().setDate(new Date().getDate() - 1))
  .toISOString()
  .split("T")[0];
const fechaMañana = new Date(new Date().setDate(new Date().getDate() + 1))
  .toISOString()
  .split("T")[0];
function formatDate(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}

const Tabla = ({ perfil }) => {
  const location = useLocation();
  const [isFilter, setIsFilter] = useState(false);
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const Suscriptor = decrypt(Cookies.get("suscriptor"));
  const idAplicacion = decrypt(Cookies.get("idAplicacion"));
  const [anioSeleccionado, setAnioSeleccionado] = useState("");
  const [mesSeleccionado, setMesSeleccionado] = useState("");
  const [mostrarArchivadas, setMostrarArchivadas] = useState(false);
  const [selectedTipoSolicitudFiltro, setSelectedTipoSolicitudFiltro] =
    useState("");
  const [estadosFiltro, setEstadosFiltro] = useState([]);
  const [selectedEstadoFiltro, setSelectedEstadoFiltro] = useState("");
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [selectedEmpresaFiltro, setSelectedEmpresaFiltro] = useState("");
  const [tags, setTags] = useState([]);
  const [solicitudes, setSolicitudes] = useState([]);
  const [selectedSolicitud, setSelectedSolicitud] = useState({});
  const [tipoSolicitudes, setTipoSolicitudes] = useState([]);
  const [HistorialesUsuario, setHistorialesUsuario] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRow, setSelectedRow] = useState(null);
  const [tipoController, setTipoController] = useState("gestor");
  const [codSolicitudFiltro, setCodSolicitudFiltro] = useState("");
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [documentoCliente, setDocumentoCliente] = useState("");
  const [razonSocial, setRazonSocial] = useState("");
  const token = Cookies.get("Token");

  const solicitudesPorPagina = 8;
  const indiceUltimaSolicitud = currentPage * solicitudesPorPagina;
  const indicePrimeraSolicitud = indiceUltimaSolicitud - solicitudesPorPagina;
  const solicitudesActuales = solicitudes.slice(
    indicePrimeraSolicitud,
    indiceUltimaSolicitud
  );
  const handlePageChange = (pageNumber: React.SetStateAction<number>) => {
    setCurrentPage(pageNumber);
  };
  const totalPaginas = Math.ceil(solicitudes.length / solicitudesPorPagina);
  const baseUrl = import.meta.env.VITE_BASE_URL;

  const renderNumeritosPaginacion = () => {
    const numeritos = [];
    for (let i = 1; i <= totalPaginas; i++) {
      numeritos.push(
        <button
          key={i}
          className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }
    return numeritos;
  };
  const AbrirFiltro = () => {
    setIsModalVisible(!isModalVisible);
  };
  const CerrarFiltro = () => {
    setIsModalVisible(false);
  };
  const obtenerSolicitudesUsuario = async () => {
    try {
      const tagsParams = tags.map((tag) => `&str_descripcion=${tag}`).join("");

      const response = await axios.get(
        perfil === "Gestor Controller"
          ? `${baseUrl}api/solicitudes/filtrar-controller/?str_idSuscriptor=${Suscripcion}&int_idGestor=${idUsuario}${
              tipoController === "controller"
                ? `&isController=${tipoController}`
                : ""
            }${mostrarArchivadas ? "" : "&str_Visible=si"}${
              selectedTipoSolicitudFiltro
                ? `&str_NombreTipoSolicitud=${selectedTipoSolicitudFiltro}`
                : ""
            }${selectedEstadoFiltro ? `&estado=${selectedEstadoFiltro}` : ""}${
              anioSeleccionado ? `&anio_fecha_registro=${anioSeleccionado}` : ""
            }${
              mesSeleccionado ? `&mes_fecha_registro=${mesSeleccionado}` : ""
            }${tagsParams}${
              selectedEmpresaFiltro
                ? `&str_idEmpresa=${selectedEmpresaFiltro}`
                : ""
            }${
              codSolicitudFiltro
                ? `&str_CodSolicitudes=${codSolicitudFiltro}`
                : ""
            }`
          : `${baseUrl}api/solicitudes/filtrar-gestor/?str_idSuscriptor=${Suscripcion}&int_idGestor=${idUsuario}${
              mostrarArchivadas ? "" : "&str_Visible=si"
            }${
              selectedTipoSolicitudFiltro
                ? `&str_NombreTipoSolicitud=${selectedTipoSolicitudFiltro}`
                : ""
            }${selectedEstadoFiltro ? `&estado=${selectedEstadoFiltro}` : ""}${
              anioSeleccionado ? `&anio_fecha_registro=${anioSeleccionado}` : ""
            }${
              mesSeleccionado ? `&mes_fecha_registro=${mesSeleccionado}` : ""
            }${tagsParams}${
              selectedEmpresaFiltro
                ? `&str_idEmpresa=${selectedEmpresaFiltro}`
                : ""
            }${
              codSolicitudFiltro
                ? `&str_CodSolicitudes=${codSolicitudFiltro}`
                : ""
            }${
              documentoCliente
                ? `&str_documentoCliente=${documentoCliente}`
                : ""
            }${razonSocial ? `&str_razonSocial=${razonSocial}` : ""}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      setSolicitudes(response.data);
      await getLogs(null,null,null,"Listado Solicitudes","Solicitudes","Ver Solicitudes","Contratos","GET");

    } catch (error: any) {
      console.error("Error al obtener datos:", error.message);
    }
  };
  useEffect(() => {
    const fetchTipoSolicitudes = async () => {
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerTipoSolicitud"](Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: {
            int_idTipoSolicitud: any;
            int_Nombre: any;
            str_CodTipoSol: any;
          }) => ({
            value: tipo.int_Nombre,
            label: tipo.int_Nombre,
            codigo: tipo.str_CodTipoSol,
            id: tipo.int_idTipoSolicitud,
          })
        );
        setTipoSolicitudes(opciones);
 
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchEstadosFiltro = async () => {
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerEstadosSolicitud"](Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEstado: any; int_Nombre: any }) => ({
            value: tipo.int_Nombre,
            label: tipo.int_Nombre,
          })
        );
        setEstadosFiltro(opciones);
 
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchEmpresasFiltro = async () => {
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerEmpresas"](idAplicacion, Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
 
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    fetchEstadosFiltro();
    obtenerSolicitudesUsuario();
    fetchTipoSolicitudes();
    fetchEmpresasFiltro();
  }, [isFilter, tipoController]);
  const handleFiltrar = () => {
    setIsFilter(true);
    obtenerSolicitudesUsuario();
    handlePageChange(1);
    CerrarFiltro();
    renderNumeritosPaginacion();
  };
  const SubmitObtenerDatos = () => {
    obtenerSolicitudesUsuario();
  };
  const handleChangeTipoSolicitudFiltro = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedTipoSolicitudFiltro(selectedOption.value);
  };
  const handleChangeestadoFiltro = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedEstadoFiltro(selectedOption.value);
  };
  const handleChangeEmpresaFiltro = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedEmpresaFiltro(selectedOption.value);
  };
  const handleAnioChange = (e) => {
    setAnioSeleccionado(e.target.value);
  };
  const handleTipoController = (e) => {
    setTipoController(e.target.value);
  };
  const handleChangeMostrarArchivados = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setMostrarArchivadas(event.target.checked);
  };
  const handleMesChange = (e) => {
    setMesSeleccionado(e.target.value);
  };
  const HistorialUsuario = async (idSolicitud) => {
    try {
      const response = await axios.get(
        API_GESTOR["HistorialUsuario"](Suscripcion, idSolicitud),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = response.data;

      setHistorialesUsuario(data);
 
    } catch (error) {
      console.error("Error al obtener el historial del usuario:", error);
    }
  };
  const CambiarBarraLateral = (solicitud) => {
    setSelectedSolicitud(solicitud);
    HistorialUsuario(solicitud.int_idSolicitudes);
  };
  const BorrarTodosFiltros = () => {
    setMostrarArchivadas(false);
    setSelectedTipoSolicitudFiltro("");
    setSelectedEstadoFiltro("");
    setSelectedEmpresaFiltro("");
    setMesSeleccionado("");
    setAnioSeleccionado("");
    setCodSolicitudFiltro("");
    setDocumentoCliente("");
    setRazonSocial("");
    setTags([]);
    setIsModalVisible(false);
    setIsFilter(false);
  };
  const handleRowClick = (index) => {
    setSelectedRow(index);
  };
  const handleCopy = (text) => {
    if (text.length > 14) {
      text = text.substring(0, 14);
    }

    setIsNotificationOpen(true);

    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success(`Copiado: ${text}`, {
          position: "top-center",
          autoClose: 2000,
          onClose: () => setIsNotificationOpen(false),
        });
      })
      .catch((err) => {
        toast.error("Error al copiar el código", {
          position: "top-center",
          autoClose: 2000,
          onClose: () => setIsNotificationOpen(false),
        });
        console.error("Error al copiar el código:", err);
      });
  };
  
  const recargarSolicitudes = () => {
    obtenerSolicitudesUsuario();
    setSelectedRow(null);
    setSelectedSolicitud({});
   }
  return (
    <div style={{ width: "100%" }}>
      <div
        className="div-boton-filtrar-inicio-solicitante montserrat-font"
        style={{ gap: "0.5rem" }}
      >
        <button
          className="boton-filtrar-inicio-solicitante montserrat-font"
          onClick={AbrirFiltro}
        >
          <i className="fa-solid fa-sliders"></i> Filtrar
        </button>
        {perfil === "Gestor Controller" && (
          <select
            name=""
            id=""
            onChange={handleTipoController}
            className="form-select"
            style={{ width: "20%" }}
          >
            <option value={"gestor"}>Gestor</option>
            <option value={"controller"}>Controller</option>
          </select>
        )}
      </div>
      <div className="contenedor-solicitudes-tabla-solicitudes">
        <div className="div-tabla-inicio-solicitante">
          <table className="tabla-inicio-solicitante">
            <thead>
              <tr>
                <th>Num. Solicitud</th>
                <th>Fech. Solicitud</th>
                <th>Fech. Esperada</th>
                <th>Empresa</th>
                <th>Monto del contrato</th>
                <th>Estado</th>
              </tr>
            </thead>
            <tbody>
              {solicitudesActuales.length === 0 ? (
                <tr>
                  <td colSpan={6} style={{ textAlign: "center" }}>
                    No se encontraron registros
                  </td>
                </tr>
              ) : (
                solicitudesActuales.map((solicitud, index) => {
                  const fechaSolicitud = solicitud.dt_FechaEsperada
                    ? solicitud.dt_FechaEsperada.split("T")[0]
                    : null;

                  // Comparar si es hoy, ayer o mañana
                  const esHoy = fechaSolicitud === fechaHoy;
                  const esAyer = fechaSolicitud === fechaAyer;
                  const esMañana = fechaSolicitud === fechaMañana;
                  const esEstadoCritico =
                    solicitud.estado_nombre === "Asignado" ||
                    solicitud.estado_nombre === "En Proceso";

                  // Determina la clase de alerta dependiendo de si la fecha es hoy, ayer o mañana
                  const alertaClase = esEstadoCritico
                    ? esHoy
                      ? "alerta-fecha-proxima" // Alerta si es hoy
                      : esAyer
                      ? "alerta-fecha-pasada" // Alerta si es ayer
                      : esMañana
                      ? "alerta-fecha-proxima" // Alerta si es mañana
                      : ""
                    : "";

                  // Define el título de la alerta dependiendo de la fecha
                  const tituloAlerta = esEstadoCritico
                    ? esHoy
                      ? "Solicitud con fecha próxima"
                      : esAyer
                      ? "Solicitud con fecha pasada"
                      : esMañana
                      ? "Solicitud con fecha para mañana"
                      : ""
                    : "";
                  return (
                    <tr
                      key={index}
                      onClick={() => {
                        CambiarBarraLateral(solicitud);
                        handleRowClick(index);
                      }}
                      className={
                        selectedRow === index ? "fila-seleccionada" : ""
                      }
                    >
                      <td
                        className={`${alertaClase}`}
                        title={tituloAlerta}
                        onClick={(e) => {
                          e.preventDefault();
                          if (!isNotificationOpen) {
                            handleCopy(solicitud.str_CodSolicitudes);
                          }
                        }}
                      >
                        {solicitud.str_DeTerceros === "si" ? (
                          <Tooltip title="Modelo de Terceros" placement="top">
                            <span className="identificador-modelo">.</span>{" "}
                          </Tooltip>
                        ) : (
                          ""
                        )}
                        {solicitud.str_CodSolicitudes}
                      </td>
                      <td>{formatDate(solicitud.dt_FechaRegistro)}</td>
                      <td>
                        {solicitud.dt_FechaEsperada === null ||
                        solicitud.dt_FechaEsperada === ""
                          ? ""
                          : formatDate(solicitud.dt_FechaEsperada)}
                      </td>
                      <td className="colum-empresa-tabla-solicitante">
                        <Tooltip
                          title={solicitud.str_NombreEmpresa}
                          placement="top-start"
                        >
                          {solicitud.str_NombreEmpresa}
                        </Tooltip>
                      </td>
                      <td>
                        {solicitud.db_Honorarios === "0" ||
                        solicitud.db_Honorarios === null ||
                        solicitud.db_Honorarios === 0
                          ? ""
                          : solicitud.str_Moneda === "dolares"
                          ? `$ ${Number(solicitud.db_Honorarios).toLocaleString(
                              "es-PE"
                            )}`
                          : `${solicitud.str_SimboloMoneda} ${Number(
                              solicitud.db_Honorarios
                            ).toLocaleString()}`}
                      </td>
                      <td className="div-estado">
                        <span>{solicitud.estado_nombre}</span>
                        <CircleEstado estado={solicitud.estado_nombre} />
                      </td>
                    </tr>
                  );
                })
              )}
              <ToastContainer />
            </tbody>
            <tfoot>
              <tr>
                <td colSpan="6">
                <div className="contenedor-footer-tabla">
                  <button
                    onClick={() => recargarSolicitudes()}
                    className="boton-recargar"
                  >
                    Recargar
                  </button>
                  <div className="paginacion-tabla-solicitante">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="numero-pagina"
                    >
                      <IconoLeft size={"1.3rem"} color={"#000"} />
                    </button>
                    {renderNumeritosPaginacion()}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={indiceUltimaSolicitud >= solicitudes.length}
                      className="numero-pagina"
                    >
                      <IconoRight size={"1.5rem"} color={"#000"} />
                    </button>
                  </div>
                </div>
                </td>
               
              </tr>
            </tfoot>
          </table>
        </div>
        <BarraLateral
          solicitudes={solicitudes}
          pagesolicitudes={true}
          estado={selectedSolicitud.estado_nombre}
          historiales={HistorialesUsuario}
          solicitudSeleccionada={selectedSolicitud}
          Suscripcion={Suscripcion}
          idAplicacion={idAplicacion}
          idUsuario={idUsuario}
          SubmitObtenerDatos={SubmitObtenerDatos}
          setSelectedSolicitud={setSelectedSolicitud}
          Suscriptor={Suscriptor}
          perfil={perfil}
          CambiarBarraLateral={CambiarBarraLateral}
          tipoController={tipoController}
        />
      </div>
      {isModalVisible && (
        <div className="modal-filtros-gestor">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarFiltro}
            ></button>
          </div>
          <div className="titulo-modal-filtros">
            <span className="text-titulo-modal-filtros lato-font size-titulos">
              Buscar Por
            </span>
            <span
              className="borrarTodo-modal-filtros montserrat-font-500"
              onClick={BorrarTodosFiltros}
            >
              Borrar Todo
            </span>
          </div>
          <div className="inputs-modal-filtros">
            <input
              type="text"
              className="form-control"
              value={codSolicitudFiltro}
              onChange={(e) => setCodSolicitudFiltro(e.target.value)}
              placeholder="Cod.Solicitud"
            />
            <input
              type="text"
              className="form-control"
              value={documentoCliente}
              onChange={(e) => setDocumentoCliente(e.target.value)}
              placeholder="Doc.Cliente/Proveedor"
            />
            <input
              type="text"
              className="form-control"
              value={razonSocial}
              onChange={(e) => setRazonSocial(e.target.value)}
              placeholder="Razón social"
            />
            <TagInput setTags={setTags} tags={tags} />
            <Select
              options={tipoSolicitudes}
              value={tipoSolicitudes.find(
                (option) => option.value === selectedTipoSolicitudFiltro
              )}
              onChange={handleChangeTipoSolicitudFiltro}
              placeholder="Tipo de Solicitud"
            />
            <Select
              options={estadosFiltro}
              value={estadosFiltro.find(
                (option) => option.value === selectedEstadoFiltro
              )}
              onChange={handleChangeestadoFiltro}
              placeholder="Estado de solicitud"
            />
            <div className="anno-mes-modal-filtros">
              <select
                className="form-select"
                aria-label="Default select example"
                value={anioSeleccionado}
                onChange={handleAnioChange}
              >
                <option value="" selected disabled>
                  Año
                </option>
                {anios.map((anio) => (
                  <option key={anio} value={anio}>
                    {anio}
                  </option>
                ))}
              </select>
              <select
                className="form-select"
                aria-label="Default select example"
                value={mesSeleccionado}
                onChange={handleMesChange}
              >
                <option value="" selected disabled>
                  Mes
                </option>
                {meses.map((mes) => (
                  <option key={mes.value} value={mes.value}>
                    {mes.label}
                  </option>
                ))}
              </select>
            </div>
            <Select
              options={empresasFiltro}
              value={empresasFiltro.find(
                (option) => option.value === selectedEmpresaFiltro
              )}
              onChange={handleChangeEmpresaFiltro}
              placeholder="Empresa"
            />
          </div>
          <div className="div-btn-filtrar">
            <div className="form-check">
              <input
                className="form-check-input"
                type="checkbox"
                value=""
                checked={mostrarArchivadas}
                onChange={handleChangeMostrarArchivados}
              />
              <label className="form-check-label">Mostrar Archivadas</label>
            </div>
            <button className="btn btn-filtrar" onClick={handleFiltrar}>
              Aplicar Filtros
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tabla;
