import React, { useEffect, useState } from "react";
import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";

import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import ModalGestores from "../../../../Solicitante/Partials/Modals/ModalGestores";
import GestorAsignado from "../../GestorAsignado";
import GestorAsignadoSolicitante from "../../GestorAsignadoSolicitante";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import axios from "axios";
import { validateToken } from "../../../../../Components/Services/TokenService";
import Cookies from "js-cookie";
import Swal from "sweetalert2";
import IconoPlus from "../../../../../../assets/SVG/IconoPlus";
import IconoEditar from "../../../../../../assets/SVG/IconoEditar";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes: string;
    str_DeTerceros: string;
  };
  historiales: any[];
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: () => void;
}
interface Solicitud {
  nombre_completo: string;
  nombre_gestor: string;
}
const Asignado: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  Suscriptor,
  tipoController,
  perfil,
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAsignar: false,
  });

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };

  const navigate = useNavigate();
  const token = Cookies.get("Token");
  const [error, setError] = useState<string | null>(null);
  const [solicitud, setSolicitud] = useState<Solicitud | null>(null);
  useEffect(() => {
    const obtenerGestores = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerSolicitud"](
            solicitudSeleccionada.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setSolicitud(response.data);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
        setError("No se pudo obtener el gestor asignado.");
        setSolicitud(null);
      }
    };

    if (solicitudSeleccionada.int_idGestor) {
      obtenerGestores();
    }
  }, [solicitudSeleccionada]);
  const handleEditarSolicitud = () => {
    if (solicitud?.int_idGestor != idUsuario) {
      Swal.fire({
        icon: "error",
        title: "",
        text: "La solicitud ha sido reasignada",
      });
      SubmitObtenerDatos();
      return;
    }
    navigate(RoutesPrivate.EDITARSOLICITUD, {
      state: {
        solicitudSeleccionada,
        Suscripcion,
        idAplicacion,
        idUsuario,
        Suscriptor,
        Asignado: true,
      },
    });
  };
    const handleVerSolicitud = () => {
      navigate(RoutesPrivate.EDITARSOLICITUD, {
        state: {
          solicitudSeleccionada,
          Suscripcion,
          idAplicacion,
          idUsuario,
          Suscriptor,
          ver: true,
        },
      });
    };
  return (
    <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        {perfil === "Gestor Controller" && tipoController === "controller" ? (
          <Tooltip title="Reasignar Solicitud" placement="top">
            <div
              className="icono-barralateral-acciones"
              onClick={() => toggleModal("isModalVisibleAsignar")}
            >
              <IconoPlus size={"1.3rem"} color={"#000"} />
            </div>
          </Tooltip>
        ) : (
          <>
          <Tooltip title="Editar Solicitud" placement="top">
            <div
              className="icono-barralateral-acciones"
              onClick={handleEditarSolicitud}
            > 
            <IconoEditar size={"1.3rem"} color={"#000"} />
             </div>
          </Tooltip>
             <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
         </>
        )}
      </div>
      <GestorAsignadoSolicitante solicitud={solicitud} />

      <HistorialSolicitud historiales={historiales} />

      <ModalGestores
        isModalVisibleAsignar={modales.isModalVisibleAsignar}
        CerrarAsignar={() => toggleModal("isModalVisibleAsignar")}
        idAplicacion={idAplicacion}
        Suscripcion={Suscripcion}
        idSolicitud={solicitud?.int_idSolicitudes}
        idUsuario={idUsuario}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
        Controller={true}
        solicitudSeleccionada={solicitud}
      />
    </div>
  );
};

export default Asignado;
