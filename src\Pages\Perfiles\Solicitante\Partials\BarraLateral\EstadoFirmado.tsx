import React, { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { Tooltip } from "@mui/material";
import Cookies from "js-cookie";

import axios from "axios";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import { validateToken } from "../../../../Components/Services/TokenService";
import HistorialSolicitud from "./HistorialSolicitud";
import GestorAsignado from "./GestorAsignado";
import IconoArchivar from "../../../../../assets/SVG/IconoArchivar";
import getLogs from "../../../../Components/Services/LogsService";
import IconoVer from "../../../../../assets/SVG/IconoVer";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import IconoDownload from "../../../../../assets/SVG/IconoDownload";


interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes:string
  };
  historiales: any[]; 
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: ()=>void,
  Suscriptor: string;
}

const EstadoFirmado: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  aprobadores,
  Suscriptor
}) => {



  const token = Cookies.get("Token");
  const navigate = useNavigate();

  const handleArchivarConfirmation = () => {
    Swal.fire({
      title: "¿Estás seguro?",
      text: `Está seguro de archivar la solicitud?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, archivar",
      cancelButtonText: "Cancelar"

    }).then((result) => {
      if (result.isConfirmed) {
        handleArchivarSolicitud(); 
      }
    });
  };
  const handleArchivarSolicitud= async () => {
await validateToken();

    try {
      
      const response = await axios.put(
        API_GESTOR["ArchivarSolicitud"](),
        {
            int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
            int_idUsuarioModificacion:idUsuario
        },
        {
          headers: {
            "Content-Type": "multipart/form-data",
             "Authorization": `Bearer ${token}`
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        SubmitObtenerDatos()
        setSelectedSolicitud({})
        await getLogs(null,null,null,"Listado Solicitudes","Solicitudes","Archivar Solicitud","Contratos","PUT");

        Swal.fire("", "La solicitud se archivó correctamente", "success");
      } else {
        throw new Error("No se pudo ingresar el archivo");
      }
    } catch (error) {
      
      Swal.fire("", "No se pudo subir el archivo", "error");
    }
  };
  const handleDownload = async () => {
    await validateToken();
    try {

      const response = await axios.get(API_SOLICITANTE["ListarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"DOFI"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if(response.data){

      const response2 = await axios.get(API_SOLICITANTE["DescargarArchivo"]( Suscripcion,solicitudSeleccionada.str_CodSolicitudes,"DOFI"),{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      , responseType: "blob" });
      const contentDisposition = response2.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
        :  `${response.data.nombre_archivo}`;

      const url = window.URL.createObjectURL(new Blob([response2.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
     const handleVerSolicitud = () => {
       navigate(RoutesPrivate.EDITARSOLICITUD, {
         state: {
           solicitudSeleccionada,
           Suscripcion,
           idAplicacion,
           idUsuario,
           Suscriptor,
           ver: true,
         },
       });
     };
  return (
   <>

      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
  
        <Tooltip title="Archivar Solicitud" placement="top" >
           <div className="icono-barralateral-acciones" onClick={handleArchivarConfirmation}>

            <IconoArchivar size={"1.3rem"} color={"#000000"} />
           </div>
         </Tooltip>
          {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") ? (
          <Tooltip title="Descargar" placement="top">
        <div className="icono-barralateral-acciones" onClick={handleDownload}>
            <IconoDownload onClick={handleDownload} size={"1.3rem"} color={"#000"}/>
           </div>
        </Tooltip>
        ) : (
          <Tooltip title="Ver Solicitud" placement="top"> 
          <div className="icono-barralateral-acciones" onClick={handleVerSolicitud}>
          <IconoVer size={"1.3rem"} color={"#000"}/>

          </div>
         </Tooltip>
        )}
      </div>
      <GestorAsignado solicitudSeleccionada={solicitudSeleccionada} />

      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>
      
      </>
  );
};

export default EstadoFirmado;