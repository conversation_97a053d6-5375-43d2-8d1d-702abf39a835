import React, { useState } from "react";

interface Solicitud {
  int_idSolicitudes: number;
  str_CodSolicitudes: string;
}

interface ModalTagsProps {
  isModalVisibleTags: boolean;
  CerrarModalTags: () => void;
  setTags: (tags: string[]) => void; 
  accion: () => void;
}

const ModalTags: React.FC<ModalTagsProps> = ({
  isModalVisibleTags,
  CerrarModalTags,
  setTags,
  accion,
}) => {
    const [inputTags, setInputTags] = useState({
        tag1: "",
        tag2: "",
        tag3: "",
        tag4: ""
      });

      const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setInputTags((prevTags) => ({
          ...prevTags,
          [name]: value
        }));
      };
    
      const handleSubmitTags = () => {
        const tagsArray = Object.values(inputTags).filter(tag => tag !== "");
        setTags(tagsArray)
        accion(); 
      };
  return (
    <>
      {isModalVisibleTags && (
        <div className="modal-tags">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarModalTags}
            ></button>
          </div>
          <div className="pregunta-modal-solicitante lato-font">
            <span>¿Agregue claves para busqueda rápida </span>
          </div>
          <div className="div-inputs-tags">
            <div className="inputs-tags">
              {" "}
              <div className="mb-3">
                <input
                  type="text"
                  name="tag1"
                  className="form-control"
                  placeholder="Tag 1"
                  value={inputTags.tag1}
                  onChange={handleInputChange}
                />
              </div>
              <div className="mb-3">
                <input
                  type="text"
                  name="tag2"
                  className="form-control"
                  placeholder="Tag 2"
                  value={inputTags.tag2}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="inputs-tags">
              {" "}
              <div className="mb-3">
                <input
                  type="text"
                  name="tag3"
                  className="form-control"
                  placeholder="Tag 3"
                  value={inputTags.tag3}
                  onChange={handleInputChange}
                />
              </div>
              <div className="mb-3">
                <input
                  type="text"
                  name="tag4"
                  className="form-control"
                  placeholder="Tag 4"
                  value={inputTags.tag4}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
          <div className="botones-modal-solicitante">
            <button
              className="btn btn-outline-primary"
              onClick={CerrarModalTags}
            >
              Cancelar
            </button>
            <button className="btn btn-primary" onClick={handleSubmitTags}>
              Guardar
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalTags;
