import React, { useEffect, useState } from "react";
import Header from "../../Components/Partials/Header/Header";
import Cookies from "js-cookie";
import Titulo from "../../Components/Partials/Seccion/Titulo";
import "./Inicio.css";
import axios from "axios";
import API_GESTOR from "../../../assets/Api/ApisGestor";
import CircleEstado from "../../../assets/SVG/CircleEstado";
import { Tooltip } from "@mui/material";
import BarraLateral from "./Partials/BarraLateral/BarraLateral";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../Security/Routes/ProtectedRoute";
import Promedios from "./Partials/Promedios/Promedios";
import { decrypt, validateToken } from "../../Components/Services/TokenService";
import ConteoSolicitudes from "./Partials/BarraLateral/ConteoSolicitudes";
import Tabla from "./TablaSolicitudes/Tabla";
function formatDate(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}

const Inicio = () => {
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));

  const navigate = useNavigate()


  const verSolicitudes = () =>{
    navigate(RoutesPrivate.INICIOGESTOR)
  }
  return (
    <div>
      <Header />
      <Titulo seccion={`Bienvenido ${Nombres} ${Apellidos} `} />
      <div className="container-nueva-solicitud" style={{ padding: "0 1.4rem" }}>
        <div className="container-contador-gestor">
          <ConteoSolicitudes
           EstadisticasInicio = {true}
          />
        </div>
      </div>
      <div className="global-inicio-Gestor">
        <div className="contenedor-card-inicio-gestor">
          <Tabla/>
        </div>
      </div>
    </div>
  );
};

export default Inicio;
