/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react'
import IconoEliminarMaestros from '../../../../../assets/SVG/IconoEliminarMaestros'
import API_GESTOR from '../../../../../assets/Api/ApisGestor';
import axios from 'axios';
import Cookies from "js-cookie";
import Swal from 'sweetalert2';
import { decrypt, validateToken } from '../../../../Components/Services/TokenService';

interface UnidadNegocio {
    str_Descripcion: string;
    int_idUnidadesNegocio:number;
  }

  const MaestroUN: React.FC = () => {
    const [unidadesNegocio, setUnidadesNegocio] = useState<UnidadNegocio[]>([]);
    const Suscripcion = decrypt(Cookies.get("suscripcion"));
    const idUsuario = decrypt(Cookies.get("hora_llegada"));
    const [nuevaUnidad, setNuevaUnidad] = useState('');
    const token = Cookies.get("Token");

    const fetchUnidades = async () => {
await validateToken();
        try {
          const response = await axios.get(API_GESTOR['ObtenerUnidadesNegocios'](Suscripcion),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          });
          setUnidadesNegocio(response.data);
        } catch (error) {
          
        }
      };
      const EliminarUnidad = async (idUnidad: number) => {
        try {
        await axios.delete(API_GESTOR['EliminarUnidadNegocio'](idUnidad),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        })
          await fetchUnidades()
        } catch (error) {
          console.log('No se pudo obtener las solicitudes', error);
        }
      };
      const confirmarEliminacion = (idUnidad: number) => {
        Swal.fire({
          title: '¿Estás seguro?',
          text: '¡No podrás revertir esta acción!',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Sí, eliminar',
          cancelButtonText: 'Cancelar',
        }).then((result) => {
          if (result.isConfirmed) {
            EliminarUnidad(idUnidad); 
            Swal.fire('', 'La unidad ha sido eliminada.', 'success');
          }
        });
      };
      const agregarUnidad = async () => {
await validateToken();
        if (!nuevaUnidad.trim()) {
          Swal.fire('', 'Por favor ingresa un nombre para la nueva unidad', 'error');
          return;
        }
        try {
            await axios.post(API_GESTOR['AgregarUnidadNegocio'](), {
              str_Descripcion: nuevaUnidad,
              str_idSuscripcion:Suscripcion,
              int_idUsuarioCreador :idUsuario

            },{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            });
            await fetchUnidades();
            setNuevaUnidad(''); 
            Swal.fire('', 'La unidad ha sido agregada.', 'success');
          } catch (error) {
            Swal.fire('', 'No se pudo agregar la unidad', 'error');
            console.log('Error al agregar la unidad', error);
          }
      };
      useEffect(() => {
        fetchUnidades();
      }, []);
      
  return (
    <div className='global-maestros'>
        <div className="titulo-maestros lato-font">
            Maestro de Unidades de Negocio
        </div>
        <div className="container-gestor-administracion">
            <table className='tabla-gestor-administracion'>
                <tbody>
                {unidadesNegocio.length === 0 ? (
                <tr>
                  <td colSpan={6} style={{ textAlign: "center" }}>
                    No se encontraron registros
                  </td>
                </tr>
              ) : (
                unidadesNegocio.map((unidadesNegocio, index) => (
                    <tr key={index}>
                    <td><div className="texto-tabla-gestor-administracion">{unidadesNegocio.str_Descripcion}</div> <div className="icono-tabla-gestor-administracion" style={{cursor : 'pointer'}} onClick={() => confirmarEliminacion(unidadesNegocio.int_idUnidadesNegocio )}><IconoEliminarMaestros/></div></td>
                    </tr>
                ))
            )}
                </tbody>
            </table>
            <div className="div-registros-gestor-administracion">
                <input type="text" className='form-control' placeholder='Ingresar Unidad'
                 value={nuevaUnidad}
                 onChange={(e) => setNuevaUnidad(e.target.value)}
                 />
                <button className='btn-nuevo-maestros' onClick={agregarUnidad}><i className="fa-solid fa-plus"></i> Agregar</button>
            </div>
        </div>
    </div>
  )
}

export default MaestroUN