import React, { useEffect, useState } from "react";
import { <PERSON>, Step, StepLabel, Stepper } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import axios from "axios";
import Select from "react-select";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import BarraLateralCrearSolicitud from "../../BarraLateral/BarraLateralCrearSolicitud";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import getLogs from "../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
const CrearAcuerdoConfidencialidad = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  NomTipoSolicitud
}) => {
  const [files, setFiles] = useState([]);
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [unidadesNegocios, setUnidadesNegocios] = useState([]);
  const [selectedUnidadesNegocios, setSelectedUnidadesNegocios] = useState("");
  const [selectedEmpresa, setSelectedEmpresa] = useState("");
  const [interlocutorEncontrado, setInterlocutorEncontrado] = useState(false);
  const [idInterlocutor, setIdInterlocutor] = useState(null);
  const [errors, setErrors] = useState({});
  const token = Cookies.get("Token");
  const [activeStep, setActiveStep] = useState(0);
  const [FechaMinima , setFechaMinima] = useState(getLocalDate());
  const [TiposMoneda, setTiposMoneda] = useState([]);
  

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };
  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioCreacion: UsuarioId,
    str_idSuscriptor: Suscripcion,
    int_idEmpresa: selectedEmpresa,
    int_idSolicitante: UsuarioId,
    int_idUnidadNegocio: selectedUnidadesNegocios,
    int_idTipoSol: idTipoSolicitud,
    int_SolicitudGuardada: null,
    str_DeTerceros: tipoModelo === "Propio" ? "no" : "si",
    dt_FechaEsperada: new Date().toISOString().split("T")[0],
    db_Honorarios: 0,
  });
  const [formDataContenido, setFormDataContenido] = useState({
    str_DocAdjuntos: files.length >= 1 ? "si" : "no",
    str_ObjetivoContrato: "",
    str_PlazoSolicitud: "",
    str_TipoServicio: "",
    str_InfoAdicional: "",
    int_idInterlocutor: null,
    str_idSuscriptor: Suscripcion,
    int_idSolicitudes: null,
    str_ObligacionesConjuntas: "",
  });
  const [formDataInterlocutor, setFormDataInterlocutor] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_Correo: "",
    str_RepLegal: "",
    int_RLPartida: "",
    str_Domicilio: "",
  });
  const validateForm = () => {
    let newErrors = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataInterlocutor.str_Documento)
      newErrors.str_Documento = "El Documento es requerido";
    if (!formDataInterlocutor.str_Interlocutor)
      newErrors.str_Interlocutor = "El proveedor es requerido";
   const today = new Date();
    today.setHours(0, 0, 0, 0); // Eliminamos la hora para comparar solo la fecha

    if (!formDataSolicitud.dt_FechaEsperada) {
        newErrors.dt_FechaEsperada = "La fecha estimada de entrega es requerida";
    } else if (new Date(formDataSolicitud.dt_FechaEsperada) < today) {
        newErrors.dt_FechaEsperada = "La fecha estimada no puede ser menor a hoy";
    }
if (!formDataInterlocutor.str_Correo) {
      newErrors.str_Correo = "El Correo es requerido";
    } else {
      const correoValido = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(
        formDataInterlocutor.str_Correo.trim()
      );
      if (!correoValido) {
        newErrors.str_Correo = "El Correo no es válido";
      }
    }
    if (!formDataInterlocutor.str_RepLegal)
      newErrors.str_RepLegal = "El Representante legal es requerido";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.str_PlazoSolicitud)
      newErrors.str_PlazoSolicitud = "El Plazo es requerido";
    if (!formDataContenido.int_RLPartida)
      newErrors.int_RLPartida = "La partida Registral es requerido";
    if (!formDataContenido.str_ObligacionesConjuntas)
      newErrors.str_ObligacionesConjuntas = "Las obligaciones son requeridas ";

    setErrors(newErrors);

    // Retornar true si no hay errores
    return Object.keys(newErrors).length === 0;
  };
  
  const steps = [
    "DATOS GENERALES",
    "DATOS DE CONTRATO",
    "INFORMACIÓN ADICIONAL",
  ];
  useEffect(() => {
    const fetchEmpresas = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEmpresas"](idAplicacion, Suscriptor),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
        if (opciones.length > 0) {
          setSelectedEmpresa(opciones[0].value);
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    
    fetchEmpresas();
  }, []);
  useEffect(() => {
    const fetchUnidadesNegocios = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerUnidadesNegocios"](Suscripcion,selectedEmpresa),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idUnidadesNegocio: any; str_Descripcion: any }) => ({
            value: tipo.int_idUnidadesNegocio,
            label: tipo.str_Descripcion,
          })
        );
        setUnidadesNegocios(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](selectedEmpresa, Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
          
        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
            try {
              const response = await axios.get(
                API_GESTOR["ObtenerTiempoRespuestaTS"](Suscripcion,idTipoSolicitud,selectedEmpresa),{
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                  }
                }
              );
              const responseData = response.data;
              const ultimoObjeto = responseData.length > 0 ? responseData[responseData.length - 1] : null;
      
              const nuevaFechaEsperada = addDaysToDate(new Date(), ultimoObjeto.int_TiempoRespuesta ? ultimoObjeto.int_TiempoRespuesta : 0);
              setFormDataSolicitud(prevState => ({
                ...prevState,
                dt_FechaEsperada: nuevaFechaEsperada,
              }));
              setFechaMinima(nuevaFechaEsperada);

            } catch (error) {
              console.error("Error al obtener tipos de solicitud:", error);
            }
          };
    ObtenerTiempoRespuestaTipoSolicitud();
    fetchUnidadesNegocios();
    fetchMoneda();

  } , [selectedEmpresa]);
  useEffect(() => {
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      int_idEmpresa: selectedEmpresa,
      int_idUnidadNegocio: selectedUnidadesNegocios,
    }));
  }, [selectedEmpresa, selectedUnidadesNegocios, formDataContenido]);
  const handleChangeEmpresa = (selectedOption: React.SetStateAction<null>) => {
    setSelectedEmpresa(selectedOption.value);
  };
  const handleChangeUnidadesNegocios = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedUnidadesNegocios(selectedOption.value);
  };

  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };

  const handleFileRemove = (index) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value; 
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      dt_FechaEsperada: `${selectedDate}T00:00:00`,
    }));
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataInterlocutor((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion)
      );
      
      if (response.data) {
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: response.data.str_Interlocutor,
          str_Correo: response.data.str_Correo,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal", 
        }));
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);
      } else {
        setInterlocutorEncontrado(false);
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_Correo: "",
          str_RepLegal: "",
          int_RLPartida: "",
        }));
        setIdInterlocutor(null);
      }
    } catch (error) {
      
      setInterlocutorEncontrado(false);
      setFormDataInterlocutor((prevData) => ({
        ...prevData,
        str_Interlocutor: "",
        str_Correo: "",
        str_RepLegal: "",
        int_RLPartida: "",
      }));
      setIdInterlocutor(null);
    }
  };
  const handleSubmitInterlocutores = async (idSolicitud,guardado) => {
    try {
      if (interlocutorEncontrado) {
        const formDataModificado = {
          ...formDataInterlocutor,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificado.int_idUsuarioCreacion;
        const response = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutor),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
               "Authorization": `Bearer ${token}`
            },
          }
        );

        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }
        handleSubmitContenidoSolicitud(idSolicitud,idInterlocutor,guardado);
        
      } else {
        const response = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
               "Authorization": `Bearer ${token}`
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }
        handleSubmitContenidoSolicitud(idSolicitud,response.data.int_idInterlocutor,guardado);
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);
        
      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };
  const EstadoNuevo = async (idSolicitud, estado) => {
    await validateToken();
    try {
      await axios.put(API_SOLICITANTE["ActualizarEstado"](), {
        str_idSuscriptor: Suscripcion,
        nombre_estado: estado,
        int_idUsuarioCreacion: UsuarioId,
        int_idSolicitudes: idSolicitud,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
    }
  };
  const obtenerGestorConMenorSolicitudes = async () => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["ObtenerGestoresTipoSolicitud"](
          Suscripcion,
          idTipoSolicitud,
          selectedEmpresa
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = response.data;

      if (!gestores || gestores.length === 0) {
        
        return null;
      }

      // Obtener el número de solicitudes activas para cada gestor
      const solicitudesPorGestor = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            const contadorResponse = await axios.get(
              API_SOLICITANTE["ContadorPorGestor"](gestor.int_idGestor,Suscripcion), {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            const solicitudes_Activas = contadorResponse.data.total_no_firmado;

            

            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas,
            };
          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idGestor}:`,
              error
            );
            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas: Infinity, // Asignar un valor alto para evitar seleccionarlo
            };
          }
        })
      );

      // Encontrar el gestor con menor número de solicitudes activas
      const gestorConMenorSolicitudes = solicitudesPorGestor.reduce(
        (minGestor, currentGestor) => {
          return currentGestor.solicitudes_Activas <
            minGestor.solicitudes_Activas
            ? currentGestor
            : minGestor;
        },
        { int_idGestor: null, solicitudes_Activas: Infinity }
      );

      

      return gestorConMenorSolicitudes.int_idGestor;
    } catch (error) {
      
      return null;
    }
  };
  const handleAsignarGestor = async (idSolicitud, gestorSeleccionado) => {
    await validateToken();
    try {
      const response = await axios.put(
        API_SOLICITANTE["AsignarGestor"](),
        {
          int_idSolicitudes: idSolicitud,
          int_idGestor: gestorSeleccionado,
          int_idUsuarioModificacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        console.error(`Gestor Asignado Correctamente ${response}`);
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitGuardarSolicitud = async () => {
    await validateToken();
    if (!selectedEmpresa || !selectedUnidadesNegocios) {
      Swal.fire(
        "Error",
        "Debe seleccionar una empresa y una unidad de negocio para registrar",
        "error"
      );
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 1,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        await handleSubmitInterlocutores(response.data.int_idSolicitudes,1);
        if (files.length >= 1) {
          handleSubmitFiles(
            response.data.int_idSolicitudes,
            response.data.str_CodSolicitudes
          );
        }
        await getLogs(response.data.int_idSolicitudes,null,response.data.int_idSolicitudes,"Solicitudes","Solicitudes","Crear Solicitud","Contratos","POST");

      }

    } catch (error) {
      return error
    }
  };
  const handleSubmitConfirmarSolicitud = async () => {
    await validateToken();
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop(); 
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 0,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
             "Authorization": `Bearer ${token}`
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        await handleSubmitInterlocutores(response.data.int_idSolicitudes,2);
       

        if (files.length >= 1) {
          handleSubmitFiles(
            response.data.int_idSolicitudes,
            response.data.str_CodSolicitudes
          );
        }
        await getLogs(response.data.int_idSolicitudes,null,response.data.int_idSolicitudes,"Solicitudes","Solicitudes","Crear Solicitud","Contratos","POST");

      }
      
    } catch (error) {
      return error
    }
  };
  const handleSubmitContenidoSolicitud = async (
    idSolicitud,
    idInterlocutor,
    guardado
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idSolicitudes: idSolicitud,
        int_idInterlocutor: idInterlocutor,
      };
      const response = await axios.post(
        API_SOLICITANTE["InsertarContenidoSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
             "Authorization": `Bearer ${token}`
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        if (guardado === 1) {
          await EstadoNuevo(idSolicitud,"Nuevo")
          Swal.fire("", "Solicitud Guardada ", "success");
          navigate(RoutesPrivate.INICIOSOLICITANTE);
          
        }else{
          await EstadoNuevo(idSolicitud,"Nuevo")
          const idGestorAsignado = await obtenerGestorConMenorSolicitudes();
          if (!idGestorAsignado) {
            Swal.fire(
              "Error",
              "No hay gestores disponibles para asignar la solicitud",
              "error"
            );
            return;
          }
          await handleAsignarGestor(idSolicitud, idGestorAsignado);
          await EstadoNuevo(idSolicitud,"Asignado")
          Swal.fire("", "Solicitud Confirmada ", "success");
          navigate(RoutesPrivate.INICIOSOLICITANTE);
        }
      }
    } catch (error) {
      return error
    }
  };
  const handleSubmitFiles = async (idSolicitud, codSolicitud) => {
    await validateToken();
    try {
      for (const file of files) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append("str_CodSolicitudes", codSolicitud);
        formDataSolicitud.append("int_idSolicitudes", idSolicitud);
              // Enviar el tipo_adjunto como campo separado
        if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_SOLICITANTE["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
               "Authorization": `Bearer ${token}`
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }

        
      }

      
    } catch (error) {
      
    }
  };
  const handleSubmitSolicitudGuardar = () => {
     
    handleSubmitGuardarSolicitud();
  };
  const handleSubmitSolicitudConfirmar = () => {
     
    handleSubmitConfirmarSolicitud();
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font${"collapsed" }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                    <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Solicitante </h5>
                      </div>
                     
                    </div>
                    <div className="div-input-crear-solicitud">
                    <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Cliente</h5>
                      </div>
                      
                    </div>
                  </div>
                <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder={`${Nombres} ${Apellidos}`}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange}>
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <Select
                        options={empresasFiltro}
                        value={empresasFiltro.find(
                          (option) => option.value === selectedEmpresa
                        )}
                        onChange={handleChangeEmpresa}
                        placeholder="Empresa"
                      />
                      {errors.int_idEmpresa && (
                        <span className="error-message">
                          {errors.int_idEmpresa}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="str_Documento"
                        className={`form-control ${errors.str_Documento && "is-invalid"}`}
                        value={formDataInterlocutor.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value);
                            }
                          }
                        }}
                      />
                      
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <Select
                        options={unidadesNegocios}
                        value={unidadesNegocios.find(
                          (option) => option.value === selectedUnidadesNegocios
                        )}
                        onChange={handleChangeUnidadesNegocios}
                        placeholder="Unidad de Negocio"
                      />
                      {errors.int_idUnidadNegocio && (
                        <span className="error-message">
                          {errors.int_idUnidadNegocio}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_Interlocutor && "is-invalid"}`}
                        name="str_Interlocutor"
                        value={formDataInterlocutor.str_Interlocutor}
                        onChange={handleInputChange}
                      />
                 
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Partida Registral:</label>
                      <input
                        type="text"
                        name="int_RLPartida"
                        className={`form-control ${errors.int_RLPartida && "is-invalid"}`}
                        value={formDataInterlocutor.int_RLPartida}
                        placeholder=""
                        onChange={handleInputChange}
                      />
                      
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante:</label>
                      <input
                        type="text"
                        name="str_RepLegal"
                        className={`form-control ${errors.str_RepLegal && "is-invalid"}`}
                        value={formDataInterlocutor.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                      />
                     
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Correo Electrónico:</label>
                      <input
                        type="email"
                        name="str_Correo"
                        className={`form-control ${errors.str_Correo && "is-invalid"}`}
                        placeholder=""
                        value={formDataInterlocutor.str_Correo}
                        onChange={handleInputChange}
                      />
                      
                    </div>
                  </div>
                    <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Dirección:</label>
                      <input
                        type="text"
                        name="str_Domicilio"
                        className={`form-control ${errors.str_Domicilio && "is-invalid"}`}
                        value={formDataInterlocutor.str_Domicilio}
                        placeholder=""
                        onChange={handleInputChange}
                      />
                     
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font${"collapsed" }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Detalle del Acuerdo
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Objeto del contrato
                      </label>
                      <textarea
                        className={`form-control ${errors.str_ObjetivoContrato && "is-invalid"}`}
                        id=""
                        name="str_ObjetivoContrato"
                        rows="3"
                        onChange={handleTextareaChange}
                      ></textarea>
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Obligaciones</label>
                      <textarea
                        className={`form-control ${errors.str_ObligacionesConjuntas && "is-invalid"}`}
                        id=""
                        name="str_ObligacionesConjuntas"
                        rows="3"
                        onChange={handleTextareaChange}
                      ></textarea>
                      
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Plazo del Acuerdo:</label>
                      <input
                        type="text"
                        className={`form-control ${errors.str_PlazoSolicitud && "is-invalid"}`}
                        placeholder=""
                        name="str_PlazoSolicitud"
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font${"collapsed" }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(2)}
                >
                  Información adicional
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="text-area-crear-solicitud">
                    <label className="form-label">Condiciones del Servicio</label>
                    <textarea
                      class="form-control"
                      name="str_InfoAdicional"
                      id="exampleFormControlTextarea1"
                      rows="3"
                      onChange={handleTextareaChange}
                    ></textarea>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha Esperada de entrega:{" "}
                      </label>
                      <input
                        type="date"
                          onKeyDown={(e) => e.preventDefault()}
                        min={FechaMinima}
                        className="form-control"
                        value={(formDataSolicitud.dt_FechaEsperada ? formDataSolicitud.dt_FechaEsperada.split("T")[0] : "")}
                     
                        onChange={handleDateChange}
                        name="dt_FechaEsperada"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        NomTipoSolicitud={NomTipoSolicitud}
      />
    </div>
  );
};

export default CrearAcuerdoConfidencialidad;
