import axios from "axios";
import React, { useEffect, useState } from "react";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import Cookies from "js-cookie";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import CircleEstado from "../../../../../../assets/SVG/CircleEstado";
import * as XLSX from "xlsx";
import GifCarga from '../../../../../../assets/Img/gifCarga.gif'
import IconoLeft from "../../../../../../assets/SVG/IconoLeft";
import IconoRight from "../../../../../../assets/SVG/IconoRight";


const Gestores = ({ Suscripcion, anio }) => {
  const [gestores, setGestores] = useState([]);
  
  const token = Cookies.get("Token");
  const [currentPage, setCurrentPage] = useState(1);
  const gestoresPorPagina = 8;
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedGestor, setSelectedGestor] = useState(null);
  const [isLoader, setIsLoader] = useState(false);
  const indiceUltimaSolicitud = currentPage * gestoresPorPagina;
  const indicePrimeraSolicitud = indiceUltimaSolicitud - gestoresPorPagina;
  const gestoresActuales = gestores.slice(
    indicePrimeraSolicitud,
    indiceUltimaSolicitud
  );
  const handlePageChange = (pageNumber: React.SetStateAction<number>) => {
    setCurrentPage(pageNumber);
  };
  const totalPaginas = Math.ceil(gestores.length / gestoresPorPagina);

  const renderNumeritosPaginacion = () => {
    const numeritos = [];
    for (let i = 1; i <= totalPaginas; i++) {
      numeritos.push(
        <button
          key={i}
          className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }
    return numeritos;
  };
  const ObtenerGestores = async () => {
    try {
      // Obtener la lista de gestores
      const responseGestores = await axios.get(
        API_GESTOR["ObtenerGestores"](Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = responseGestores.data;
      // Añadir datos de solicitudes a cada gestor
      const gestoresConSolicitudes = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            // Llamada a la API ContadorPorGestor para cada gestor
            const contadorResponse = await axios.get(
              API_SOLICITANTE["ContadorPorGestorAnio"](
                gestor.int_idUsuarios,
                Suscripcion,
                anio
              ),
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            const contadorProcesos = await axios.get(
              API_SOLICITANTE["ContadorPorGestorAnioProcesos"](
                gestor.int_idUsuarios,
                Suscripcion,
                anio
              ),
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            const { Cerrados, Activos, Nuevos } = contadorProcesos.data;
            const TiempoTotalTemporal =
              contadorProcesos.data["Tiempo Total Temporal"];
            const {
              solicitudes_por_estado,
              total_no_firmado,
              total_horas_trabajadas_con_firmado,
              total_atrasadas
            } = contadorResponse.data;
            const totalHorasTrabajadas =
              parseInt(total_horas_trabajadas_con_firmado) +
              parseInt(TiempoTotalTemporal);
            const ProcesosActivos = parseInt(Activos) + parseInt(Nuevos);
            return {
              ...gestor,
              solicitudesPorEstado: solicitudes_por_estado,
              solicitudesActivas: total_no_firmado,
              ProcesosCerrados: Cerrados,
              ProcesosActivos: ProcesosActivos,
              ProcesosEnProceso: Activos,
              ProcesosNuevos: Nuevos,
              TiempoTotalTrabajado: totalHorasTrabajadas,
              SolicitudesAtrasadas: total_atrasadas
            };

          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idUsuarios}:`,
              error
            );
            setGestores([])
            return {
              ...gestor,
              solicitudesPorEstado: {},
              solicitudesActivas: 0,
              totalHorasTrabajadas: 0,
              total_horas_trabajadas_con_firmado: 0,
              total_horas_trabajadas_sin_firmado: 0,
              total_atrasadas:0
            };
          }
        })
      );

      setGestores(gestoresConSolicitudes);
  } catch (error) {
      console.error("Error al obtener datos de gestores y solicitudes:", error);
    }
  };

  const DatosGestor = async (selectedGestor) => {
    setIsLoader(true);
    try {
      // Obtener la lista de gestores
      const response = await axios.get(
        API_GESTOR["DatosGestor"](Suscripcion, selectedGestor.int_idUsuarios),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestor = response.data;
      const gestorConPerfil = {
        ...selectedGestor,
        ...gestor,
        perfil: gestor.perfil,
        foto: gestor.str_RutaFoto || "",
      };
      setSelectedGestor(gestorConPerfil);

      setIsLoader(false);
    } catch (error) {
      setIsLoader(false);
    }
  };
  const descargarExcel = () => {
    // Preparar datos para exportar
    const datosParaExportar = gestores.map((gestor) => ({
      Gestor: `${gestor.str_Nombres} ${gestor.str_Apellidos}`,
      Correo: gestor.str_Correo,
      "Solicitudes Activas": gestor.solicitudesActivas,
      "Procesos Activos": gestor.ProcesosActivos,
      "Horas Reportadas": gestor.TiempoTotalTrabajado,
      "Solicitudes Asignadas": gestor.solicitudesPorEstado["Asignado"],
      "Solicitudes En Proceso": gestor.solicitudesPorEstado["En Proceso"],
      "Solicitudes En Validación": gestor.solicitudesPorEstado["En Validación"],
      "Solicitudes Aceptadas": gestor.solicitudesPorEstado["Aceptado"],
      "Solicitudes En Aprobación": gestor.solicitudesPorEstado["En Aprobación"],
      "Solicitudes Aprobadas": gestor.solicitudesPorEstado["Aprobado"],
      "Solicitudes Firmadas": gestor.solicitudesPorEstado["Firmado"],
      "Solicitudes Atrasadas": gestor.SolicitudesAtrasadas,
      "Procesos Nuevos": gestor.ProcesosNuevos,
      "Procesos En Proceso": gestor.ProcesosEnProceso,
      "Procesos Cerrados": gestor.ProcesosCerrados,
    }));

    // Crear hoja de cálculo
    const ws = XLSX.utils.json_to_sheet(datosParaExportar);

    // Crear libro de trabajo
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Gestores");

    // Descargar archivo Excel
    XLSX.writeFile(wb, `Gestores_${anio}.xlsx`);
  };
  useEffect(() => {
    ObtenerGestores();
  }, [anio]);
  const handleRowClick = (index) => {
    setSelectedRow(index);
  };
  const CambiarBarraLateral = (gestor) => {
    DatosGestor(gestor);
  };
  return (
    <div
      className="contenedor-estadisticas-gestores"
      style={{ marginTop: "1rem" }}
    >
         <div className="montserrat-font" style={{ display: "flex", justifyContent: "space-between",width:"64.3%" }}>
         <div className="lato-font size-titulos">Lista de Gestores</div>
       
       <button className="boton-descargar-csv" style={{border: "1px solid #ccc"}} onClick={descargarExcel}>
         Descargar archivo excel <i className="fa-solid fa-download" ></i>
       </button>
         </div>
     
      <div className="contenedor-tabla-estadisticas-gestores">
        <table
          className="tabla-inicio-solicitante"
          style={{ maxWidth: "65%", width: "65%" }}
        >
          <thead>
            <tr>
              <th>Gestor</th>
              <th>Correo</th>
              <th>Solicitudes Activas</th>
              <th>Procesos Activos</th>
              <th>Horas Reportadas</th>
            </tr>
          </thead>
          <tbody>
            {gestoresActuales.map((gestor, index) => (
              <tr
                key={gestor.int_idUsuarios}
                onClick={() => {
                  handleRowClick(index);
                  CambiarBarraLateral(gestor);
                }}
                className={selectedRow === index ? "fila-seleccionada" : ""}
              >
                <td>
                  {gestor.str_Nombres} {gestor.str_Apellidos}
                </td>
                <td>{gestor.str_Correo}</td>
                <td>{gestor.solicitudesActivas}</td>
                <td>{gestor.ProcesosActivos}</td>
                <td>{gestor.TiempoTotalTrabajado}</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan={6}>
                <div className="paginacion-tabla-solicitante">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="numero-pagina"
                  >
                                          <IconoLeft  size={"1.3rem"} color={"#000"}/>

                  </button>
                  {renderNumeritosPaginacion()}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={indiceUltimaSolicitud >= gestores.length}
                    className="numero-pagina"
                  >
                    <IconoRight size={"1.5rem"} color={"#000"}/>
                  </button>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
        <div className="barra-lateral-gestores montserrat-font">
            
          {selectedGestor && (
            <>
              <div className="datos-gestor-gestores">
              {isLoader && <div className="loader-gestores"><img src={GifCarga} alt="Cargando..."></img></div>}
                {selectedGestor.foto ? (
                  <img
                    src={`data:image/jpeg;base64,${selectedGestor.foto}`}
                    alt={`${selectedGestor.str_Nombres} ${selectedGestor.str_Apellidos}`}
                  />
                ) : (
                  <img
                    src={`https://static.vecteezy.com/system/resources/previews/009/292/244/non_2x/default-avatar-icon-of-social-media-user-vector.jpg`}
                    alt={`${selectedGestor.str_Nombres} ${selectedGestor.str_Apellidos}`}
                  />
                )}
                <div className="datos-text-gestor-gestores">
                  <div className="nombre-gestor-gestores">
                    {selectedGestor.str_Nombres} {selectedGestor.str_Apellidos}
                  </div>
                  <div className="rol-gestor-gestores">
                    {selectedGestor.perfil}
                  </div>
                </div>
                <div className="contacto-text-gestor-gestores">
                  <div className="contacto-gestor-gestores">
                    <i className="fa-regular fa-envelope"></i>
                    {selectedGestor.str_Correo}
                  </div>
                </div>
              </div>
              <div
                className="contadores-gestor-gestores"
                style={{ borderLeft: "1px solid #ccc" }}
              >
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Asignado" />
                  Solicitudes Asignadas:{" "}
                  {selectedGestor.solicitudesPorEstado.Asignado}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="En Proceso" />
                  Solicitudes En Proceso:{" "}
                  {selectedGestor.solicitudesPorEstado["En Proceso"]}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="En Validación" />
                  Solicitudes En Validación:{" "}
                  {selectedGestor.solicitudesPorEstado["En Validación"]}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Aceptado" />
                  Solicitudes Aceptadas:{" "}
                  {selectedGestor.solicitudesPorEstado.Aceptado}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="En Aprobación" />
                  Solicitudes En Aprobación:{" "}
                  {selectedGestor.solicitudesPorEstado["En Aprobación"]}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Aprobado" />
                  Solicitudes Aprobadas:{" "}
                  {selectedGestor.solicitudesPorEstado.Aprobado}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Firmado" />
                  Solicitudes Firmadas:{" "}
                  {selectedGestor.solicitudesPorEstado.Firmado}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Atrasado" />
                  Solicitudes Atrasadas:{" "}
                  {selectedGestor.SolicitudesAtrasadas}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Nuevo" />
                  Procesos Nuevos: {selectedGestor.ProcesosNuevos}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="En Proceso" />
                  Procesos Abiertos: {selectedGestor.ProcesosEnProceso}
                </div>
                <div className="conteo-gestor-gestores">
                  <CircleEstado estado="Firmado" />
                  Procesos Cerrados: {selectedGestor.ProcesosCerrados}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Gestores;
