.floating-button {
  position: fixed;
  bottom: 1.3rem;
  right: 1.3rem;
  background-color: #2C4CB3 ;
  border: none;
  border-radius: 50%;
  width: 4.5rem;
  height: 4.5rem;
  box-shadow: 0 0.25rem 0.35rem rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  
}
.floating-button img{
  width: 2.5rem;
  height: 2.5rem;
}.chat-window {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  max-width: 95vw;
  max-height: 95vh;
  min-width: 30rem;
  min-height: 45rem;
  width: 30rem;
  height: 45rem;
  background-color: #ffffff;
   border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  opacity: 0; 
  visibility: hidden; 
  transition: opacity 0.5s ease, visibility 0s 0.3s; 
  border: 0.125rem solid #2C4CB3 ;

  /* Habilitar redimensión */
  resize: both;
  overflow: auto;
}

.chat-window.open {
  opacity: 1;
  visibility: visible; 
  transition: opacity 0.5s ease; 
}

.chat-header {
  padding: 0.75rem; 
  background-color: #2C4CB3 ;
  color: white;
  font-size: 1.2rem !important;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.5rem 0.5rem 0 0;    
}
.chat-body {
  flex-grow: 1;
  padding: 0.75rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap:1rem;
  
}
.message {
  padding: 0.5rem;
  border-radius: 0.45rem;
  max-width: 80%;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  font-family: "Montserrat", sans-serif;

}

.message.user {
  align-self: flex-end;
  background-color: #2759fd ;
  color: white;
  font-family: "Montserrat", sans-serif;
  font-size: 0.9rem;
}

.message.bot {
  align-self: flex-start;
  background-color: #f1f1f1;
  color: #333;
  font-family: "Montserrat", sans-serif;
  font-size: 0.9rem;

}

.chat-input {
  display: flex;
  padding: 0.75rem;
  border-top: 1px solid #ddd;
}

.chat-input input {
  flex-grow: 1;
  padding: 0.5rem;
  border: 0.0625rem solid #ccc;
  border-radius: 0.4rem;
}

.chat-input button {
  background-color: #2C4CB3 ;
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.35rem;
  margin-left: 0.5rem;
  cursor: pointer;
}
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
}

.spinner {
  border: 0.25rem solid #f3f3f3; 
  border-top:  0.25rem  solid #2C4CB3 ;
  border-radius: 50%; 
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite; 
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.container-chat-vacio{
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 100%;
}
.div-imagen-chatbot-saludo{
  display: flex;
  max-width: 90%;
  font-family: "Montserrat", sans-serif;

}
.div-imagen-chatbot-saludo img{
  width: 100%;
}
.texto-chatbot-saludo{
  font-family: "Montserrat", sans-serif;
  font-size: 1rem;
  color: #4B4B4B;
  text-align: center;
  margin: 0.5rem 0;
}
.div-close-button{
  display: flex;
  gap: 1rem;
  align-items: start;

}
.boton-lista-bot{
  display: flex;
  justify-content: center;
  align-items: start;
  gap: 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-family: "Montserrat", sans-serif;
  color: #4B4B4B;
  background-color: transparent;
  border: none;
  outline: none;
}

.boton-lista-bot svg:hover{
  fill: #fff;
}

@keyframes fadeOut {
0% {
  opacity: 1;
}
100% {
  opacity: 0;
}
}

.message.fade-out {
animation: fadeOut 1s forwards;
}
.header-imagenbot{
display: flex;
justify-content: start;
align-items: center;
gap: 1rem;
}
.imagen-header{
max-width:  4rem;
max-height:6rem;
display: flex;
align-items: center;
justify-content: start;
margin-right: 0;
border-radius: 30%;
background-color: #fff;

}
.imagen-header img{
width: 100%;
height: 100%;
height: auto;
object-fit: cover;

}
.texto-header{
font-family: "Montserrat", sans-serif;
font-size: 1rem;
color: #fff;
text-align: left;
margin: 0.5rem 0;
font-weight: 500;
display: flex;
flex-direction: column;
}
.help-buttons{
display: flex;
justify-content: center;
align-items: center;
gap: 1rem;
padding: 0.2rem;
flex-direction: column;
}
.help-buttons button{
background-color: transparent ;
color: #2C4CB3;
border: none;
padding: 0.5rem 0.75rem;
border-radius: 0.35rem;
 cursor: pointer;
font-family: "Montserrat", sans-serif;
font-size: 0.9rem;
width: 100%;
display: flex;
justify-content: center;
align-items: center;
gap: 0.5rem;
border: 0.1rem solid  #2C4CB3;
}
.help-buttons button:hover{
background-color: #2C4CB3;
color: #fff;
}
.contactame-con-asesor{
padding: 0.5rem;
display: flex;
flex-direction: column;
align-items: start;
gap: 0.5rem;
font-family: "Montserrat", sans-serif;
font-size: 0.9rem;
}
.titulo-contactame{
font-weight: bold;
font-size: 1rem;
color: #2C4CB3;
text-align: start;
margin-bottom: 1rem;
}
.boton-asesor{
background-color: #4ca753;
color: #fff;
border: none;
padding: 0.5rem 0.75rem;
border-radius: 0.4rem;
display: flex;
justify-content: center;
margin: auto;
margin-top: 1.5rem;
font-size: 1rem;
cursor:pointer;
}