import { useEffect, useRef, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import IconoLista from "../../../../../assets/SVG/IconoLista";
import Swal from "sweetalert2";
import { Tooltip } from "@mui/material";
import { decrypt, validateToken } from "../../../../Components/Services/TokenService";

interface TiposSolicitudes {
  int_Nombre: string;
  int_idTipoSolicitud: number;
  tipoSolicitud:string
}

interface ClausulaLegal {
  str_Nombre: string;
  int_idClausulasLegales: number;
}

interface ClausulaIncluida {
  int_idClausulaLegal: number;
}

const ClausulasIncluidas = () => {
  const [TiposSolicitudes, setTiposSolicitudes] = useState<TiposSolicitudes[]>(
    []
  );
  const [clausulasLegales, setClausulasLegales] = useState<ClausulaLegal[]>([]);
  const [selectedClausulas, setSelectedClausulas] = useState<number[]>([]);
  const [selectedTipoSolicitud, setSelectedTipoSolicitud] = useState<
    number | null
  >(null);
  const [codigoTipoSolicitud, setCodigoTipoSolicitud] = useState<
  string | null
>("");
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const token = Cookies.get("Token");

  const Suscripcion = decrypt(Cookies.get("suscripcion"));

  const fetchTiposSolicitud = async () => {
await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ObtenerTipoSolicitud"](Suscripcion),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );
      const tiposSolicitudes = response.data;
      setTiposSolicitudes(tiposSolicitudes);

      // Seleccionar automáticamente el primer tipo de solicitud y cargar sus cláusulas
      if (tiposSolicitudes.length > 0) {
        const firstTipoSolicitud = tiposSolicitudes[0].int_idTipoSolicitud;
        setSelectedTipoSolicitud(firstTipoSolicitud);
        fetchClausulasIncluidas(firstTipoSolicitud);
      }
    } catch (error) {
    }
  };

  const fetchClausulas = async () => {
await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ObteneClausulasLegales"](Suscripcion),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );
      setClausulasLegales(response.data);
    } catch (error) {
    }
  };

  const fetchClausulasIncluidas = async (int_idTipoSolicitud: number) => {
    try {
      const response = await axios.get(
        API_GESTOR["ObtenerClausulasIncluidas"](
          Suscripcion,
          int_idTipoSolicitud
        ),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        }
      );
      const incluidas: ClausulaIncluida[] = response.data;
      setSelectedClausulas(
        incluidas.map((clausula) => clausula.int_idClausulaLegal)
      );
    } catch (error) {
      return error;
    }
  };

  const handleSelectTipoSolicitud = (idTipoSolicitud: number) => {
    setSelectedTipoSolicitud(idTipoSolicitud);
    fetchClausulasIncluidas(idTipoSolicitud);
  };
  const pruebita = (codtiposol: string) => {
  };
  const handleCheckboxChange = (id: number) => {
    setSelectedClausulas((prevSelected) => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter((item) => item !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  };

  const handleDeleteAndPostClausulas = async () => {
await validateToken();
    if (selectedTipoSolicitud) {
      try {
        await axios.delete(
          API_GESTOR["EliminarClausulasIncluidas"](
            Suscripcion,
            selectedTipoSolicitud
          ),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );

        const postPromises = selectedClausulas.map((clausulaId) => {
          return axios.post(API_GESTOR["AgregarClausulasIncluidas"](), {
            str_idSuscripcion: Suscripcion,
            int_idTipoSolicitud: selectedTipoSolicitud,
            int_idClausulaLegal: clausulaId,
            int_idUsuarioCreacion: idUsuario,
          },{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          });
        });
        Swal.fire(
          "¡Clausulas Incluidas!",
          "Las clausulas se incluyeron correctamente al tipo de solicitud.",
          "success"
        );

        await Promise.all(postPromises);
      } catch (error) {
        Swal.fire("", "Hubo un error.", "error");
      }
    }
  };
  const handleButtonClick = (str_CodTipoSol: string) => {
    fileInputRef.current?.click();
    setCodigoTipoSolicitud(str_CodTipoSol)
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    
  ) => {
    if (event.target.files) {
      handleFileUploadConfirmation(event.target.files[0]);
    }
  };
  const handleFileUploadConfirmation = (file: File) => {
    Swal.fire({
      title: "¿Estás seguro?",
      text: `Está seguro de sobreescribir el documento firmado?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, subirlo",
    }).then((result) => {
      if (result.isConfirmed) {
        handleSubmitFiles(file);
      }
    });
  };

  const handleSubmitFiles = async (file: File) => {
    if (!file) {
      Swal.fire("", "Por favor, selecciona un archivo primero", "error");
      return;
    }
    try {
      const formDataSolicitud = new FormData();
      formDataSolicitud.append("archivo", file);
      formDataSolicitud.append("str_idSuscripcion", Suscripcion);
      formDataSolicitud.append("str_CodTipoSol", codigoTipoSolicitud);
      formDataSolicitud.append("int_idUsuarioCreacion", idUsuario);

      const response = await axios.post(
        API_GESTOR["UploadPlantilla"](),
        formDataSolicitud,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            "Authorization": `Bearer ${token}`
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        Swal.fire("", "Archivo subido correctamente", "success");
        
      } else {
        throw new Error("No se pudo ingresar el archivo");
      }
    } catch (error) {
      
      Swal.fire("", "No se pudo subir el archivo", "error");
    }
  };
  // Dividir las cláusulas legales en columnas
  const divideInColumns = (items: ClausulaLegal[], columns: number) => {
    const perColumn = Math.ceil(items.length / columns);
    return Array.from({ length: columns }, (_, i) =>
      items.slice(i * perColumn, i * perColumn + perColumn)
    );
  };

  const clausulasDivididas = divideInColumns(clausulasLegales, 3);

  useEffect(() => {
    fetchTiposSolicitud();
    fetchClausulas();
  }, []);
  
  return (
    <div className="global-maestros">
      <div className="titulo-maestros lato-font">
        Incluir cláusulas al Tipo de Solicitud
      </div>
      <div className="container-gestor-administracion">
        <table className="tabla-gestor-administracion">
          <tbody>
            {TiposSolicitudes.length === 0 ? (
              <tr>
                <td colSpan={6} style={{ textAlign: "center" }}>
                  No se encontraron registros
                </td>
              </tr>
            ) : (
              TiposSolicitudes.map((tipoSolicitud) => (
                <tr key={tipoSolicitud.int_idTipoSolicitud}>
                  <td>
                    <div className="texto-tabla-gestor-administracion">
                      {tipoSolicitud.int_Nombre}
                    </div>
                    <Tooltip title="Asignar Clausulas" placement="top">
                      <div
                        className="icono-tabla-gestor-administracion"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          handleSelectTipoSolicitud(
                            tipoSolicitud.int_idTipoSolicitud
                          )
                        }
                      >
                        <IconoLista />
                      </div>
                    </Tooltip>
                    <Tooltip title="Asignar Plantilla" placement="top">
                      <div
                        className="icono-tabla-gestor-administracion"
                        style={{ cursor: "pointer" }}
                        onClick={() => handleButtonClick(tipoSolicitud.str_CodTipoSol)}
                      >
                        <i className="fa-solid fa-arrow-up-from-bracket"></i>
                        <input
                      ref={fileInputRef}
                      className="form-control"
                      type="file"
                      multiple
                      style={{ display: "none" }}
                      onChange={(event) =>
                        handleFileChange(event )
                      }
                    />
                      </div>
                    </Tooltip>
                    
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>

        {selectedTipoSolicitud && (
          <>
            <div className="div-clausulas-gestor-administracion">
              {clausulasLegales.length === 0 ? (
                <div>No se encontraron cláusulas</div>
              ) : (
                <div className="row contenedor-columnas-checkbox">
                  {clausulasDivididas.map((columna, colIndex) => (
                    <div
                      className="columna-checkbox"
                      key={colIndex}
                      style={{ flex: 1, margin: "0 0.625rem" }}
                    >
                      {columna.map((clausula, index) => (
                        <div className="form-check" key={index}>
                          <input
                            type="checkbox"
                            id={`clausula-${clausula.int_idClausulasLegales}`}
                            checked={selectedClausulas.includes(
                              clausula.int_idClausulasLegales
                            )}
                            onChange={() =>
                              handleCheckboxChange(
                                clausula.int_idClausulasLegales
                              )
                            }
                            className="form-check-input"
                          />
                          <label
                            htmlFor={`clausula-${clausula.int_idClausulasLegales}`}
                            className="form-check-label"
                          >
                            {clausula.str_Nombre}
                          </label>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              )}
              <div className="div-asignar-clausulas">
                <div
                  className="btn-asignar"
                  onClick={handleDeleteAndPostClausulas}
                >
                  Incluir Clausulas
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ClausulasIncluidas;
