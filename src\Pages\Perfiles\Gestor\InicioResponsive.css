@media screen and (max-width: 480px) {
    /*CONTENEDOR GLOBAL*/
.global-inicio-gestor {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f9feff;
    margin-bottom: 1.508rem;
  }
  .contenedor-card-inicio-gestor{
    display: flex;
    max-width: 100%;
    justify-content: center;
    align-items: stretch;
    gap: 2.2rem;
    margin: 1.3rem 3.75rem;
  }
  .lista-solicitudes-inicio-gestor{
    width: 45%;
    max-width: 45%;
    min-width: 45%;
    border-radius: 0.625rem 1.5rem 1.5rem 0.625rem ;
    border-left: 13px solid #2C4CB3;
    border-top: 0.0625rem solid #D9D9D9;
    border-bottom: 0.0625rem solid #D9D9D9;
    border-right: 0.0625rem solid #D9D9D9;
    min-height: 69vh;
    padding: 1.9rem;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
  .header-solicitudes-inicio-gestor{
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 2rem;
    color: #4B4B4B;
    cursor: pointer;
  }
  .table-solicitudes-inicio-gestor{
    width: 100%;
    margin-top: 2.2rem;
  }
  .div-estado-inicio-gestor{
    max-width:75%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.9375rem;
  }
  .div-estado-inicio-gestor span{
    text-align: right;
    display: block;
    width: 100%;
  }
  .div-estado-inicio-gestor svg{
    text-align: right;
    margin-right: 1.9rem;
  }

  .estadisticas-inicio-gestor{
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    border: 0.0625rem solid #D9D9D9;
    background: #F5F5F5;
    width: 30%;
    max-width: 30%;
    min-width: 30%;
    min-height: 69vh;
    padding: 1.9rem;
    border-radius: 1.5rem;
  }
  .header-estadisticas-inicio-gestor{
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 2rem;
    color: #4B4B4B;
    margin-bottom: 1.508rem;
    cursor: pointer;
  }
  .card-estadistica-inicio-gestor{
    margin: 0.3125rem 1.508rem;
    width: 90%;
    padding: 0.8rem 1.6rem;
    background-color: white;
    box-shadow: 0px 9px 14.7px 0px #0000000A;
    border-radius: 0.9375rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2.2rem;
  }
  .datos-card-estadistica-IG{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  
  }
  .titulo-estadistica-inicio-gestor {
    min-width: 60%;
    font-size: 1rem;

  }
  .dias-card-estadistica-ig{
    font-size: 2.65rem;
    color: #2C4CB3;
  }
  .dias-text-card-estadistica-ig{
    font-size: 1.3rem;
    color: #2C4CB3;
  }
  .titulo-estadistica-inicio-gesto{
    font-size: 1rem;
    color: black;
  }
  .conteo-inicio-gestor{
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    border: 0.0625rem solid #D9D9D9;
    background: white;
    width: 20%;
    max-width: 20%;
    min-width: 20%;
    min-height: 69vh;
    padding: 1.9rem;
    border-radius: 1.5rem;
  }
  .conteo-inicio-gestor-pageSolicitudes{
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    border: 0.0625rem solid #D9D9D9;
    background: white;
    width: 20%;
    max-width: 20%;
    min-width: 20%;
    min-height: 100%;
    padding: 1.9rem;
    border-radius: 1.5rem;
  }
  .header-conteo-inicio-gestor{
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 1.508rem;
    
  }
  .titulo-conteo-inicio-gestor{
    font-size: 1.5rem;
    color: #4B4B4B;
  }
  .titulo-solicitudes-inicio-gestor{
    font-size: 2rem;
  }
  .titulo-estadisticas-inicio-gestor{
    font-size: 2rem;
  }
  .fila-seleccionada{
    background-color:#F5F5F5;
  }
}