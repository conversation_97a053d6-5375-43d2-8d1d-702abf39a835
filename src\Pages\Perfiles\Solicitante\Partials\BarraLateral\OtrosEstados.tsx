
import HistorialSolicitud from "./HistorialSolicitud";
import GestorAsignado from "./GestorAsignado";
import { Tooltip } from "@mui/material";
import IconoVer from "../../../../../assets/SVG/IconoVer";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";

type SolicitudSeleccionada = {
  id: number;
  nombre: string;
};

type Historial = {
  id: number;
  descripcion: string;
  fecha: string;
};

type Aprobador = {
  id: number;
  nombre: string;
  rol: string;
};
type OtrosEstadosProps = {
  solicitudSeleccionada: SolicitudSeleccionada;
  historiales: Historial[];
  aprobadores: Aprobador[];
  idAplicacion: number;
  idUsuario: number;
  Suscripcion: string;
  Suscriptor: string;
};
const OtrosEstados: React.FC<OtrosEstadosProps> = ({
  solicitudSeleccionada,
  historiales,
  aprobadores,
  idAplicacion,
  idUsuario,
  Suscripcion,
  Suscriptor
}) => {
     const navigate = useNavigate();
     const handleVerSolicitud = () => {
       navigate(RoutesPrivate.EDITARSOLICITUD, {
         state: {
           solicitudSeleccionada,
           Suscripcion,
           idAplicacion,
           idUsuario,
           Suscriptor,
           ver: true,
         },
       });
     };
  return (
  <>
    <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
    
 
        <Tooltip title="Ver Solicitud" placement="top">
        <div className="icono-barralateral-acciones" onClick={handleVerSolicitud}>
          <IconoVer size={"1.3rem"} color={"#000"}/>
        </div>
        </Tooltip>
      </div>
      
    <GestorAsignado solicitudSeleccionada={solicitudSeleccionada} />
    <HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>
  </>
);
};
export default OtrosEstados;
