import { useEffect, useState } from "react";
import "./Header.css";
import "./HeaderResponsive.css";

import logoPrisma from "../../../../assets/Img/prisma_200x78.png";
import IconoAplicaciones from "../../../../assets/SVG/IconoAplicaciones";
import IconoNotification from "../../../../assets/SVG/IconoNotification";
import axios from "axios";
import Cookies from "js-cookie";
import ModalAplicaciones from "./ModalAplicaciones";
import fotoUsuario from "../../../../assets/Img/300-7.jpg";
import IconoLogout from "../../../../assets/SVG/IconoLogout";
import API_GESTOR from "../../../../assets/Api/ApisGestor";
import ModalEventosHoy from "./ModalEventosHoy";
import { decrypt, logout } from "../../Services/TokenService";

interface Aplicacion {
  idAplicacion: number;
  nombre: string | any | undefined;
  descripcion: string;
  avatar?: string;
  url: string;
}
const Header = () => {
  const [aplicacionesAsignadas, setAplicacionesAsignadas] = useState<
    Aplicacion[]
  >([]);
  const [aplicacionProcesos, setAplicacionProcesos] = useState({});

  const [perfilesAsignados, setPerfilesAsignados] = useState<
    Record<number, string | null>
  >({});
  const suscriptor = decrypt(Cookies.get("suscriptor"));
  const suscripcion = decrypt(Cookies.get("suscripcion"));
  const usuario = decrypt(Cookies.get("hora_llegada"));
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const Perfil = decrypt(Cookies.get("rol"));
  const app = decrypt(Cookies.get("app"));
  const correo = decrypt(Cookies.get("correo"));
  const sesion = decrypt(Cookies.get("sesion"));
  const token =  Cookies.get("Token") ;
  const fotoPerfil = localStorage.getItem("fotoPerfil");
  const [modales, setModales] = useState({
    isModalVisibleAplicaciones: false,
    isModalVisibleEventos: false,
  });
  const [showUserModal, setShowUserModal] = useState(false);
  const [eventosHoy, setEventosHoy] = useState([]);
  const [tareasHoy, setTareasHoy] = useState([]);
  const baseSeguridad = import.meta.env.VITE_SEGURIDAD_URL;

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  useEffect(() => {
    const verificarAplicacionesAsignadas = async () => {
      try {
        const response = await axios.get(
          `${baseSeguridad}asignacion_aplicacion/suscriptor/${suscriptor}/`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const aplicaciones = response.data;
        
        setAplicacionesAsignadas(aplicaciones);
        const perfilesPromises = aplicaciones.map(
          async (app: Aplicacion) => {
            try {
              const perfilResponse = await axios.get(
                `${baseSeguridad}perfiles/asignado/aplicacion/${app.idAplicacion}/usuario/${usuario}/`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              const { str_Nombre } = perfilResponse.data;
        
              return {
                idAplicacion: app.int_idAplicacion,
                perfil: str_Nombre ? str_Nombre : null,
              };
            } catch (error) {
              return { idAplicacion: app.int_idAplicacion, perfil: null };
            }
          }
        );
        const perfilesResults = await Promise.all(perfilesPromises);
        const perfilesMap: Record<number, string | null> = {};
        perfilesResults.forEach(({ idAplicacion, perfil }) => {
          perfilesMap[idAplicacion] = perfil;
        });
        setPerfilesAsignados(perfilesMap);
      } catch (error) {
        return error;
      }
    };
    const ObtenerEventosHoy = async () => {
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerEventosHoy"](usuario),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const eventos = response.data;
        const hoy = new Date();
        const peruTimeOffset = 0;
        const utcOffset = hoy.getTimezoneOffset();
        const adjustedTime =
          hoy.getTime() + (utcOffset + peruTimeOffset) * 60000;
        const hoyPeru = new Date(adjustedTime);
        hoyPeru.setHours(0, 0, 0, 0);
        const finSemana = new Date(hoyPeru);
        finSemana.setDate(hoyPeru.getDate() + 7);
        const eventosSemana = eventos.filter((evento) => {
          const fechaEvento = new Date(evento.dt_fechaEvento);
          const fechaEventoPeru = new Date(
            fechaEvento.getTime() +
              (fechaEvento.getTimezoneOffset() + peruTimeOffset) * 60000
          );
          fechaEventoPeru.setHours(0, 0, 0, 0);

          return fechaEventoPeru >= hoyPeru && fechaEventoPeru <= finSemana;
        });
        setEventosHoy(eventosSemana);
      } catch (error) {
        console.error("Error al obtener eventos:", error);
        setEventosHoy([]);
      }
    };
    const ObtenerTareasHoy = async () => {
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerTareasHoy"](usuario),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const tareas = response.data;
        const hoy = new Date();
        const peruTimeOffset = 0;
        const utcOffset = hoy.getTimezoneOffset();
        const adjustedTime =
          hoy.getTime() + (utcOffset + peruTimeOffset) * 60000;
        const hoyPeru = new Date(adjustedTime);
        hoyPeru.setHours(0, 0, 0, 0);
        const finSemana = new Date(hoyPeru);
        finSemana.setDate(hoyPeru.getDate() + 7);
        const tareasSemana = tareas.filter((tarea) => {
          const fechaTarea = new Date(tarea.dt_FechaFinTarea);
          const fechaTareaPeru = new Date(
            fechaTarea.getTime() +
              (fechaTarea.getTimezoneOffset() + peruTimeOffset) * 60000
          );
          fechaTareaPeru.setHours(0, 0, 0, 0);

          return fechaTareaPeru >= hoyPeru && fechaTareaPeru <= finSemana;
        });
        setTareasHoy(tareasSemana);
      } catch (error) {
        console.error("Error al obtener eventos:", error);
        setTareasHoy([]);
      }
    };
    ObtenerTareasHoy();
    verificarAplicacionesAsignadas();
    ObtenerEventosHoy();
    const intervalId = setInterval(() => {
      ObtenerEventosHoy();
      ObtenerTareasHoy();
    }, 10000);

    return () => clearInterval(intervalId);
  }, [suscriptor, usuario]);
  const clickFoto = () => {
    window.location.replace(
      "https://qaprisma.greta.pe/Prisma-Contratos/Inicio"
    );
  };
 
  return (
    <div className="global-header">
      <div className="container-header">
        <div className="container-logo-header">
          <img
            src={logoPrisma}
            alt="Logo Prisma"
            className="logo-header"
            onClick={clickFoto}
          />
        </div>
        <div className="container-Usuario-header montserrat-font">
          <div
            onClick={() => toggleModal("isModalVisibleAplicaciones")}
            style={{ cursor: "pointer" }}
          >
            <IconoAplicaciones />
          </div>

          <div
            className="icono-notificaciones"
            onClick={
              eventosHoy.length > 0 || tareasHoy.length > 0
                ? () => toggleModal("isModalVisibleEventos")
                : () => {}
            }
            style={{ position: "relative", cursor: "pointer" }}
          >
            <IconoNotification
              color={"white"}
              colorNotification={
                eventosHoy.length > 0 || tareasHoy.length > 0
                  ? "rgb(253, 18, 18)"
                  : "white"
              }
            />
            {(eventosHoy.length > 0 || tareasHoy.length > 0) && (
              <span
                style={{
                  position: "absolute",
                  top: "-5px",
                  right: "-5px",
                  backgroundColor: "red",
                  color: "white",
                  borderRadius: "50%",
                  padding: "0.125px 0.375px",
                  fontSize: "0.7rem",
                  fontWeight: "bold",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "1rem",
                  minHeight: "1rem",
                  animation: "parpadeo 1s infinite",
                }}
              >
                {eventosHoy.length + tareasHoy.length}
              </span>
            )}
          </div>
          <span>
            {Nombres} {Apellidos}
          </span>
          <div
            className="profile-container"
            onClick={() => setShowUserModal((showUserModal) => !showUserModal)}
            style={{ position: "relative" }}
          >
            <img
               src={
                !fotoPerfil || fotoPerfil === "" || fotoPerfil === null || fotoPerfil === "null"
                  ? 
                   fotoUsuario
                   :
                   `data:image/jpeg;base64,${fotoPerfil}`
              }
              alt=""
              className="img-perfil"
            />
            {showUserModal && (
              <div
                className="user-modal"
                style={{
                  position: "absolute",
                  top: "100%",
                  right: "0",
                  backgroundColor: "#fff",
                  boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
                  padding: "0.625rem",
                  borderRadius: "8px",
                  zIndex: 100,
                  width: "200px",
                  border: "0.0625rem solid #c9c9c9",
                }}
              >
                <button
                  onClick={logout}
                  style={{
                    padding: "0.3125rem 0.625rem",
                    backgroundColor: "transparent",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer",
                    width: "100%",
                    color: "#2C4CB3",
                    fontWeight: "bold",
                  }}
                >
                  <IconoLogout /> Cerrar sesión
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <ModalAplicaciones
        isModalVisibleAplicaciones={modales.isModalVisibleAplicaciones}
        CerrarModal={() => toggleModal("isModalVisibleAplicaciones")}
        aplicacionesAsignadas={aplicacionesAsignadas}
        Perfil={Perfil}
        perfilesAsignados={perfilesAsignados}
        suscriptor={suscriptor}
        suscripcion={suscripcion}
        documento={app}
        correo={correo}
        sesion_id={sesion}
      />
      <ModalEventosHoy
        isModalVisibleEventos={modales.isModalVisibleEventos}
        CerrarModal={() => toggleModal("isModalVisibleEventos")}
        eventos={eventosHoy}
        tareas={tareasHoy}
        aplicacionProcesos={aplicacionProcesos}
        suscripcion={suscripcion}
        documento={app}
        correo={correo}
        sesion_id={sesion}
        suscriptor={suscriptor}
      />
    </div>
  );
};

export default Header;
