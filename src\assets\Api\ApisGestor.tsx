const BASE_URL = import.meta.env.VITE_BASE_URL;
const BASE_URL8000 = import.meta.env.VITE_SEGURIDAD_URL;
const BASE_PROCESOS = import.meta.env.VITE_URL_PROCESOS;

type Suscripcion = string | number |undefined;
type IdUsuario = string | number;
type IdSolicitud = string | number;
type idContenidoSolicitud = string | number;
type IdAplicacion = string | number;
type Ruc = string | number;
type IdInterlocutor = string | number;

const API_GESTOR = {
  ObtenerSolicitudesGestor: (Suscripcion: Suscripcion, idUsuario: IdUsuario): string => 
    `${BASE_URL}api/solicitudes/gestor/?str_idSuscriptor=${Suscripcion}&int_idGestor=${idUsuario}`,
  ObtenerSolicitudesController: (Suscripcion: Suscripcion, idUsuario: IdUsuario): string => 
    `${BASE_URL}api/solicitudes/controller/?str_idSuscriptor=${Suscripcion}&int_idGestor=${idUsuario}`,
  ObtenerSolicitudesGerenteLegal: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/solicitudes/?str_idSuscriptor=${Suscripcion}`,
  ObtenerTipoSolicitud: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/tipo-solicitud/?str_idSuscripcion=${Suscripcion}`,
  ObtenerEstadosSolicitud: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/estados/?str_idSuscripcion=${Suscripcion}`,
  ObtenerEmpresas: (idAplicacion: IdAplicacion, Suscriptor: Suscripcion): string => 
    `${BASE_URL8000}empresas/asignadas/aplicacion/${idAplicacion}/suscriptor/${Suscriptor}/`,
  HistorialUsuario: (Suscripcion: Suscripcion, idSolicitud: IdSolicitud): string => 
    `${BASE_URL}api/historial-estados/?str_idSuscriptor=${Suscripcion}&int_idSolicitudes=${idSolicitud}`,
  ObtenerAprobadores: (Suscripcion: Suscripcion, int_idAplicacion: IdAplicacion): string => 
    `${BASE_URL8000}perfiles/nombre/Aprobador/suscripcion/${Suscripcion}/aplicacion/${int_idAplicacion}/`,
  InsertarAprobador: (): string => 
    `${BASE_URL}api/solicitudes/aprobadores-insertar/`,
  ActualizarEstado: (): string => 
    `${BASE_URL}api/solicitudes/editar-estado/`,
  ListarAprobadores: (Suscripcion: Suscripcion,Solicitud: IdSolicitud): string => 
    `${BASE_URL}api/solicitudes/aprobadores/${Suscripcion}/${Solicitud}/`,
  UploadArchivos: (): string => 
    `${BASE_URL}api/archivos/upload/`,
  IngresarTags: (): string => 
    `${BASE_URL}api/tags/`,
  EnviarDocumentoFirmado: (): string => 
    `${BASE_URL}api/solicitudes/firmaContrato/`,
  ArchivarSolicitud: (): string => 
    `${BASE_URL}api/solicitudes/archivar/`,
  ListarArchivosEditar: (Suscripcion: Suscripcion,idSolicitud:IdSolicitud): string => 
    `${BASE_URL}api/archivos/?str_idSuscriptor=${Suscripcion}&int_idSolicitudes=${idSolicitud}&str_CodTipoDocumento=DOAD`,
  ObtenerContenidoSolicitud: (idSolicitud: IdSolicitud): string => 
    `${BASE_URL}api/ContenidoSolicitud/${idSolicitud}/`,
  BuscarInterlocutorID: (idInterlocutor: IdInterlocutor): string => 
    `${BASE_URL}api/interlocutores/${idInterlocutor}/`,
  BuscarInterlocutor: (ruc: Ruc, Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/interlocutores/documento/${ruc}/?str_idSuscripcion=${Suscripcion}`,
  ObtenerUnidadesNegocios: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/unidades-negocios/?str_idSuscripcion=${Suscripcion}`,
  
  EliminarArchivo: (Suscripcion: Suscripcion,str_CodSolicitudes:string, nombre_archivo:string,int_idArchivos:string,tipo_adjunto:string | null ): string => 
    `${BASE_URL}api/archivos/eliminar/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=DOAD&nombre_archivo=${nombre_archivo}&int_idArchivos=${int_idArchivos}${tipo_adjunto ? "&tipo_adjunto="+tipo_adjunto : ""}`,
  ActualizarInterlocutor: (idInterlocutor: IdInterlocutor): string => 
    `${BASE_URL}api/interlocutores/${idInterlocutor}/`,
  AgregarInterlocutor: (): string => 
    `${BASE_URL}api/interlocutores/`,
  AgregarConsorcio: (): string => 
    `${BASE_URL}api/consorcio/`,
  ActualizarSolicitud: (idSolicitud: IdSolicitud): string => 
    `${BASE_URL}api/solicitudes/${idSolicitud}/`,
  ActualizarContenidoSolicitud: (idContenidoSolicitud: idContenidoSolicitud): string => 
    `${BASE_URL}api/ContenidoSolicitud/${idContenidoSolicitud}/`,
  InsertarContenidoSolicitud: (): string => 
    `${BASE_URL}api/ContenidoSolicitud/crear/`,
  ObtenerClausulasIncluidas: (Suscripcion: Suscripcion,int_idTipoSolicitud:number,idEmpresa:number): string => 
    `${BASE_URL}api/clausulasIncluidas/?str_idSuscripcion=${Suscripcion}&int_idTipoSolicitud=${int_idTipoSolicitud}&int_idEmpresa=${idEmpresa}`,
  ActivarClausulas: (): string => 
    `${BASE_URL}api/clausulasActivas/`,
  EliminarClausula: (idClausula:number,Suscripcion: Suscripcion,solicitud:number): string => 
    `${BASE_URL}api/clausulasActivas/${idClausula}/${Suscripcion}/${solicitud}`,
  ObtenerClausulasActivas: (Suscripcion: Suscripcion,solicitud:number): string => 
    `${BASE_URL}api/clausulasActivas/list/${Suscripcion}/${solicitud}`,
  EliminarTags: (solicitud:number): string => 
    `${BASE_URL}api/tags/eliminar/${solicitud}/`,

  DescargarArchivoNombre: (Suscripcion: Suscripcion,str_CodSolicitudes:string,nombre_archivo:string,tipo_adjunto:string | null): string => 
    `${BASE_URL}api/archivos/descargar/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=DOAD&nombre_archivo=${nombre_archivo}${tipo_adjunto ? "&tipo_adjunto="+tipo_adjunto : ""}`,

  ObtenerTiempoRespuestaTS: (Suscripcion:number,idTipoSolicitud:number,idEmpresa:number): string => 
    `${BASE_URL}api/TiempoRespuesta/?str_idSuscripcion=${Suscripcion}&int_idTipoSolicitud=${idTipoSolicitud}&int_idEmpresa=${idEmpresa}`,
  //OBTENER CONTEO SOLICITUDES

  SolicitudesGestor: (idUsuario:number,Suscripcion:number): string => 
    `${BASE_URL}api/calculos/Gestor/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}`,
  PromedioAtencion: (idUsuario:number,Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/Gestor/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PromedioPreparacion: (idUsuario:number,Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/Preparacion/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PromedioTotal: (idUsuario:number,Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/Total/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  SolicitudesTotal: (idUsuario:number,Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/calculos/solicitudes/Total/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,

  PromedioAtencionTotales: (idUsuario:number,Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/promedioTotal/Gestor/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}`,
  PromedioPreparacionTotales: (idUsuario:number,Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/promedioTotal/Preparacion/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}`,
  PromedioTotalTotales: (idUsuario:number,Suscripcion:number ,anio:number): string => 
    `${BASE_URL}api/promedioTotal/Total/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}`,
  SolicitudesTotalTotales: (idUsuario:number,Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/calculosTotal/solicitudes/Total/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}`,

  ContratosFirmados: (idUsuario:number,Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/contratosFirmados/Total/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PendientesporUN: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNFaltantes/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  FirmadosporUN : (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNFirmados/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PresupuestoUN: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNPresupuesto/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  SolicitudesPorEstado: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/DiferenciaEstados/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  HorasUN: (Suscripcion:number, anio:number,idUsuario:number,selectedEmpresaFiltro:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/HorasTrabajadas/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  MatrizSolicitudes: (Suscripcion:number, anio:number,idUsuario:number,codSolicitud:string,filtroMatrizEstado:string): string => 
    `${BASE_URL}api/solicitudes/gestor/matriz/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}${codSolicitud ? "&str_CodSolicitudes="+codSolicitud : ""}${filtroMatrizEstado ? "&firmado="+filtroMatrizEstado : ""}`,

  SolicitudesxFinalizar: (Suscripcion:number, anio:number,idUsuario:number): string => 
    `${BASE_URL}api/solicitudes/gestor/fin-contrato/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idGestor=${idUsuario}`,


  
  //OBTENER CONTEO SOLICITUDES

  SolicitudesGeneral: (Suscripcion:number): string => 
    `${BASE_URL}api/calculos/General/?str_idSuscriptor=${Suscripcion}`,
  PromedioAtencionGeneral: (Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,

  PromedioPreparacionGeneral: (Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/Preparacion/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PromedioTotalGeneral: (Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/promedio/Total/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  SolicitudesTotalGeneral: (Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/calculos/solicitudes/Total/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  ContratosFirmadosGeneral: (Suscripcion:number,selectedEmpresaFiltro:number, anio:number,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/contratosFirmados/Total/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PromedioAtencionGeneralTotales: (Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/promedioTotal/General/?str_idSuscriptor=${Suscripcion}&year=${anio}`,
  PromedioPreparacionGeneralTotales: (Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/promedioTotal/Preparacion/General/?str_idSuscriptor=${Suscripcion}&year=${anio}`,
  PromedioTotalGeneralTotales: (Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/promedioTotal/Total/General/?str_idSuscriptor=${Suscripcion}&year=${anio}`,
  SolicitudesTotalGeneralTotales: (Suscripcion:number, anio:number): string => 
    `${BASE_URL}api/calculosTotal/solicitudes/Total/General/?str_idSuscriptor=${Suscripcion}&year=${anio}`,

  PendientesporUNGeneral: (Suscripcion:number, anio:number,selectedEmpresaFiltro:number ,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNFaltantes/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${ selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  PresupuestoUNGeneral: (Suscripcion:number, anio:number,selectedEmpresaFiltro:number ,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/UNPresupuesto/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  SolicitudesPorEstadoGeneral: (Suscripcion:number, anio:number,selectedEmpresaFiltro:number ,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/DiferenciaEstados/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  HorasUNGeneral: (Suscripcion:number, anio:number,selectedEmpresaFiltro:number ,selectedTipoSolicitudFiltro:number): string => 
    `${BASE_URL}api/graficos/HorasTrabajadas/General/?str_idSuscriptor=${Suscripcion}&year=${anio}&int_idEmpresa=${selectedEmpresaFiltro}${selectedTipoSolicitudFiltro ? "&int_idTipoSol="+selectedTipoSolicitudFiltro : ""}`,
  MatrizSolicitudesGeneral: (Suscripcion:number, anio:number,codSolicitud:string,filtroMatrizEstado:string): string => 
    `${BASE_URL}api/solicitudes/general/matriz/?str_idSuscriptor=${Suscripcion}&year=${anio}${codSolicitud ? "&str_CodSolicitudes="+codSolicitud : ""}${filtroMatrizEstado ? "&firmado="+filtroMatrizEstado : ""}`,
  MatrizSolicitudesGestor: (idUsuario:number,Suscripcion:number, anio:number,codSolicitud:string,filtroMatrizEstado:string): string => 
    `${BASE_URL}api/solicitudes/gestor/matriz/?int_idGestor=${idUsuario}&str_idSuscriptor=${Suscripcion}&year=${anio}${codSolicitud ? "&str_CodSolicitudes="+codSolicitud : ""}${filtroMatrizEstado ? "&firmado="+filtroMatrizEstado : ""}`,
  ListarArchivo: (Suscripcion: Suscripcion,str_CodSolicitudes:string): string => 
    `${BASE_URL}api/archivos/listar-archivo/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=COAP`,
  //ADMINISTRACION 
  EliminarUnidadNegocio: (idUN:number): string => 
    `${BASE_URL}api/unidades-negocios/${idUN}/`,
  AgregarUnidadNegocio: (): string => 
    `${BASE_URL}api/unidades-negocios/`,
  ObtenerDivisas: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/tipocambio/?str_idSuscripcion=${Suscripcion}`,
  AgregarDivisa: (): string => 
    `${BASE_URL}api/tipocambio/`,
  ObteneClausulasLegales: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/clausulaslegales/?str_idSuscripcion=${Suscripcion}`,
  AgregarClausulas: (): string => 
    `${BASE_URL}api/clausulaslegales/`,
  AgregarClausulasIncluidas: (): string => 
    `${BASE_URL}api/clausulasIncluidas/`,
  EliminarClausulaLegal: (idcl:number): string => 
    `${BASE_URL}api/clausulaslegales/${idcl}/`,
  EliminarClausulasIncluidas: (Suscripcion:string | undefined,selectedTipoSolicitud:number): string => 
    `${BASE_URL}api/clausulasIncluidas/${Suscripcion}/${selectedTipoSolicitud}/`,
  

  //CONSORCIO
  ActualizarConsorcio: (): string => 
    `${BASE_URL}api/consorcio/editar/`,

  //DESCARGAR ARCHIVOS
  ListarPlantilla: (Suscripcion: Suscripcion,str_CodTipoSolicitud:string,empresa:number,TipoPlantilla:string): string => 
    `${BASE_URL}api/archivos/plantilla/listar-plantilla/?str_idSuscripcion=${Suscripcion}&str_CodTipoSolicitud=${str_CodTipoSolicitud}&int_idEmpresa=${empresa}&str_TipoPlantilla=${TipoPlantilla}`,
  DescargarPlantilla: (Suscripcion: Suscripcion,str_CodTipoSolicitud:string,empresa:number,TipoPlantilla:string): string => 
    `${BASE_URL}api/archivos/descargar-plantilla/?str_idSuscripcion=${Suscripcion}&str_CodTipoSolicitud=${str_CodTipoSolicitud}&int_idEmpresa=${empresa}&str_TipoPlantilla=${TipoPlantilla}`,
  UploadPlantilla: (): string => 
    `${BASE_URL}api/archivos/plantilla/upload/`,

  //ADENDAS
  CrearAdenda: (): string => 
    `${BASE_URL}api/solicitudes/crearAdenda/`,

  //EXTRA JUDICIALES
  CrearEJ: (): string => 
    `${BASE_URL}api/solicitudes/crearEJ/`,

  //PROCESOS
  ObtenerProcesosAdmin: (Suscripcion: Suscripcion): string => 
    `${BASE_PROCESOS}procesos/suscripcion/${Suscripcion}/`,
  ObtenerEventosHoy: (Usuario: number): string => 
    `${BASE_PROCESOS}eventos/get_eventos_by_usuario/${Usuario}/`,
  ObtenerTareasHoy: (Usuario: number): string => 
    `${BASE_PROCESOS}tareas/get_tareas_by_usuario/${Usuario}/`,
  //GESTORES
  ObtenerGestores: (Suscripcion: Suscripcion): string => 
    `${BASE_URL8000}usuarios/gestores/suscripcion/${Suscripcion}/`,
  DatosGestor: (Suscripcion: Suscripcion,idUsuario:number): string => 
    `${BASE_URL8000}usuarios/gestor/usuario/${idUsuario}/suscripcion/${Suscripcion}/`,
  //BOT
  ObtenerKeysBot: (Suscripcion: Suscripcion): string => 
    `${BASE_URL}api/credentialBot/?str_idSuscripcion=${Suscripcion}`,
  };
  

export default API_GESTOR;