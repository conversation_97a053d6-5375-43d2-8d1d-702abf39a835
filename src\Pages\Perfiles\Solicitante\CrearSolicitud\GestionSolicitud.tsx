import Header from "../../../Components/Partials/Header/Header";
import Cookies from "js-cookie";
import Titulo from "../../../Components/Partials/Seccion/Titulo";
import { useLocation } from "react-router-dom";
import "./GestionSolicitud.css";
import PrestacionServicios from "./TipoSolicitudes/PrestacionServicio/PrestacionServicio";
import LocacionServicio from "./TipoSolicitudes/LocacionServicio/LocacionServicio";
import PrestacionServicioPro from "./TipoSolicitudes/PrestacionServicioPro/PrestacionServicioPro";
import AcuerdoConfidencialidad from "./TipoSolicitudes/AcuerdoConfidencialidad/AcuerdoConfidencialidad";
import ArrendamientoBienes from "./TipoSolicitudes/ArrendatarioBienes/ArrendamientoBienes";
import CompraVenta from "./TipoSolicitudes/CompraVenta/CompraVenta";
import Consorcio from "./TipoSolicitudes/Consorcio/Consorcio";
import { RoutesPrivate } from "../../../../Security/Routes/ProtectedRoute";
import { decrypt } from "../../../Components/Services/TokenService";

const GestionSolicitud = () => {
  const location = useLocation();
  const editar = location.state?.esEditar;
  const ver = location.state?.ver;
  const selectedTipo = location.state?.selectedTipo;
  const selectedSolicitud = location.state?.selectedSolicitud;
  const selectedTipoModelo = location.state?.selectedTipoModelo;
  const idAplicacion = location.state?.idAplicacion;
  const Suscriptor = location.state?.Suscriptor;
  const Suscripcion = location.state?.Suscripcion;
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const UsuarioId = decrypt(Cookies.get("hora_llegada"));
  const codigoSolicitud = editar===true ? selectedSolicitud.str_CodSolicitudes : selectedTipo?.codigo;
  const idTipoSolicitud = editar===true ? "" : selectedTipo?.id;
  const NomTipoSolicitud = editar===true ? selectedSolicitud.nombre_TipoSolicitud : selectedTipo?.value;
  console.log("NomTipoSolicitud", NomTipoSolicitud);
   return (
    <div>
      <Header />
      <Titulo seccion={`Solicitud ${codigoSolicitud}`} salir={true} paginaSalir={RoutesPrivate.INICIOSOLICITANTE}/>
     {NomTipoSolicitud === "Contrato de prestación de servicios" || NomTipoSolicitud === "Contrato de Depósito Simple"  || NomTipoSolicitud === "Contrato de Administración Almacén On Site" || NomTipoSolicitud === "Contrato de Depósito Temporal" || NomTipoSolicitud === "Contrato de Transporte" || NomTipoSolicitud === "Contrato de Distribución" || 
     NomTipoSolicitud === "Contrato de Depósito y Transporte" || NomTipoSolicitud === "Contrato de Servicio SILE" || NomTipoSolicitud === "Contrato de Depósito temporal y Transporte"?
      <PrestacionServicios 
        Nombres={Nombres} 
        Apellidos={Apellidos} 
        UsuarioId={UsuarioId} 
        Suscriptor={Suscriptor} 
        idAplicacion={idAplicacion} 
        Suscripcion={Suscripcion} 
        idTipoSolicitud={idTipoSolicitud} 
        tipoModelo={selectedTipoModelo} 
        esEdicion={editar} 
        selectedSolicitud={selectedSolicitud}
        ver={ver}
        NomTipoSolicitud={NomTipoSolicitud}
      />
      :
      NomTipoSolicitud === "Contrato de locación de servicios de cliente" || NomTipoSolicitud === "Contrato de locación de servicios de proveedor" || NomTipoSolicitud === "Contrato de locación de servicios" ?
      <LocacionServicio 
        Nombres={Nombres} 
        Apellidos={Apellidos} 
        UsuarioId={UsuarioId} 
        Suscriptor={Suscriptor} 
        idAplicacion={idAplicacion} 
        Suscripcion={Suscripcion} 
        idTipoSolicitud={idTipoSolicitud} 
        tipoModelo={selectedTipoModelo} 
        esEdicion={editar} 
        selectedSolicitud={selectedSolicitud}
        ver={ver}
         NomTipoSolicitud={NomTipoSolicitud}
      />
      :
      NomTipoSolicitud === "Contrato de prestación de servicios profesionales" ?  
      <PrestacionServicioPro
        Nombres={Nombres} 
        Apellidos={Apellidos} 
        UsuarioId={UsuarioId} 
        Suscriptor={Suscriptor} 
        idAplicacion={idAplicacion} 
        Suscripcion={Suscripcion} 
        idTipoSolicitud={idTipoSolicitud} 
        tipoModelo={selectedTipoModelo} 
        esEdicion={editar} 
        selectedSolicitud={selectedSolicitud}
        ver={ver}
         NomTipoSolicitud={NomTipoSolicitud}
      />
      :
      NomTipoSolicitud === "Acuerdo de Confidencialidad Cliente"  || NomTipoSolicitud === "Acuerdo de Confidencialidad Proveedor" || NomTipoSolicitud === "Acuerdo de confidencialidad" ?  
      <AcuerdoConfidencialidad
        Nombres={Nombres} 
        Apellidos={Apellidos} 
        UsuarioId={UsuarioId} 
        Suscriptor={Suscriptor} 
        idAplicacion={idAplicacion} 
        Suscripcion={Suscripcion} 
        idTipoSolicitud={idTipoSolicitud} 
        tipoModelo={selectedTipoModelo} 
        esEdicion={editar} 
        selectedSolicitud={selectedSolicitud}
        ver={ver}
         NomTipoSolicitud={NomTipoSolicitud}
      />
    :
     NomTipoSolicitud === "Contrato de arrendamiento de bienes muebles o inmuebles" ?
     <ArrendamientoBienes
        Nombres={Nombres} 
        Apellidos={Apellidos} 
        UsuarioId={UsuarioId} 
        Suscriptor={Suscriptor} 
        idAplicacion={idAplicacion} 
        Suscripcion={Suscripcion} 
        idTipoSolicitud={idTipoSolicitud} 
        tipoModelo={selectedTipoModelo} 
        esEdicion={editar} 
        selectedSolicitud={selectedSolicitud}
        ver={ver}
          NomTipoSolicitud={NomTipoSolicitud}
      />
    : NomTipoSolicitud === "Contrato de compra / venta de bienes muebles o inmuebles" ?
    <CompraVenta
       Nombres={Nombres} 
       Apellidos={Apellidos} 
       UsuarioId={UsuarioId} 
       Suscriptor={Suscriptor} 
       idAplicacion={idAplicacion} 
       Suscripcion={Suscripcion} 
       idTipoSolicitud={idTipoSolicitud} 
       tipoModelo={selectedTipoModelo} 
       esEdicion={editar} 
       selectedSolicitud={selectedSolicitud}
       ver={ver}
         NomTipoSolicitud={NomTipoSolicitud}
     />
   : NomTipoSolicitud === "Contrato de consorcio" ?
   <Consorcio
      Nombres={Nombres} 
      Apellidos={Apellidos} 
      UsuarioId={UsuarioId} 
      Suscriptor={Suscriptor} 
      idAplicacion={idAplicacion} 
      Suscripcion={Suscripcion} 
      idTipoSolicitud={idTipoSolicitud} 
      tipoModelo={selectedTipoModelo} 
      esEdicion={editar} 
      selectedSolicitud={selectedSolicitud}
      ver={ver}
        NomTipoSolicitud={NomTipoSolicitud}
    />
  :""}
    </div>
  );
};

export default GestionSolicitud;