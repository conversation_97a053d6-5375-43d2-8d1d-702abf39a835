import React, { useEffect, useState } from 'react';
import IconoLeft from '../../../../../../assets/SVG/IconoLeft';
import IconoRight from '../../../../../../assets/SVG/IconoRight';

const Matriz = ({solicitudes, selectedFields, handleDownload,formatDate , setFiltroMatrizEstado, filtroMatrizEstado}) => {
 
 
  useEffect(() => {
    setCurrentPage(1);
  }, [solicitudes]);
  // Función para formatear las fechas dentro del componente
  const camposDeFecha = ["Fecha Firma", "Fecha Registro", "Fecha Fin"];
  /*PAGINACION*/
 const [currentPage, setCurrentPage] = useState(1);
 const solicitudesPorPagina = 10;
 const indiceUltimaSolicitud = currentPage * solicitudesPorPagina;
 const indicePrimeraSolicitud = indiceUltimaSolicitud - solicitudesPorPagina;
 const solicitudesActuales = solicitudes.slice(
   indicePrimeraSolicitud,
   indiceUltimaSolicitud
 );
 const handlePageChange = (pageNumber: React.SetStateAction<number>) => {
   setCurrentPage(pageNumber);
 };
 const totalPaginas = Math.ceil(solicitudes.length / solicitudesPorPagina);

 const renderNumeritosPaginacion = () => {
   const numeritos = [];
   for (let i = 1; i <= totalPaginas; i++) {
     numeritos.push(
       <button
         key={i}
         className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
         onClick={() => handlePageChange(i)}
       >
         {i}
       </button>
     );
   }
   return numeritos;
 };
  return (
    <div className="div-contenedor-Reportes-gestor">
    <div className="div-boton-descargar-csv" style={{justifyContent:"space-between"}}>
      <select name="" id="" className='form-select' value={filtroMatrizEstado} style={{maxWidth:'20%'}} onChange={()=> setFiltroMatrizEstado(event.target.value)}>
        <option value="">Todo</option>
        <option value="si">Contratos</option>
        <option value="no">Solicitudes</option>

      </select>
      <button className="boton-descargar-csv" onClick={handleDownload}>
        Descargar archivo excel <i className="fa-solid fa-download"></i>
      </button>
    </div>
    <div className="div-tabla-matriz-reporte-gestor">
      <table className="tabla-matriz-reporte-gestor">
        <thead>
          <tr>
            {selectedFields.map((field, index) => (
              <th key={index}>{field}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {solicitudesActuales.map((solicitud) => (
            <tr key={solicitud.int_idSolicitudes}>
              {selectedFields.map((field, index) => (
                <td key={index}>
                  {camposDeFecha.includes(field) && solicitud[field]
                    ? formatDate(solicitud[field])
                    : solicitud[field] || '-'}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
        <tfoot>
              <tr>
                <td colSpan="6">
                  <div className="paginacion-tabla-solicitante">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="numero-pagina"
                    >
                                            <IconoLeft  size={"1.3rem"} color={"#000"}/>

                    </button>
                    {renderNumeritosPaginacion()}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={indiceUltimaSolicitud >= solicitudes.length}
                      className="numero-pagina"
                    >
                      <IconoRight size={"1.5rem"} color={"#000"}/>
                    </button>
                  </div>
                </td>
              </tr>
            </tfoot>
      </table>
    </div>
  </div>
  );
};

export default Matriz;