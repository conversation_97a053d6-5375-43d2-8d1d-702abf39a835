const BASE_URL = import.meta.env.VITE_BASE_URL;

type Suscripcion = string | number | any;
type IdUsuario = string | number | any;

type Solicitud = string | number;

const API_APROBADOR = {
  ObtenerSolicitudesAprobador: (Suscripcion: Suscripcion, idUsuario: IdUsuario): string => 
    `${BASE_URL}api/solicitudes/aprobador/?str_idSuscriptor=${Suscripcion}&int_idAprobador=${idUsuario}`,
  
  AprobarSolicitud: (solicitud: Solicitud, idUsuario: IdUsuario, suscripcion: Suscripcion): string => 
    `${BASE_URL}api/solicitudes/aprobador/aprobar/${solicitud}/${idUsuario}/${suscripcion}/`,
  RechazarSolicitud: (solicitud: Solicitud, idUsuario: IdUsuario): string => 
    `${BASE_URL}api/solicitudes/aprobador/rechazar/${solicitud}/${idUsuario}/`,

  ListarAprobadores: (Suscripcion:Suscripcion,Solicitud:Solicitud): string => 
    `${BASE_URL}api/solicitudes/aprobadores/${Suscripcion}/${Solicitud}/`,
  ListarArchivo: (Suscripcion: Suscripcion,str_CodSolicitudes:string): string => 
    `${BASE_URL}api/archivos/listar-archivo/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=COAP`,
  DescargarArchivo: (Suscripcion: Suscripcion,str_CodSolicitudes:string): string => 
    `${BASE_URL}api/archivos/descargar-archivo/?str_idSuscriptor=${Suscripcion}&str_CodSolicitudes=${str_CodSolicitudes}&str_CodTipoDocumento=COAP`,
};

export default API_APROBADOR;