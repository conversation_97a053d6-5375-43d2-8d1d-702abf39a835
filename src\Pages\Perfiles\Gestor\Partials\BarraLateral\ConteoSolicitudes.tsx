import { useEffect, useState } from 'react';
import API_GESTOR from '../../../../../assets/Api/ApisGestor';
import axios from 'axios';
import IconoRight from '../../../../../assets/SVG/IconoRight';
import { RoutesPrivate } from '../../../../../Security/Routes/ProtectedRoute';
import { useNavigate } from 'react-router-dom';

const ConteoSolicitudes = ({  solicitudes ,idUsuario,Suscripcion}) => {
  // const [estadisticas, setEstadisticas] = useState([]);
  
  // const estadosPermitidos = ["Aceptado", "Aprobado","Asignado", "En Proceso", "En Aprobación", "En Validación", "Firmado", perfil === "Gestor Controller" ? "Nuevo" : null ];

  // const ConteoSolicitudesGestor = async () => {
  //   try {
  //     const response = await axios.get(API_GESTOR["SolicitudesGestor"](idUsuario,Suscripcion));
      
  //     const estadosFiltrados = response.data
  //       .filter((estado:string) => estadosPermitidos.includes(estado.nombre_estado))
  //       ; 
  //     setEstadisticas(estadosFiltrados);
  //   } catch (error) {
  //     console.error("Error al obtener las estadísticas", error);
  //   }
  // };

  // useEffect(() => {
  //   ConteoSolicitudesGestor();
  // }, [idUsuario]);
  const navigate = useNavigate();

  const nuevoCount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Nuevo").length;
  const asignadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Asignado").length;
  const enprocesocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Proceso").length;
  const envalidacioncount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Validación").length;
  const FirmadoCount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Firmado").length;
  const Aceptadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aceptado").length;
  const EnAprobacioncount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Aprobación").length;
  const Aprobadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aprobado").length;
  
  return (
    <div className="contador-estados-inicio-solicitante montserrat-font">
  
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Asignadas'> </span>
          <div className='titulo-conteo'>Asignadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{asignadocount}</div>
        
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>En Proceso</div> 
          <div  className="valor-contador-estados-IS montserrat-font-500">{enprocesocount}</div>
          <span className='color-estado-IS-EnProceso'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Aceptadas'> </span>
          <div className='titulo-conteo'>Aceptadas</div> 
          <div  className="valor-contador-estados-IS montserrat-font-500">{Aceptadocount}</div>
        
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>En Validación</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{envalidacioncount}</div>
          <span className='color-estado-IS-EnValidacion'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-EnAprobacion'> </span>
          <div className='titulo-conteo'>En Aprobación</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{EnAprobacioncount}</div>
          
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Aprobadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{Aprobadocount}</div>
          <span className='color-estado-IS-Aprobadas'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Firmadas'> </span>
          <div className='titulo-conteo'>Firmadas</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{FirmadoCount}</div>
        </div>
        <div className="estado-flecha-estadisticas" onClick={() => navigate(RoutesPrivate.REPORTESGESTOR, { state: { idUsuario, Suscripcion } })}>
              <IconoRight size={"2rem"} selected={true} colorselected={"#156CFF"} color={"#156CFF"} />
            </div>
      </div>
  );
};

export default ConteoSolicitudes;