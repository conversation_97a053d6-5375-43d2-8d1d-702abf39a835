import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>per, TextField } from "@mui/material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import axios from "axios";
import Select from "react-select";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import BarraLateralCrearSolicitud from "../../BarraLateral/BarraLateralCrearSolicitud";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import API_GESTOR from "../../../../../../assets/Api/ApisGestor";
import IconoMoneda from "../../../../../../assets/SVG/IconoMoneda";
import IconoDolares from "../../../../../../assets/SVG/IconoSoles";
import getLogs from "../../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
interface FormDataSolicitud {
  int_idEmpresa: number | null;
  int_idUnidadNegocio: number | null;
  db_Honorarios: number | null;
  dt_FechaEsperada: string | null;
}

interface FormDataInterlocutor {
  str_Documento: string;
  str_Interlocutor: string;
  str_Correo: string;
  str_RepLegal: string;
}

interface FormDataContenido {
  str_ObjetivoContrato: string;
  db_Presupuesto: number | null;
  str_PlazoSolicitud: string;
  str_TipoServicio: string;
  str_Margen: string;
}

// Define el tipo de los errores
interface Errors {
  int_idEmpresa?: string;
  int_idUnidadNegocio?: string;
  str_Documento?: string;
  str_Interlocutor?: string;
  db_Honorarios?: string;
  dt_FechaEsperada?: string;
  str_Correo?: string;
  str_RepLegal?: string;
  str_ObjetivoContrato?: string;
  db_Presupuesto?: string;
  str_PlazoSolicitud?: string;
  str_TipoServicio?: string;
  str_Margen?: string;
  str_InfoAdicional?: string;
  str_plazoForzoso?: string;
  str_servicioIniciado?: string;
  db_inversion?: string;
  str_CondicionPago?: string;

}
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
const EditarPrestacionServicio = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  esEdicion,
  ver,
  NomTipoSolicitud
}) => {
  const [files, setFiles] = useState([]);
  const [newFiles, setNewFiles] = useState([]);
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [unidadesNegocios, setUnidadesNegocios] = useState([]);
  const [selectedUnidadesNegocios, setSelectedUnidadesNegocios] = useState(
    selectedSolicitud.int_idUnidadNegocio
  );
  const [selectedEmpresa, setSelectedEmpresa] = useState(
    selectedSolicitud.int_idEmpresa
  );
  const [interlocutorEncontrado, setInterlocutorEncontrado] = useState(false);
  const [idInterlocutor, setIdInterlocutor] = useState(null);
  const [errors, setErrors] = useState({});
  const token = Cookies.get("Token");
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");
  const [activeStep, setActiveStep] = useState(0);
  const [FechaMinima, setFechaMinima] = useState(getLocalDate());
  const [TiposMoneda, setTiposMoneda] = useState({
    str_Moneda: "",
    str_Pais: "",
  });

  const navigate = useNavigate();
  const handleStepChange = (step) => {
    setActiveStep(step);
  };

  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioModificacion: UsuarioId,
    int_idEmpresa: selectedEmpresa,
    int_idUnidadNegocio: selectedUnidadesNegocios,
    int_idTipoSol: selectedSolicitud.int_idTipoSol,
    int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
    str_DeTerceros: selectedSolicitud.str_DeTerceros,
    dt_FechaEsperada:
      selectedSolicitud.dt_FechaEsperada ||
      new Date().toISOString().split("T")[0],
    db_Honorarios: selectedSolicitud.db_Honorarios,
    str_idSuscriptor: Suscripcion,
  });
  const [formDataContenido, setFormDataContenido] = useState({
    str_DocAdjuntos: files ? "si" : newFiles.length >= 1 ? "si" : "no",
    str_ObjetivoContrato: "",
    db_Presupuesto: 0,
    str_PlazoSolicitud: "",
    str_Moneda: selectedTipoMoneda,
    str_TipoServicio: "",
    str_Margen: "",
    str_InfoAdicional: "",
    int_idInterlocutor: null,
    str_plazoForzoso: "",
    str_servicioIniciado: "",
    db_inversion: null,
    str_CondicionPago: "",
    str_DetallePenalidades: "",
    str_Penalidades: "no",
    str_ans: "no",
  });

  const [formDataInterlocutor, setFormDataInterlocutor] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_Correo: "",
    str_RepLegal: "",
    str_Domicilio: "",
  });
  const validateForm = (): boolean => {
    let newErrors: Errors = {}; // Tipo para los errores

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
    if (!formDataInterlocutor.str_Documento)
      newErrors.str_Documento = "El Documento es requerido";
    if (!formDataInterlocutor.str_Interlocutor)
      newErrors.str_Interlocutor = "El proveedor es requerido";
    if (!formDataSolicitud.db_Honorarios)
      newErrors.db_Honorarios = "Los honorarios son requeridos";
    if (!formDataSolicitud.dt_FechaEsperada)
      newErrors.dt_FechaEsperada = "La fecha estimada de entrega es requerida";
    if (!formDataInterlocutor.str_Correo) {
      newErrors.str_Correo = "El Correo es requerido";
    } else {
      const correoValido = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(
        formDataInterlocutor.str_Correo.trim()
      );
      if (!correoValido) {
        newErrors.str_Correo = "El Correo no es válido";
      }
    }
    if (!formDataInterlocutor.str_RepLegal)
      newErrors.str_RepLegal = "El Representante legal es requerido";
    if (!formDataContenido.str_ObjetivoContrato)
      newErrors.str_ObjetivoContrato = "El Objeto del contrato es requerido";
    if (!formDataContenido.db_Presupuesto)
      newErrors.db_Presupuesto = "El Presupuesto es requerido";
    if (!formDataContenido.str_PlazoSolicitud)
      newErrors.str_PlazoSolicitud = "El Plazo es requerido";
    if (!formDataContenido.str_TipoServicio)
      newErrors.str_TipoServicio = "El Tipo de servicio es requerido";
    if (!formDataContenido.str_Margen)
      newErrors.str_Margen = "El Margen es requerido";
    // if (!formDataContenido.str_plazoForzoso)
    //   newErrors.str_plazoForzoso = "El plazo forzoso es requerido";
    // if (!formDataContenido.str_servicioIniciado)
    //   newErrors.str_servicioIniciado = "El servicio iniciado es requerido";
    // if (formDataContenido.str_plazoForzoso === "si" && !formDataContenido.db_inversion)
    //   newErrors.db_inversion = "La inversion es requerida";
    // if (!formDataContenido.str_CondicionPago)
    //   newErrors.str_CondicionPago = "La condición de pago es requerida";
    setErrors(newErrors);

    // Retorna true si no hay errores
    return Object.keys(newErrors).length === 0;
  };
  const steps = [
    "DATOS GENERALES",
    "DATOS DE CONTRATO",
    "INFORMACIÓN ADICIONAL",
  ];
  const fetchArchivos = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["ListarArchivosEditar"](
          Suscripcion,
          selectedSolicitud.int_idSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setFiles(response.data);
    } catch (error) {
      console.error("Error al obtener las empresas:", error);
    }
  };
  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            db_Presupuesto: response.data.db_Presupuesto,
            str_PlazoSolicitud: response.data.str_PlazoSolicitud,
            str_TipoServicio: response.data.str_TipoServicio,
            str_InfoAdicional: response.data.str_InfoAdicional,
            str_Margen: response.data.str_Margen,
            int_idInterlocutor: response.data.int_idInterlocutor,
            str_Moneda: response.data.str_Moneda || "dolares",
            db_Honorarios: response.data.db_Honorarios,
            str_plazoForzoso: response.data.str_plazoForzoso,
            str_servicioIniciado: response.data.str_servicioIniciado,
            db_inversion: response.data.db_inversion,
            str_CondicionPago: response.data.str_CondicionPago,
             str_DetallePenalidades: response.data.str_DetallePenalidades,
            str_Penalidades: response.data.str_Penalidades,
            str_ans: response.data.str_ans,
          }));
          setSelectedTipoMoneda(response.data.str_Moneda || "dolares");
          const responseInterlocutor = await axios.get(
            API_SOLICITANTE["BuscarInterlocutorID"](
              response.data.int_idInterlocutor
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseInterlocutor.data) {
            setFormDataInterlocutor((prevData) => ({
              ...prevData,
              str_Documento: responseInterlocutor.data.str_Documento,
            }));
            buscarInterlocutor(responseInterlocutor.data.str_Documento);
          } else {
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            db_Presupuesto: 0,
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_Margen: "",
            str_InfoAdicional: "",
          }));
        }
      } catch (error) {}
    };

    const fetchEmpresas = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEmpresas"](idAplicacion, Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };

    fetchEmpresas();
    DatosContenidoSolicitud();
    fetchArchivos();
  }, []);
  useEffect(() => {
    const fetchUnidadesNegocios = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerUnidadesNegocios"](
            Suscripcion,
            selectedEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idUnidadesNegocio: any; str_Descripcion: any }) => ({
            value: tipo.int_idUnidadesNegocio,
            label: tipo.str_Descripcion,
          })
        );
        setUnidadesNegocios(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    fetchUnidadesNegocios();
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](selectedEmpresa, Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerTiempoRespuestaTS"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const responseData = response.data;
        const ultimoObjeto =
          responseData.length > 0
            ? responseData[responseData.length - 1]
            : null;

        const nuevaFechaEsperada = addDaysToDate(
          new Date(),
          ultimoObjeto.int_TiempoRespuesta
            ? ultimoObjeto.int_TiempoRespuesta
            : 0
        );
        setFormDataSolicitud((prevState) => ({
          ...prevState,
          dt_FechaEsperada: nuevaFechaEsperada,
        }));
        setFechaMinima(nuevaFechaEsperada);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    ObtenerTiempoRespuestaTipoSolicitud();
    fetchMoneda();
  }, [selectedEmpresa]);
  useEffect(() => {
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      int_idEmpresa: selectedEmpresa,
      int_idUnidadNegocio: selectedUnidadesNegocios,
    }));
  }, [selectedEmpresa, selectedUnidadesNegocios, formDataContenido]);

  const handleChangeEmpresa = (selectedOption: React.SetStateAction<null>) => {
    setSelectedEmpresa(selectedOption.value);
  };
  const handleChangeUnidadesNegocios = (
    selectedOption: React.SetStateAction<null>
  ) => {
    setSelectedUnidadesNegocios(selectedOption.value);
  };
  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);

    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  const handleFileChange = (event) => {
    const fileArray = Array.from(event.target.files);
    setNewFiles((prevFiles) => [...prevFiles, ...fileArray]); // Agregar nuevos archivos a los existentes
  };
  const EliminarArchivo = async (file) => {
    await validateToken();
    try {
      const response = await axios.delete(
        API_SOLICITANTE["EliminarArchivo"](
          Suscripcion,
          selectedSolicitud.str_CodSolicitudes,
          file.nombre_archivo,
          file.int_idArchivos,
          file.tipo_adjunto
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        fetchArchivos();
      }
    } catch (error) {
      console.error("Error al eliminar la solicitud:", error);
    }
  };
  const handleFileRemove = (index, isStoredFile, file) => {
    if (isStoredFile) {
      EliminarArchivo(file);
    } else {
      setNewFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      dt_FechaEsperada: `${selectedDate}T00:00:00`,
    }));
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    
    // Caso especial para str_plazoForzoso
    if (name === "str_plazoForzoso") {
      setFormDataContenido((prevData) => ({
        ...prevData,
        [name]: value,
        // Si plazo forzoso es "no", establecer db_inversion como null
        ...(value === "no" ? { db_inversion: null } : {})
      }));
    } 
    // Para otros campos
    else {
      setFormDataSolicitud((prevData) => ({
        ...prevData,
        [name]: value,
      }));
      
      setFormDataInterlocutor((prevData) => ({
        ...prevData,
        [name]: value,
      }));

      setFormDataContenido((prevData) => ({
        ...prevData,
        [name]: value,
      }));
    }
  };
  const handleTextareaChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };
  const buscarInterlocutor = async (ruc: string) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: response.data.str_Interlocutor,
          str_Correo: response.data.str_Correo,
          str_RepLegal: response.data.str_RepLegal,
          str_TipoDoc: response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio || "",
        }));
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);
      } else {
        setInterlocutorEncontrado(false);
        setFormDataInterlocutor((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_Correo: "",
          str_RepLegal: "",
          str_Domicilio: "",
        }));
        setIdInterlocutor(null);
      }
    } catch (error) {
      setInterlocutorEncontrado(false);
      setFormDataInterlocutor((prevData) => ({
        ...prevData,
        str_Interlocutor: "",
        str_Correo: "",
        str_RepLegal: "",
        str_Domicilio: "",
      }));
      setIdInterlocutor(null);
    }
  };
  const handleSubmitInterlocutores = async () => {
    await validateToken();
    try {
      if (interlocutorEncontrado) {
        const formDataModificado = {
          ...formDataInterlocutor,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificado.int_idUsuarioCreacion;
        const response = await axios.put(
          API_SOLICITANTE["ActualizarInterlocutor"](idInterlocutor),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }
        handleSubmitContenidoSolicitud(idInterlocutor);
      } else {
        const response = await axios.post(
          API_SOLICITANTE["AgregarInterlocutor"](),
          formDataInterlocutor,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }
        handleSubmitContenidoSolicitud(response.data.int_idInterlocutor);
        setIdInterlocutor(response.data.int_idInterlocutor);
        setInterlocutorEncontrado(true);
      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };
  const obtenerGestorConMenorSolicitudes = async () => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["ObtenerGestoresTipoSolicitud"](
          Suscripcion,
          selectedSolicitud.int_idTipoSol,
          selectedEmpresa
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = response.data;

      if (!gestores || gestores.length === 0) {
        return null;
      }

      // Obtener el número de solicitudes activas para cada gestor
      const solicitudesPorGestor = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            const contadorResponse = await axios.get(
              API_SOLICITANTE["ContadorPorGestor"](
                gestor.int_idGestor,
                Suscripcion
              ),
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            const solicitudes_Activas = contadorResponse.data.total_no_firmado;

            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas,
            };
          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idGestor}:`,
              error
            );
            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas: Infinity, // Asignar un valor alto para evitar seleccionarlo
            };
          }
        })
      );

      // Encontrar el gestor con menor número de solicitudes activas
      const gestorConMenorSolicitudes = solicitudesPorGestor.reduce(
        (minGestor, currentGestor) => {
          return currentGestor.solicitudes_Activas <
            minGestor.solicitudes_Activas
            ? currentGestor
            : minGestor;
        },
        { int_idGestor: null, solicitudes_Activas: Infinity }
      );

      return gestorConMenorSolicitudes.int_idGestor;
    } catch (error) {
      return null;
    }
  };
  const handleAsignarGestor = async (gestorSeleccionado) => {
    await validateToken();
    try {
      const response = await axios.put(
        API_SOLICITANTE["AsignarGestor"](),
        {
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
          int_idGestor: gestorSeleccionado,
          int_idUsuarioModificacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        console.error(`Gestor Asignado Correctamente ${response}`);
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }
    } catch (error) {
      return error;
    }
  };
  const EstadoNuevo = async (estado) => {
    await validateToken();
    try {
      await axios.put(
        API_SOLICITANTE["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: estado,
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: selectedSolicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
    }
  };
  const handleSubmitGuardarSolicitud = async () => {
    await validateToken();
    if (!selectedEmpresa || !selectedUnidadesNegocios) {
      Swal.fire(
        "",
        "Debe seleccionar una empresa y una unidad de negocio para registrar",
        "error"
      );
      return;
    }
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 1,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitConfirmarSolicitud = async () => {
    await validateToken();

    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_SolicitudGuardada: 0,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitContenidoSolicitud = async (idInterlocutor) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idInterlocutor: idInterlocutor,
      };
      const response = await axios.put(
        API_SOLICITANTE["ActualizarContenidoSolicitud"](
          selectedSolicitud.int_idSolicitudes
        ),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitFiles = async () => {
    await validateToken();
    try {
      for (const file of newFiles) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          selectedSolicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          selectedSolicitud.int_idSolicitudes
        );
                     // Enviar el tipo_adjunto como campo separado
        if (file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_SOLICITANTE["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }
      }
    } catch (error) {}
  };
  const handleSubmitSolicitudGuardar = async () => {
     
    let success = true;
    let errorMessages = [];

    try {
      await handleSubmitInterlocutores().catch((err) => {
        success = false;
        errorMessages.push("Error al subir interlocutores: " + err.message);
      });

      await handleSubmitGuardarSolicitud().catch((err) => {
        success = false;
        errorMessages.push("Error al confirmar solicitud: " + err.message);
      });

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }

      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Guardada Correctamente..",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOSOLICITANTE));
        await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"), // Unir los mensajes de error
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };
  const handleSubmitSolicitudConfirmar = async () => {
     
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop();
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }

    let success = true;
    let errorMessages = [];

    try {
      await handleSubmitInterlocutores().catch((err) => {
        success = false;
        errorMessages.push("Error al subir interlocutores: " + err.message);
      });

      await handleSubmitConfirmarSolicitud().catch((err) => {
        success = false;
        errorMessages.push("Error al confirmar solicitud: " + err.message);
      });

      const idGestorAsignado = await obtenerGestorConMenorSolicitudes();
      if (!idGestorAsignado) {
        Swal.fire(
          "Error",
          "No hay gestores disponibles para asignar la solicitud",
          "error"
        );
        return;
      }
      await handleAsignarGestor(idGestorAsignado);
      await EstadoNuevo("Asignado");

      if (newFiles.length > 0) {
        await handleSubmitFiles().catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
      }

      if (success) {
        Swal.fire({
          title: "",
          text: "Solicitud Confirmada.",
          icon: "success",
        }).then(() => navigate(RoutesPrivate.INICIOSOLICITANTE));
        await getLogs(JSON.stringify(formDataSolicitud),JSON.stringify(selectedSolicitud),selectedSolicitud.int_idSolicitudes,"Solicitudes","Solicitudes","Editar Solicitud","Contratos","PUT");
      } else {
        Swal.fire({
          title: "Errores encontrados",
          text: errorMessages.join("\n"),
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };
  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Solicitante </h5>
                      </div>
                    </div>
                    <div className="div-input-crear-solicitud">
                      <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Cliente</h5>
                      </div>
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder={`${Nombres} ${Apellidos}`}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo de Documento:</label>
                      <select name="str_TipoDoc" id="tipoDocumento" className="form-select" onChange={handleInputChange} value={formDataInterlocutor.str_TipoDoc || ""} >
                        <option value="Documento de identidad personal">
                          Documento de identidad personal
                        </option>
                        <option value="Documento de identidad de empresa">
                          Documento de identidad de empresa
                        </option>
                        <option value="Pasaporte">Pasaporte</option>
                        <option value="Carnet de Extranjería">Carnet de Extranjería</option>
                      </select>
                    </div>
                   
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <Select
                        options={empresasFiltro}
                        value={empresasFiltro.find(
                          (option) => option.value === selectedEmpresa
                        )}
                        onChange={handleChangeEmpresa}
                        placeholder="Empresa"
                        isDisabled={ver}
                      />
                      {errors.int_idEmpresa && (
                        <span className="error-message">
                          {errors.int_idEmpresa}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">R.U.C:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        name="str_Documento"
                        className="form-control"
                        value={formDataInterlocutor.str_Documento}
                        onChange={(e) => {
                          const maxLength =
                            15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value);
                            }
                          }
                        }}
                        readOnly={ver}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                   
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <Select
                        options={unidadesNegocios}
                        value={unidadesNegocios.find(
                          (option) => option.value === selectedUnidadesNegocios
                        )}
                        onChange={handleChangeUnidadesNegocios}
                        placeholder="Unidad de Negocio"
                        isDisabled={ver}
                      />
                      {errors.int_idUnidadNegocio && (
                        <span className="error-message">
                          {errors.int_idUnidadNegocio}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_Interlocutor && "is-invalid"
                        }`}
                        name="str_Interlocutor"
                        value={formDataInterlocutor.str_Interlocutor}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud"></div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Correo Electrónico:</label>
                      <input
                        type="email"
                        name="str_Correo"
                        className={`form-control ${
                          errors.str_Correo && "is-invalid"
                        }`}
                        placeholder=""
                        value={formDataInterlocutor.str_Correo}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud"></div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Representante:</label>
                      <input
                        type="text"
                        name="str_RepLegal"
                        className={`form-control ${
                          errors.str_RepLegal && "is-invalid"
                        }`}
                        value={formDataInterlocutor.str_RepLegal}
                        placeholder=""
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                   <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud"></div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Domicilio/Dirección:</label>
                      <input
                        type="text"
                        name="str_Domicilio"
                        className={`form-control ${
                          errors.str_Domicilio && "is-invalid"
                        }`}
                        value={formDataInterlocutor.str_Domicilio}
                        placeholder=""
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingTwo">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseTwo"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseTwo"
                  onClick={() => handleStepChange(1)}
                >
                  Detalle del servicio
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseTwo"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingTwo"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                    <label className="form-label">Objeto del contrato</label>
                    <textarea
                      className={`form-control ${
                        errors.str_ObjetivoContrato && "is-invalid"
                      }`}
                      id=""
                      name="str_ObjetivoContrato"
                      rows={3}
                      value={formDataContenido.str_ObjetivoContrato}
                      onChange={handleTextareaChange}
                      readOnly={ver}
                    ></textarea>
                  </div>
                               <div className="div-input-crear-solicitud" style={{flexDirection:"column" , gap:"0.5rem"}}>
                      <label className="form-label">Penalidades:</label>
                      <div className="radio-inputs-crear-solicitud">
                      <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >                            <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="SiPenalidades"
                            value="si"
                            checked={formDataContenido.str_Penalidades === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="SiPenalidades">Sí</label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_Penalidades === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >  
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_Penalidades"
                            id="NoPenalidades"
                            value="no"
                            checked={formDataContenido.str_Penalidades === "no"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="NoPenalidades">No</label>
                        </div>
                      </div>
                       <textarea
                        className="form-control"
                        id=""
                        name="str_DetallePenalidades"
                        rows={3}
                        value={formDataContenido.str_DetallePenalidades}
                        onChange={handleTextareaChange}
                        disabled={
                          formDataContenido.str_Penalidades === "no"  
                        }
                        readOnly={ver}
                      ></textarea>
                     </div>     
 </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Monto del contrato:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${
                          errors.db_Honorarios && "is-invalid"
                        }`}
                        placeholder=""
                        name="db_Honorarios"
                        value={formDataSolicitud.db_Honorarios}
                        onChange={handleInputChange}
                        readOnly={ver}
                        required
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Presupuesto:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${
                          errors.db_Presupuesto && "is-invalid"
                        }`}
                        placeholder=""
                        name="db_Presupuesto"
                        value={formDataContenido.db_Presupuesto}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Plazo de ejecución:</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_PlazoSolicitud && "is-invalid"
                        }`}
                        placeholder=""
                        name="str_PlazoSolicitud"
                        value={formDataContenido.str_PlazoSolicitud}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                         <div className="div-input-crear-solicitud">
                      <label className="form-label">Condición de pago:</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_CondicionPago && "is-invalid"
                        }`}
                        placeholder=""
                        name="str_CondicionPago"
                        onChange={handleInputChange}
                        value={formDataContenido.str_CondicionPago}
                        readOnly={ver}
                      />
                    </div>
                  
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Margen:</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_Margen && "is-invalid"
                        }`}
                        placeholder=""
                        name="str_Margen"
                        value={formDataContenido.str_Margen}
                        onChange={handleInputChange}
                        readOnly={ver}
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Tipo Servicio:</label>
                      <input
                        type="text"
                        className={`form-control ${
                          errors.str_TipoServicio && "is-invalid"
                        }`}
                        placeholder=""
                        name="str_TipoServicio"
                        onChange={handleInputChange}
                        value={formDataContenido.str_TipoServicio}
                        readOnly={ver}
                      />
                    </div>
                  </div>
                     <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Servicio Iniciado:</label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_servicioIniciado === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_servicioIniciado"
                            id="servicioIniciado"
                            value={"si"}
                            checked={formDataContenido.str_servicioIniciado === "si"} 
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="servicioIniciado"
                          >
                            Si
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_servicioIniciado === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          {" "}
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_servicioIniciado"
                            id="noServicioIniciado"
                            value={"no"}
                            checked={formDataContenido.str_servicioIniciado === "no"} 
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="noServicioIniciado"
                          >
                            No
                          </label>
                        </div> 
                      </div>
                    </div>
<div className="div-input-crear-solicitud">
  <label className="form-label">Plazo Forzoso</label>
                            <div className="radio-inputs-crear-solicitud">
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_plazoForzoso === "si"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_plazoForzoso"
                            id="siPlazoForzoso"
                            value="si"
                            checked={
                              formDataContenido.str_plazoForzoso === "si"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                           />
                          <label className="form-check-label" htmlFor="siPlazoForzoso">Sí</label>
                        </div>
                        <div className={`check-group form-check-inline ${
                            formDataContenido.str_plazoForzoso === "no"
                              ? "check-selected"
                              : ""
                          }`}>
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_plazoForzoso"
                            id="NoplazoForzoso"
                            value="no"
                            checked={
                              formDataContenido.str_plazoForzoso === "no"
                            }
                            onChange={handleInputChange}
                            disabled={ver}
                           />
                          <label className="form-check-label" htmlFor="NoplazoForzoso">No</label>
                        </div>
                      </div>
</div>
 
                         <div className="div-input-crear-solicitud">
                         <label className="form-label">Inversión</label>
                         <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-"].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className={`form-control ${
                          errors.db_inversion && "is-invalid"
                        }`}
                        placeholder=""
                        name="db_inversion"
                        value={formDataContenido.str_plazoForzoso === "no" ? "" : formDataContenido.db_inversion || ""}
                        onChange={handleInputChange}
                        disabled={formDataContenido.str_plazoForzoso === "no" || ver}
                         
                      /></div>
                       
                        
                  </div>
                  <div className="inputs-crear-solicitud">
                          <div className="div-input-crear-solicitud">
                      <label className="form-label">Moneda:</label>
                      <div className="radio-inputs-crear-solicitud">
                        {TiposMoneda.str_Moneda !== "Dolares" && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "dolares" ? "check-selected" : ""
                            }`}
                          >
                            <input
                              className="form-check-input"
                              type="radio"
                              name="inlineRadioOptions"
                              id="monedaDolares"
                              value="dolares"
                              checked={selectedTipoMoneda === "dolares"}
                              onChange={handleChangeTipoMoneda}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="monedaDolares"
                            >
                              <IconoDolares size={"1.5rem"} color={"#156CFF"} />{" "}
                              Dólares
                            </label>
                          </div>
                        )}

                        {Object.keys(TiposMoneda).length > 0 && (
                          <div
                            className={`check-group form-check-inline ${
                              selectedTipoMoneda === "empresa" ? "check-selected" : ""
                            }`}
                          >
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoMoneda"
                              id="tipoMoneda"
                              value={"empresa"}
                              checked={selectedTipoMoneda === "empresa"}
                              onChange={handleChangeTipoMoneda}
                            />
                            <label
                              className="form-check-label"
                              htmlFor="tipoMoneda"
                            >
                              <IconoMoneda size={"1.5rem"} color={"#156CFF"} />{" "}
                              {TiposMoneda.str_Moneda}
                            </label>
                          </div>
                        )}
                      </div>
                    </div>
                           <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Acuerdo de Nivel de Servicio
                      </label>
                      <div className="radio-inputs-crear-solicitud">
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ans === "si"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ans"
                            id="sians"
                            value="si"
                            checked={formDataContenido.str_ans === "si"}
                            onChange={handleInputChange}
                            disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="sians">
                            Sí
                          </label>
                        </div>
                        <div
                          className={`check-group form-check-inline ${
                            formDataContenido.str_ans === "no"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="str_ans"
                            id="Noans"
                            value="no"
                            checked={formDataContenido.str_ans === "no"}
                            onChange={handleInputChange}
                             disabled={ver}
                          />
                          <label className="form-check-label" htmlFor="Noans">
                            No
                          </label>
                        </div>
                      </div>
                    </div>
                    </div>

                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font${"collapsed"}`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(2)}
                >
                  Información adicional
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="text-area-crear-solicitud">
                    <label className="form-label">Condiciones del Servicio</label>
                    <textarea
                      className="form-control"
                      name="str_InfoAdicional"
                      id="exampleFormControlTextarea1"
                      rows={3}
                      value={formDataContenido.str_InfoAdicional}
                      onChange={handleTextareaChange}
                      readOnly={ver}
                    ></textarea>
                    {errors.str_InfoAdicional && (
                      <span className="error-message">
                        {errors.str_InfoAdicional}
                      </span>
                    )}
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Fecha Esperada de entrega:{" "}
                      </label>
                      <input
                        type="date"
                        onKeyDown={(e) => e.preventDefault()}
                        min={FechaMinima}
                        className="form-control"
                        value={
                          formDataSolicitud.dt_FechaEsperada
                            ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                            : ""
                        }
                        onChange={handleDateChange}
                        name="dt_FechaEsperada"
                      />
                      {errors.dt_FechaEsperada && (
                        <span className="error-message">
                          {errors.dt_FechaEsperada}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearSolicitud
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudGuardar={handleSubmitSolicitudGuardar}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        esEdicion={esEdicion}
        newFiles={newFiles}
        ver={ver}
             NomTipoSolicitud={NomTipoSolicitud}
      />
    </div>
  );
};

export default EditarPrestacionServicio;
