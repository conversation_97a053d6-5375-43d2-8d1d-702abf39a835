import React, { useState } from "react";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { TextField } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import Swal from "sweetalert2";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";

interface ModalTagsProps {
    isModalVisiblContratoFirmado: boolean;
    CerrarModalContratoFirmado: () => void;
    file: File;
    handleSubmitFiles: () => void;
    idUsuario: number;
    Suscripcion: string;
    idSolicitud: number;
    SubmitObtenerDatos: () => void;
    setSelectedSolicitud: (solicitud: object) => void;
  }

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
const ModalDocumentoFirmado: React.FC<ModalTagsProps> = ({
  isModalVisiblContratoFirmado,
  CerrarModalContratoFirmado,
  file,
  handleSubmitFiles,
  idUsuario,
  Suscripcion,
  idSolicitud,
  SubmitObtenerDatos,
  setSelectedSolicitud
}) => {
  const [fechaFirmado, setFechaFirmado] = useState(dayjs());
  const [horasTrabajadas, setHorasTrabajadas] = useState<number>(0);
  const token = Cookies.get("Token");

  const handleDateChange = (newValue: dayjs.Dayjs | null) => {
    setFechaFirmado(newValue);
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setHorasTrabajadas(parseInt(value));
  };
  const handleEnviarFirma = async () => {
await validateToken();
    try {
        const formattedDate = fechaFirmado.format("YYYY-MM-DD HH:mm:ss");
      const response = await axios.put(API_GESTOR["EnviarDocumentoFirmado"](), {
        int_idSolicitudes: idSolicitud,
        dt_FirmaContrato: formattedDate,
        int_HorasTrabajadas: horasTrabajadas,
        int_idUsuarioModificacion: idUsuario,
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        try {
            handleSubmitFiles()
            
          const respuestaActualizarEstado = await axios.put(
            API_GESTOR["ActualizarEstado"](),
            {
              str_idSuscriptor: Suscripcion,
              nombre_estado: "Firmado",
              int_idUsuarioCreacion: idUsuario,
              int_idSolicitudes: idSolicitud,
            },{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            }
          );
          if(respuestaActualizarEstado.status >= 200 && respuestaActualizarEstado.status < 300){
            Swal.fire("", "El Documento se subió correctamente", "success");
            CerrarModalContratoFirmado();
          SubmitObtenerDatos();
          setSelectedSolicitud({});
          }
        } catch {
          console.error(`Error para cambiar el estado ${response.status}`);
        }
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }

      
    } catch (error) {
      console.error("Error al asignar el gestor:", error);
    }
  };
  return (
    <>
      {isModalVisiblContratoFirmado && (
        <div className="modal-tags">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarModalContratoFirmado}
            ></button>
          </div>
          <div className="modalDocFirmado lato-font">
            {file ?<span>
              Se está subiendo el documento <br />
              <span className="nombre_archivo">{file.name}</span>{" "}
            </span> : 
            ""

            }
            
          </div>
          <div className="input-fecha-firmado">
            <label className="form-label">Fecha Esperada de entrega: </label>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                value={fechaFirmado}
                onChange={handleDateChange}
                renderInput={(params) => <TextField {...params} />}
              />
            </LocalizationProvider>
          </div>
          <div className="input-fecha-firmado">
            <label className="form-label">Horas Trabajadas: </label>
            <input
              type="text"
              className="form-control"
              placeholder={horasTrabajadas}
              onChange={handleInputChange}
              required
            />
          </div>
          <div className="botones-modal-solicitante">
            <button
              className="btn btn-outline-primary"
              onClick={CerrarModalContratoFirmado}
            >
              Cancelar
            </button>
            <button className="btn btn-primary" onClick={handleEnviarFirma}>Guardar</button>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalDocumentoFirmado;
