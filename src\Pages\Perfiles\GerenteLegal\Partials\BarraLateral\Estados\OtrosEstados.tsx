import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import GestorAsignado from "../../../../Gestor/Partials/GestorAsignado";
import GestorAsignadoSolicitante from "../../../../Gestor/Partials/GestorAsignadoSolicitante";
import { useEffect,useState } from "react";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import axios from "axios";
import Cookies from "js-cookie";
import { validateToken } from "../../../../../Components/Services/TokenService";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
const OtrosEstados = ({ solicitudSeleccionada, historiales, aprobadores,Suscripcion,Suscriptor, idUsuario,idAplicacion}) => {
     const navigate = useNavigate();
     const token = Cookies.get("Token");
     const [error, setError] = useState<string | null>(null);
     const [solicitud, setSolicitud] = useState(null);
     
     useEffect(() => {
      const obtenerGestores = async () => {
        await validateToken();
        try {
          const response = await axios.get(
            API_SOLICITANTE["ObtenerSolicitud"](solicitudSeleccionada.int_idSolicitudes),{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            }
          );
          setSolicitud(response.data);
        } catch (error) {
          console.error("Error al obtener tipos de solicitud:", error);
          setError("No se pudo obtener el gestor asignado.");
          setSolicitud(null);

        } 
      };
  
      if (solicitudSeleccionada.int_idGestor) {
        obtenerGestores();
      }
    }, [solicitudSeleccionada]);
     const handleEditarSolicitud = () =>{
          navigate(RoutesPrivate.VERSOLICITUD, {state:{solicitudSeleccionada,Suscripcion,idAplicacion,idUsuario,Suscriptor, ver:true}})
        }
  return (
    <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        {solicitudSeleccionada.str_CodSolicitudes.includes("EJ") || solicitudSeleccionada.str_CodSolicitudes.includes("CH") ? (
          ""
        ) : (
        <Tooltip title="Ver Solicitud" placement="top">
    
        <div
              onClick={handleEditarSolicitud}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
        </Tooltip>
        )}
      </div>
      <GestorAsignadoSolicitante solicitud={solicitud} />
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores} />
    </div>
  );
};

export default OtrosEstados;


