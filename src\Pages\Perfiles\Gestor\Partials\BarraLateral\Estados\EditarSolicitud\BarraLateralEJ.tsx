import axios from 'axios';
import { useRef, useState } from 'react'

const BarraLateralCrearEJ = ({ 
  files, 
  handleFileRemove, 
  handleFileChange,
  handleFileRemoveEJ,
  handleFileChangeEJ,
  filesEJ,
  handleSubmitSolicitudConfirmar
}) => {
  const [isConfirmando, setIsConfirmando] = useState(false);
  const fileInputRef = useRef(null);
  const fileInputRef2 = useRef(null);

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleButtonClick2 = () => {
    fileInputRef2.current.click();
  };

  const confirmar = async () => {
    try {
      setIsConfirmando(true);
      await handleSubmitSolicitudConfirmar();
    } catch (error) {
      console.error("Error al confirmar:", error);
    } finally {
      setIsConfirmando(false);
    }
  };

  return (
    <div className="barraLateral-crear-solicitud">
      <div className="superior-barraLateral-crear-solicitud">
        <span className="titulo-barra-Lateral-CS lato-font-400">
          Documentos Adjuntos
        </span>
        <div className="div-subirArchivos-barraLateral">
          <input
            ref={fileInputRef}
            className="form-control"
            type="file"
            style={{display:'none'}}
            onChange={handleFileChange}
          />
          <button
            className="btn btn-subirArchivos"
            type="button"
            onClick={handleButtonClick}
            disabled={isConfirmando}
          >
            <i className="fa-solid fa-upload"></i>Subir Archivos
          </button>
          <div>
            <ul>
              {files.map((file, index) => (
                <div className="listado-archivos" key={index}>
                  <li className={""}>
                    <i className="fa-solid fa-caret-right iconolistar"></i>
                    {file.name || file.nombre_archivo}
                  </li>
                  <i
                    className="fa-solid fa-trash iconoborrar"
                    onClick={() => handleFileRemove(index)}
                  ></i>
                </div>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="superior-barraLateral-crear-solicitud">
        <span className="titulo-barra-Lateral-CS lato-font-400">
          Documento Final
        </span>
        <div className="div-subirArchivos-barraLateral">
          <input
            ref={fileInputRef2}
            className="form-control"
            type="file"
            style={{display:'none'}}
            onChange={handleFileChangeEJ}
            accept=".doc,.docx"
          />
          <button
            className="btn btn-subirArchivos"
            type="button"
            onClick={handleButtonClick2}
            disabled={isConfirmando}
          >
            <i className="fa-solid fa-upload"></i>Subir Archivos
          </button>
          <div>
            <ul>
              {filesEJ.map((file, index) => (
                <div className="listado-archivos" key={index}>
                  <li className={""}>
                    <i className="fa-solid fa-caret-right iconolistar"></i>
                    {file.name || file.nombre_archivo}
                  </li>
                  <i
                    className="fa-solid fa-trash iconoborrar"
                    onClick={() => handleFileRemoveEJ(index)}
                  ></i>
                </div>
              ))}
            </ul>
          </div>
        </div>
      </div>
      <div className="botones-crear-solicitud">
        <button
          className="btn btn-primary"
          onClick={confirmar}
          disabled={isConfirmando}
        >
          {isConfirmando ? "Confirmando Acuerdo..." : "Confirmar Acuerdo"}
        </button>
      </div>
    </div>
  );
}

export default BarraLateralCrearEJ
