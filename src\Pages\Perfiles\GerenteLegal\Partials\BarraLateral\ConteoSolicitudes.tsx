import { useEffect, useState } from 'react';
import API_GESTOR from '../../../../../assets/Api/ApisGestor';
import axios from 'axios';
import IconoRight from '../../../../../assets/SVG/IconoRight';
import { RoutesPrivate } from '../../../../../Security/Routes/ProtectedRoute';
import { useNavigate } from 'react-router-dom';
import { decrypt, validateToken } from '../../../../Components/Services/TokenService';
import Cookies from "js-cookie";

const ConteoSolicitudes = ({EstadisticasInicio}) => {
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const [solicitudes, setSolicitudes] = useState([]);
  const [procesos, setProcesos] = useState([]);

  const token = Cookies.get("Token");

  // const [estadisticas, setEstadisticas] = useState([]);
  
  // const estadosPermitidos = ["Aceptado", "Aprobado","Asignado", "En Proceso", "En Aprobación", "En Validación", "Firmado", perfil === "Gestor Controller" ? "Nuevo" : null ];

  // const ConteoSolicitudesGestor = async () => {
  //   try {
  //     const response = await axios.get(API_GESTOR["SolicitudesGestor"](idUsuario,Suscripcion));
      
  //     const estadosFiltrados = response.data
  //       .filter((estado:string) => estadosPermitidos.includes(estado.nombre_estado))
  //       ; 
  //     setEstadisticas(estadosFiltrados);
  //   } catch (error) {
  //     console.error("Error al obtener las estadísticas", error);
  //   }
  // };

  // useEffect(() => {
  //   ConteoSolicitudesGestor();
  // }, [idUsuario]);
  const navigate = useNavigate();
  useEffect(() => {
    const obtenerSolicitudesUsuario = async () => {
await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerSolicitudesGerenteLegal"](Suscripcion),{
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            }
          }
        );
        setSolicitudes(response.data);

      } catch (error: any) {
        console.error("Error al obtener datos:", error.message);
      }
    };
    const obtenerProcesosUsuario = async () => {
      try {
        const response = await axios.get(API_GESTOR["ObtenerProcesosAdmin"](Suscripcion)
            ,{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            });
            setProcesos(response.data);
            
        if (response.data) {
          
        }
      } catch (error) {
        console.error("Error al obtener datos:", error.message);
        setProcesos([])
      }
    };
    if(!EstadisticasInicio){
      obtenerProcesosUsuario();
    }
    obtenerSolicitudesUsuario();
  }, []);
  const nuevoCount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Nuevo").length;
  const asignadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Asignado").length;
  const enprocesocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Proceso").length;
  const envalidacioncount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Validación").length;
  const FirmadoCount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Firmado").length;
  const Aceptadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aceptado").length;
  const EnAprobacioncount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Aprobación").length;
  const Aprobadocount = solicitudes.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aprobado").length;
  let ProcesosCerrados = 0;
  let ProcesosEnProceso = 0;

  let procesosNuevos = 0;
  if(!EstadisticasInicio){
    ProcesosCerrados = procesos.filter((item: { str_Nombre: string; }) => item.int_idEstado.str_Nombre === "Cerrado").length;
    procesosNuevos = procesos.filter((item: { bool_esGuardado: boolean; }) => item.bool_esGuardado === true).length;
    ProcesosEnProceso = procesos.filter((item: { bool_esGuardado: boolean;  str_Nombre: string; }) => item.bool_esGuardado === false && item.int_idEstado.str_Nombre === "En Proceso").length;

  }
  const hoy = new Date();
  hoy.setHours(0, 0, 0, 0);
  
  const SolicitudesRetrasadas = solicitudes.filter(
    (item: { estado_nombre: string; dt_FechaEsperada: string }) => 
      item.estado_nombre === "Asignado" && new Date(item.dt_FechaEsperada) < hoy
  ).length;

  return (
    <>
    {EstadisticasInicio ? 
        <div className="contador-estados-inicio-solicitante montserrat-font">
  
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Asignadas'> </span>
          <div className='titulo-conteo'>Asignadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{asignadocount}</div>
        
        </div>
        
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>En Proceso</div> 
          <div  className="valor-contador-estados-IS montserrat-font-500">{enprocesocount}</div>
          <span className='color-estado-IS-EnProceso'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Aceptadas'> </span>
          <div className='titulo-conteo'>Aceptadas</div> 
          <div  className="valor-contador-estados-IS montserrat-font-500">{Aceptadocount}</div>
        
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>En Validación</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{envalidacioncount}</div>
          <span className='color-estado-IS-EnValidacion'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-EnAprobacion'> </span>
          <div className='titulo-conteo'>En Aprobación</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{EnAprobacioncount}</div>
          
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Aprobadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{Aprobadocount}</div>
          <span className='color-estado-IS-Aprobadas'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
        <span className='color-estado-IS-Firmadas'> </span>
          <div className='titulo-conteo'>Firmadas</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{FirmadoCount}</div>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Atrasadas</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{SolicitudesRetrasadas}</div>
          <span className='color-estado-IS-Retrasadas'> </span>

        </div>
        {EstadisticasInicio && 
        <div className="estado-flecha-estadisticas" onClick={() => navigate(RoutesPrivate.REPORTESGESTOR, { state: { idUsuario, Suscripcion } })}>
              <IconoRight size={"2rem"} selected={true} colorselected={"#156CFF"} color={"#156CFF"} />
            </div>}
        
      </div>:
          <div className="contador-estados-inicio-solicitante montserrat-font" style={{margin: "auto"}}>
  
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <span className='color-estado-IS-Asignadas'> </span>
            <div className='titulo-conteo'>Asignadas</div> 
            <div className="valor-contador-estados-IS montserrat-font-500">{asignadocount}</div>
          
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
            <div className='titulo-conteo'>En Proceso</div> 
            <div  className="valor-contador-estados-IS montserrat-font-500">{enprocesocount}</div>
            <span className='color-estado-IS-EnProceso'> </span>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <span className='color-estado-IS-Aceptadas'> </span>
            <div className='titulo-conteo'>Aceptados</div> 
            <div  className="valor-contador-estados-IS montserrat-font-500">{Aceptadocount}</div>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
            <div className='titulo-conteo'>Aprobadas</div> 
            <div className="valor-contador-estados-IS montserrat-font-500">{Aprobadocount}</div>
            <span className='color-estado-IS-Aprobadas'> </span>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <span className='color-estado-IS-Firmadas'> </span>
            <div className='titulo-conteo'>Firmadas</div>
            <div className="valor-contador-estados-IS montserrat-font-500">{FirmadoCount}</div>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
            <div className='titulo-conteo'>Procesos Nuevos</div>
            <div className="valor-contador-estados-IS montserrat-font-500">{procesosNuevos}</div>
            <span className='color-estado-IS-Nuevas'> </span>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <span className='color-estado-IS-EnProceso'> </span>
            <div className='titulo-conteo'>Procesos Nuevos</div>
            <div className="valor-contador-estados-IS montserrat-font-500">{ProcesosEnProceso}</div>
          </div>
          <div className="estado-contador-IS"  style={{borderRight: "1px solid #C4C4C4"}}>
            <div className='titulo-conteo'>Procesos Cerrados</div>
            <div className="valor-contador-estados-IS montserrat-font-500">{ProcesosCerrados}</div>
            <span className='color-estado-IS-Firmadas'> </span>
          </div>
          <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <span className='color-estado-IS-Retrasadas'> </span>
          <div className='titulo-conteo'>Atrasadas</div>
          <div className="valor-contador-estados-IS montserrat-font-500">{SolicitudesRetrasadas}</div>

        </div>
          
        </div>}</>
  );
};

export default ConteoSolicitudes;