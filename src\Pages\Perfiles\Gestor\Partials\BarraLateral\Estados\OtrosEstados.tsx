import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";
import GestorAsignado from "../../GestorAsignado";
import SolicitanteAsignado from "../SolicitanteAsignado";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import { decrypt } from "../../../../../Components/Services/TokenService";
import Cookies from "js-cookie";

const OtrosEstados = ({
  solicitudSeleccionada,
  historiales,
  aprobadores,
  Suscripcion,
  idAplicacion,
  idUsuario,
}) => {
    const navigate = useNavigate();
      const Suscriptor = decrypt(Cookies.get("suscriptor"));

    const handleVerSolicitud = () => {
      navigate(RoutesPrivate.EDITARSOLICITUD, {
        state: {
          solicitudSeleccionada,
          Suscripcion,
          idAplicacion,
          idUsuario,
          Suscriptor,
          ver: true,
        },
      });
    };
return(
     <div className={`conteo-inicio-gestor-pageSolicitudes`}>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
             <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
      </div>
<SolicitanteAsignado solicitudSeleccionada={solicitudSeleccionada} />
<HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>
     </div>
)};

export default OtrosEstados;
