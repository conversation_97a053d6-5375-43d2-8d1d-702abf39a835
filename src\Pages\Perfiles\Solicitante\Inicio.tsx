/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import Header from "../../Components/Partials/Header/Header";
import Titulo from "../../Components/Partials/Seccion/Titulo";
import Cookies from "js-cookie";
import Select, { SingleValue } from "react-select";
import "./Inicio.css";
import "./InicioResponsive.css";
import axios from "axios";
import CircleEstado from "../../../assets/SVG/CircleEstado";
import API_SOLICITANTE from "../../../assets/Api/ApisSolicitante";
import TagInput from "./Partials/Filtro/TagInput";
import ResumenConteo from "./Partials/BarraLateral/ResumenConteo";
import EstadoNuevo from "./Partials/BarraLateral/EstadoNuevo";
import EstadoValidacion from "./Partials/BarraLateral/EstadoValidacion";
import OtrosEstados from "./Partials/BarraLateral/OtrosEstados";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { Tooltip } from "@mui/material";
import { RoutesPrivate } from "../../../Security/Routes/ProtectedRoute";
import { decrypt, validateToken } from "../../Components/Services/TokenService";
import BarraLimpia from "./Partials/BarraLateral/BarraLimpia";
import EstadoFirmado from "./Partials/BarraLateral/EstadoFirmado";
import IconoLeft from "../../../assets/SVG/IconoLeft";
import IconoRight from "../../../assets/SVG/IconoRight";
import getLogs from "../../Components/Services/LogsService";
import API_GESTOR from "../../../assets/Api/ApisGestor";

type Solicitud = {
  int_idSolicitudes: number;
  str_CodSolicitudes: string;
  str_DeTerceros: string;
  dt_FechaRegistro: string;
  dt_FechaEsperada: string;
  str_NombreEmpresa: string;
  db_Honorarios: number;
  estado_nombre: string;
};

type OptionType = {
  value: string;
  label: string;
  codigo?: string;
  id?: number;
};
const fechaHoy = new Date().toISOString().split("T")[0];
const fechaAyer = new Date(new Date().setDate(new Date().getDate() - 1))
  .toISOString()
  .split("T")[0];
const fechaMañana = new Date(new Date().setDate(new Date().getDate() + 1))
  .toISOString()
  .split("T")[0];
console.log(fechaAyer);
console.log(fechaHoy);

function formatDate(dateString: string) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
}
const Inicio = () => {
  const [anioSeleccionado, setAnioSeleccionado] = useState<string>("");
  const [mesSeleccionado, setMesSeleccionado] = useState<string>("");
  const [selectedSolicitud, setSelectedSolicitud] = useState<Solicitud | null>(
    null
  );
  const [solicitudes, setSolicitudes] = useState<Solicitud[]>([]);
  const [HistorialesUsuario, setHistorialesUsuario] = useState<any[]>([]);
  const [tipoSolicitudes, setTipoSolicitudes] = useState<OptionType[]>([]);
  const [selectedTipoModelo, setSelectedTipoModelo] =
    useState<string>("Propio");
  const [selectedTipo, setSelectedTipo] =
    useState<SingleValue<OptionType> | null>(null);
  const [selectedEmpresaPlantilla, setSelectedEmpresaPlantilla] = useState("");
  const [selectedTipoSolicitudFiltro, setSelectedTipoSolicitudFiltro] =
    useState<string>("");
  const [estadosFiltro, setEstadosFiltro] = useState<OptionType[]>([]);
  const [selectedEstadoFiltro, setSelectedEstadoFiltro] = useState<string>("");
  const [empresasFiltro, setEmpresasFiltro] = useState<OptionType[]>([]);
  const [selectedEmpresaFiltro, setSelectedEmpresaFiltro] =
    useState<string>("");
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [mostrarArchivadas, setMostrarArchivadas] = useState<boolean>(false);
  const [tags, setTags] = useState<string[]>([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isFilter, setIsFilter] = useState(false);
  const [aprobadores, setAprobadores] = useState<any[]>([]);
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const Suscriptor = decrypt(Cookies.get("suscriptor"));
  const idAplicacion = decrypt(Cookies.get("idAplicacion"));
  const navigate = useNavigate();
  const token = Cookies.get("Token");
  const [tipoCreacionSolicitud, setTipoCreacionSolicitud] =
    useState<string>("solicitud");
  const [codigoAdenda, setCodigoAdenda] = useState<string>("");
  const [codSolicitudFiltro, setCodSolicitudFiltro] = useState("");
  const [archivoSolicitudes, setArchivosSolicitudes] = useState(null);
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const [documentoCliente, setDocumentoCliente] = useState("");
  const [razonSocial, setRazonSocial] = useState("");
  const [tipoAdenda, setTipoAdenda] = useState("CC");
  const [empresaSeleccionadaAdenda, setEmpresaSeleccionadaAdenda] =
    useState("");
  console.log(empresaSeleccionadaAdenda);
  const [unidadNegocioAdenda, setUnidadNegocioAdenda] = useState("");
  /*PAGINACION*/
  const [currentPage, setCurrentPage] = useState(1);
  const solicitudesPorPagina = 8;
  const indiceUltimaSolicitud = currentPage * solicitudesPorPagina;
  const indicePrimeraSolicitud = indiceUltimaSolicitud - solicitudesPorPagina;
  const solicitudesActuales = solicitudes.slice(
    indicePrimeraSolicitud,
    indiceUltimaSolicitud
  );
  const [unidadesNegocios, setUnidadesNegocios] = useState([]);
  const handlePageChange = (pageNumber: React.SetStateAction<number>) => {
    setCurrentPage(pageNumber);
  };
  const totalPaginas = Math.ceil(solicitudes.length / solicitudesPorPagina);

  const renderNumeritosPaginacion = () => {
    const numeritos = [];
    for (let i = 1; i <= totalPaginas; i++) {
      numeritos.push(
        <button
          key={i}
          className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }
    return numeritos;
  };

  const anios = Array.from({ length: 21 }, (_, i) => 2010 + i);
  const meses = [
    { value: "01", label: "Enero" },
    { value: "02", label: "Febrero" },
    { value: "03", label: "Marzo" },
    { value: "04", label: "Abril" },
    { value: "05", label: "Mayo" },
    { value: "06", label: "Junio" },
    { value: "07", label: "Julio" },
    { value: "08", label: "Agosto" },
    { value: "09", label: "Septiembre" },
    { value: "10", label: "Octubre" },
    { value: "11", label: "Noviembre" },
    { value: "12", label: "Diciembre" },
  ];
  const obtenerSolicitudesUsuario = async () => {
    try {
      await validateToken();
      const tagsParams = tags.map((tag) => `&str_descripcion=${tag}`).join("");

      const response = await axios.get(
        `${baseUrl}api/solicitudes/filtrar-solicitante/?str_idSuscriptor=${Suscripcion}&int_idSolicitante=${idUsuario}${
          mostrarArchivadas ? "" : "&str_Visible=si"
        }${
          selectedTipoSolicitudFiltro
            ? `&str_NombreTipoSolicitud=${selectedTipoSolicitudFiltro}`
            : ""
        }${selectedEstadoFiltro ? `&estado=${selectedEstadoFiltro}` : ""}${
          anioSeleccionado ? `&anio_fecha_registro=${anioSeleccionado}` : ""
        }${
          mesSeleccionado ? `&mes_fecha_registro=${mesSeleccionado}` : ""
        }${tagsParams}${
          selectedEmpresaFiltro ? `&str_idEmpresa=${selectedEmpresaFiltro}` : ""
        }${
          codSolicitudFiltro ? `&str_CodSolicitudes=${codSolicitudFiltro}` : ""
        }${
          documentoCliente ? `&str_documentoCliente=${documentoCliente}` : ""
        }${razonSocial ? `&str_razonSocial=${razonSocial}` : ""}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setSolicitudes(response.data);
      await getLogs(
        null,
        null,
        null,
        "Listado Solicitudes",
        "Solicitudes",
        "Ver Solicitudes",
        "Contratos",
        "GET"
      );
    } catch (error: any) {
      console.error("Error al obtener datos:", error.message);
    }
  };

  useEffect(() => {
    const fetchTipoSolicitudes = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerTipoSolicitud"](Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data
          .map(
            (tipo: {
              int_idTipoSolicitud: any;
              int_Nombre: any;
              str_CodTipoSol: any;
            }) => ({
              value: tipo.int_Nombre,
              label: tipo.int_Nombre,
              codigo: tipo.str_CodTipoSol,
              id: tipo.int_idTipoSolicitud,
            })
          )
          .filter((opcion) => opcion.label.trim() !== "Otro Contrato");
        setTipoSolicitudes(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchEstadosFiltro = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEstadosSolicitud"](Suscripcion),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEstado: any; int_Nombre: any }) => ({
            value: tipo.int_Nombre,
            label: tipo.int_Nombre,
          })
        );
        setEstadosFiltro(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchEmpresasFiltro = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerEmpresas"](idAplicacion || "", Suscriptor),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
          })
        );
        setEmpresasFiltro(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    fetchEstadosFiltro();
    obtenerSolicitudesUsuario();
    fetchTipoSolicitudes();
    fetchEmpresasFiltro();
  }, [isFilter]);
  useEffect(() => {
    const fetchUnidadesNegocios = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerUnidadesNegocios"](
            Suscripcion,
            empresaSeleccionadaAdenda.value
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const opciones = response.data.map(
          (tipo: { int_idUnidadesNegocio: any; str_Descripcion: any }) => ({
            value: tipo.int_idUnidadesNegocio,
            label: tipo.str_Descripcion,
          })
        );
        setUnidadesNegocios(opciones);
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    fetchUnidadesNegocios();
  }, [empresaSeleccionadaAdenda]);
  const handleFiltrar = () => {
    setIsFilter(true);
    obtenerSolicitudesUsuario();
    handlePageChange(1);
    CerrarFiltro();
    renderNumeritosPaginacion();
  };
  const handleChangeTipoSolicitud = (selectedOption: OptionType | null) => {
    setSelectedTipo(selectedOption);
  };
  const handleChangeEmpresaPlantilla = (selectedOption: OptionType | null) => {
    if (selectedOption) {
      setSelectedEmpresaPlantilla(selectedOption.value);
    }
  };
  const handleChangeTipoSolicitudFiltro = (
    selectedOption: OptionType | null
  ) => {
    if (selectedOption) {
      setSelectedTipoSolicitudFiltro(selectedOption.value);
    }
  };
  const handleChangeestadoFiltro = (selectedOption: OptionType | null) => {
    if (selectedOption) {
      setSelectedEstadoFiltro(selectedOption.value);
    }
  };
  const handleChangeEmpresaFiltro = (selectedOption: OptionType | null) => {
    if (selectedOption) {
      setSelectedEmpresaFiltro(selectedOption.value);
    }
  };
  const handleChangeEmpresaAdenda = (selectedOption: OptionType | null) => {
    if (selectedOption) {
      setEmpresaSeleccionadaAdenda(selectedOption);
    }
  };
  const handleChangeUnidadNegocioAdenda = (
    selectedOption: OptionType | null
  ) => {
    if (selectedOption) {
      setUnidadNegocioAdenda(selectedOption);
    }
  };
  const handleChangeTipoModelo = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedTipoModelo(event.target.value);
  };
  const handleChangeTipoAdenda = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setTipoAdenda(event.target.value);
  };
  const handleChangeMostrarArchivados = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setMostrarArchivadas(event.target.checked);
  };
  const AbrirFiltro = () => {
    setIsModalVisible(!isModalVisible);
  };
  const CerrarFiltro = () => {
    setIsModalVisible(false);
  };
  const SubmitObtenerDatos = () => {
    obtenerSolicitudesUsuario();
  };
  const handleAnioChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setAnioSeleccionado(e.target.value);
  };

  const handleMesChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setMesSeleccionado(e.target.value);
  };
  const HistorialUsuario = async (idSolicitud: number) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["HistorialUsuario"](Suscripcion, idSolicitud),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = response.data;

      setHistorialesUsuario(data);
    } catch (error) {
      console.error("Error al obtener el historial del usuario:", error);
    }
  };
  const obtenerAprobadores = async () => {
    await validateToken();
    if (selectedSolicitud) {
      try {
        const response = await axios.get(
          API_SOLICITANTE["ListarAprobadores"](
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const aprobadores = response.data;
        setAprobadores(aprobadores);
        console.log("Aprobadores obtenidos:", aprobadores);
      } catch (error) {
        console.error("Error al obtener aprobadores:", error);
      }
    }
  };
  useEffect(() => {
    obtenerAprobadores();
  }, [selectedSolicitud]);
  const CambiarBarraLateral = (solicitud: Solicitud) => {
    setSelectedSolicitud(solicitud);
    HistorialUsuario(solicitud.int_idSolicitudes);
  };
  const BorrarTodosFiltros = () => {
    setIsFilter(false);
    setMostrarArchivadas(false);
    setSelectedTipoSolicitudFiltro("");
    setSelectedEstadoFiltro("");
    setSelectedEmpresaFiltro("");
    setMesSeleccionado("");
    setAnioSeleccionado("");
    setCodSolicitudFiltro("");
    setDocumentoCliente("");
    setRazonSocial("");
    setTags([]);
    setIsModalVisible(false);
    obtenerSolicitudesUsuario();
  };

  // const handleImportarData = async () => {
  //   if (!archivoSolicitudes) {
  //     Swal.fire("Error", "Seleccione un archivo", "error");
  //     return;
  //   }
  //   const formData = new FormData();
  //   formData.append("file", archivoSolicitudes);
  //   formData.append("str_idSuscriptor", Suscripcion);
  //   formData.append("int_idUsuario", idUsuario);

  //   try {
  //     const response = await axios.post(
  //       API_SOLICITANTE["ImportarData"](),
  //       formData,
  //       {
  //         headers: {
  //           "Content-Type": "multipart/form-data",
  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );

  //     obtenerSolicitudesUsuario();
  //     setArchivosSolicitudes(null);

  //     Swal.fire("Exito", "Archivo importado correctamente", "success");
  //   } catch (error) {
  //     console.error("Error al importar archivo:", error);
  //     Swal.fire("Error", "Error al importar archivo", "error");
  //   }
  // };
  const handleDownload = async () => {
    await validateToken();
    if (!selectedTipo) {
      Swal.fire(
        "Error",
        "Seleccione el tipo de modelo que quiere crear",
        "error"
      );
      return;
    }
    if (!selectedEmpresaPlantilla) {
      Swal.fire(
        "Error",
        "Seleccione la empresa a la que pertenece la plantilla",
        "error"
      );
      return;
    }
    try {
      const response = await axios.get(
        API_GESTOR["ListarPlantilla"](
          Suscripcion,
          selectedTipo?.id,
          selectedEmpresaPlantilla,
          "Contratos"
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.data) {
        const response2 = await axios.get(
          API_GESTOR["DescargarPlantilla"](
            Suscripcion,
            selectedTipo?.id,
            selectedEmpresaPlantilla,
            "Contratos"
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            responseType: "blob",
          }
        );
        const contentDisposition = response2.headers["content-disposition"];
        const filename = contentDisposition
          ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
          : `${response.data.nombre_archivo}`;

        const url = window.URL.createObjectURL(new Blob([response2.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
  const CrearSolicitud = () => {
    if (!selectedTipo) {
      Swal.fire(
        "Error",
        "Seleccione el tipo de modelo que quiere crear",
        "error"
      );
      return;
    }
    navigate(RoutesPrivate.GESTIONSOLICITUD, {
      state: {
        selectedTipo,
        esEditar: false,
        selectedTipoModelo,
        idAplicacion,
        Suscriptor,
        Suscripcion,
      },
    });
  };
  const CrearAdenda = async () => {
    try {
      if (tipoAdenda === "CC") {
        if (!codigoAdenda) {
          Swal.fire(
            "",
            "Debe ingresar el código de solicitud para asociar la adenda",
            "error"
          );
          return;
        }
        const response = await axios.get(
          `${baseUrl}api/solicitudes/buscarxcodigo/${codigoAdenda}/${Suscripcion}/`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status >= 200 && response.status < 300) {
          const Estado = response.data.estado_str_Nombre;
          const solicitud = response.data;
          if (solicitud.str_CodSolicitudes.length > 14) {
            navigate(RoutesPrivate.CREARADENDA, {
              state: {
                solicitud,
                idAplicacion,
                Suscriptor,
                Suscripcion,
                gestor: false,
                idUsuario,
                Apellidos,
                Nombres,
              },
            });
          } else {
            if (Estado === "Firmado") {
              navigate(RoutesPrivate.CREARADENDA, {
                state: {
                  solicitud,
                  idAplicacion,
                  Suscriptor,
                  Suscripcion,
                  gestor: false,
                  idUsuario,
                  Apellidos,
                  Nombres,
                },
              });
            } else {
              Swal.fire(
                "",
                "La referencia colocada no se encuentra en el Registro de Contratos ",
                "error"
              );
            }
          }
        } else {
          Swal.fire(
            "",
            "El código ingresado no pertenece a ninguna solicitud",
            "error"
          );
        }
      } else {
        if (!selectedTipo) {
          Swal.fire(
            "Error",
            "Seleccione el tipo de modelo que quiere crear",
            "error"
          );
          return;
        }
        if (!empresaSeleccionadaAdenda) {
          Swal.fire(
            "Error",
            "Seleccione la empresa a la que pertenece la adenda",
            "error"
          );
          return;
        }
        if (!unidadNegocioAdenda) {
          Swal.fire(
            "Error",
            "Seleccione la unidad de negocio a la que pertenece la adenda",
            "error"
          );
          return;
        }
        navigate(RoutesPrivate.CREARADENDA, {
          state: {
            solicitud: {
              tipoSol_str_Nombre: selectedTipo?.label,
              int_idTipoSol: selectedTipo.id,
              Empresa_nombre: empresaSeleccionadaAdenda.label,
              UN_nombre: unidadNegocioAdenda.label,
              int_idEmpresa: empresaSeleccionadaAdenda.value,
              int_idUnidadNegocio: unidadNegocioAdenda.value,
              int_SolicitudGuardada: 1,
              str_DeTerceros: "no",
            },
            idAplicacion,
            Suscriptor,
            Suscripcion,
            gestor: false,
            idUsuario,
            Apellidos,
            Nombres,
            sinCodigo: true,
          },
        });
      }
    } catch {
      Swal.fire(
        "",
        "El código ingresado no pertenece a ninguna solicitud",
        "error"
      );
    }
  };
  const EditarSolicitud = () => {
    navigate(RoutesPrivate.GESTIONSOLICITUD, {
      state: {
        selectedSolicitud,
        esEditar: true,
        idAplicacion,
        Suscriptor,
        Suscripcion,
      },
    });
  };
  const verSolicitud = () => {
    navigate(RoutesPrivate.GESTIONSOLICITUD, {
      state: {
        selectedSolicitud,
        esEditar: true,
        idAplicacion,
        Suscriptor,
        Suscripcion,
        ver: true,
      },
    });
  };
  const handleRowClick = (index) => {
    setSelectedRow(index);
  };

  const recargarSolicitudes = () => {
    obtenerSolicitudesUsuario();
    setSelectedRow(null);
    setSelectedSolicitud(null);
  };
  return (
    <div className="global-inicio-solicitante">
      <Header />
      <Titulo seccion={`Bienvenido ${Nombres} ${Apellidos}  `} />

      <div
        className="container-nueva-solicitud"
        style={{ alignItems: "start" }}
      >
        <div className="card-nueva-solicitud">
          <div
            className="titulo-card-nueva-solicitud montserrat-font-500"
            style={{ justifyContent: "space-between", padding: "0 2rem" }}
          >
            <span>
              {tipoCreacionSolicitud === "solicitud"
                ? "Nueva Solicitud"
                : tipoCreacionSolicitud === "adenda"
                ? "Nueva Adenda "
                : "Descargar Plantilla"}
            </span>
            <div style={{ display: "flex", gap: "1rem" }}>
              <div className="" style={{ width: "50%", fontSize: "0.9rem" }}>
                <Select
                options={empresasFiltro}
                value={empresasFiltro.find(
                  (option) => option.value === selectedEmpresaPlantilla
                )}
                onChange={handleChangeEmpresaPlantilla}
                placeholder="Empresa"
              />
              </div>
              <select
                className="form-select"
                style={{ width: "50%", fontSize: "0.9rem" }}
                onChange={(e) => {
                  setTipoCreacionSolicitud(e.target.value);
                  setSelectedTipo(null);
                }}
              >
                <option value="solicitud">Crear Solicitud</option>
                <option value="adenda">Crear Adenda</option>
              </select>
            </div>
          </div>
          {tipoCreacionSolicitud === "solicitud" ? (
            <div className="select-card-nueva-solicitud montserrat-font">
              <Select
                options={tipoSolicitudes}
                value={tipoSolicitudes.find(
                  (option) => option.value === selectedTipo
                )}
                onChange={handleChangeTipoSolicitud}
                placeholder="Selecciona el tipo de solicitud"
              />
            </div>
          ) : tipoCreacionSolicitud === "adenda" ? (
            <div
              className=""
              style={{
                display: "flex",
                width: "100%",
                flexDirection: "column",
                padding: "0 2rem",
                alignItems: "start",
                gap: "1rem",
              }}
            >
              
              {tipoAdenda === "CC" ? (
                <input
                  type="text"
                  className="form-control"
                  placeholder="Ingresar Código de Solicitud"
                  style={{ width: "100%", margin: "0 auto" }}
                  onChange={(e) => setCodigoAdenda(e.target.value)}
                />
              ) : (
                <div
                  className=""
                  style={{
                    width: "100%",
                    margin: "0 auto",
                    flexDirection: "column",
                    display: "flex",
                    gap: "1rem",
                  }}
                >
                  <Select
                    options={tipoSolicitudes}
                    value={tipoSolicitudes.find(
                      (option) => option.value === setSelectedTipo
                    )}
                    onChange={handleChangeTipoSolicitud}
                    placeholder="Selecciona el tipo de solicitud"
                  />
                  <div
                    className=""
                    style={{ width: "100%", margin: "0 auto", display: "flex" }}
                  >
                    <div
                      className="div-input-crear-solicitud"
                      style={{
                        marginTop: "0",
                        display: "flex",
                        flexDirection: "row",
                        gap: "1rem",
                        width: "100%",
                      }}
                    >
                      <div className="" style={{ width: "100%" }}>
                        <Select
                          options={empresasFiltro}
                          value={empresasFiltro.find(
                            (option) =>
                              option.value === empresaSeleccionadaAdenda.value
                          )}
                          onChange={handleChangeEmpresaAdenda}
                          placeholder="Empresa"
                        />
                      </div>
                      <div className="" style={{ width: "100%" }}>
                        <Select
                          options={unidadesNegocios}
                          value={unidadesNegocios.find(
                            (option) =>
                              option.value === unidadNegocioAdenda.value
                          )}
                          onChange={handleChangeUnidadNegocioAdenda}
                          placeholder="Unidad de Negocio"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div
              className="select-card-nueva-solicitud montserrat-font"
              style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
            >
              <Select
                options={empresasFiltro}
                value={empresasFiltro.find(
                  (option) => option.value === selectedEmpresaPlantilla
                )}
                onChange={handleChangeEmpresaPlantilla}
                placeholder="Selecciona la empresa"
              />
              <Select
                options={tipoSolicitudes}
                value={tipoSolicitudes.find(
                  (option) => option.value === selectedTipo
                )}
                onChange={handleChangeTipoSolicitud}
                placeholder="Selecciona el tipo de solicitud"
              />
            </div>
          )}

          <div className="modelo-card-nueva-solicitud montserrat-font">
            {tipoCreacionSolicitud === "solicitud" ? (
              <div className="radio-card-nueva-solicitud">
                <span className="span-radio-card-NS montserrat-font">
                  Modelo de Contrato:{" "}
                </span>
                <div className="form-check form-check-inline">
                  <input
                    className="form-check-input montserrat-font"
                    type="radio"
                    name="inlineRadioOptions"
                    id="inlineRadio1"
                    value="Propio"
                    checked={selectedTipoModelo === "Propio"}
                    onChange={handleChangeTipoModelo}
                  />
                  <label className="form-check-label" htmlFor="inlineRadio1">
                    Propio
                  </label>
                </div>
                <div className="form-check form-check-inline">
                  <input
                    className="form-check-input montserrat-font"
                    type="radio"
                    name="inlineRadioOptions"
                    id="inlineRadio2"
                    value="terceros"
                    checked={selectedTipoModelo === "terceros"}
                    onChange={handleChangeTipoModelo}
                  />
                  <label className="form-check-label" htmlFor="inlineRadio2">
                    De Terceros
                  </label>
                </div>
              </div>
            ) : (
              <div className="radio-card-nueva-solicitud">
                <span className="span-radio-card-NS montserrat-font">
                  Tipo de Adenda:{" "}
                </span>
                <div className="form-check form-check-inline">
                  <input
                    className="form-check-input montserrat-font"
                    type="radio"
                    name="inlineRadioOptions"
                    id="codigoAdenda"
                    value="CC"
                    checked={tipoAdenda === "CC"}
                    onChange={handleChangeTipoAdenda}
                  />
                  <label className="form-check-label" htmlFor="codigoAdenda">
                    Con Código
                  </label>
                </div>
                <div className="form-check form-check-inline">
                  <input
                    className="form-check-input montserrat-font"
                    type="radio"
                    name="inlineRadioOptions"
                    id="scodigoAdenda"
                    value="SC"
                    checked={tipoAdenda === "SC"}
                    onChange={handleChangeTipoAdenda}
                  />
                  <label className="form-check-label" htmlFor="scodigoAdenda">
                    Sin Código
                  </label>
                </div>
              </div>
            )}
            <div className="" style={{ display: "flex", gap: "0.5rem" }}>
              {tipoCreacionSolicitud === "solicitud" && (
                <div
                  className="boton-card-nueva-solicitud montserrat-font"
                  onClick={handleDownload}
                >
                  Descargar Plantilla
                </div>
              )}
              <div
                className="boton-card-nueva-solicitud montserrat-font"
                onClick={
                  tipoCreacionSolicitud === "solicitud"
                    ? CrearSolicitud
                    : CrearAdenda
                }
              >
                {tipoCreacionSolicitud === "solicitud" ||
                tipoCreacionSolicitud === "adenda"
                  ? "Crear"
                  : "Descargar Plantilla"}
              </div>
            </div>
          </div>
        </div>
        <div className="card-nueva-solicitud">
          <ResumenConteo data={solicitudes} />
        </div>
      </div>

      <div className="div-boton-filtrar-solicitudes-inicio-solicitante montserrat-font">
        <div className="texto-solicitudes-inicio-gestor lato-font">
          Solicitudes
        </div>
        <button
          className="boton-filtrar-inicio-solicitante montserrat-font"
          onClick={AbrirFiltro}
        >
          <i className="fa-solid fa-sliders"></i> Filtrar
        </button>
      </div>
      <div className="div-container-tabla-inicio-solicitante">
        <div className="div-tabla-inicio-solicitante">
          <table className="tabla-inicio-solicitante">
            <thead>
              <tr>
                <th>Num. Solicitud</th>
                <th>Fech. Solicitud</th>
                <th>Fech. Esperada</th>
                <th>Empresa</th>
                <th>Monto del contrato / Valor</th>
                <th>Estado</th>
              </tr>
            </thead>
            <tbody>
              {solicitudesActuales.length === 0 ? (
                <tr>
                  <td colSpan="6" style={{ textAlign: "center" }}>
                    No se encontraron registros
                  </td>
                </tr>
              ) : (
                solicitudesActuales.map((solicitud, index) => {
                  const fechaSolicitud = solicitud.dt_FechaEsperada
                    ? solicitud.dt_FechaEsperada.split("T")[0]
                    : null;

                  // Comparar si es hoy, ayer o mañana
                  const esHoy = fechaSolicitud === fechaHoy;
                  const esAyer = fechaSolicitud === fechaAyer;
                  const esMañana = fechaSolicitud === fechaMañana;
                  console.log(esMañana, solicitud.str_CodSolicitudes);
                  const esEstadoCritico =
                    solicitud.estado_nombre === "Asignado" ||
                    solicitud.estado_nombre === "En Proceso";

                  // Determina la clase de alerta dependiendo de si la fecha es hoy, ayer o mañana
                  const alertaClase = esEstadoCritico
                    ? esHoy
                      ? "alerta-fecha-proxima" // Alerta si es hoy
                      : esAyer
                      ? "alerta-fecha-pasada" // Alerta si es ayer
                      : esMañana
                      ? "alerta-fecha-proxima" // Alerta si es mañana
                      : ""
                    : "";

                  // Define el título de la alerta dependiendo de la fecha
                  const tituloAlerta = esEstadoCritico
                    ? esHoy
                      ? "Solicitud con fecha próxima"
                      : esAyer
                      ? "Solicitud con fecha pasada"
                      : esMañana
                      ? "Solicitud con fecha para mañana"
                      : ""
                    : "";
                  return (
                    <tr
                      key={index}
                      onClick={() => {
                        CambiarBarraLateral(solicitud);
                        handleRowClick(index);
                      }}
                      className={`${
                        selectedRow === index ? "fila-seleccionada" : ""
                      } `}
                    >
                      <td className={`${alertaClase}`} title={tituloAlerta}>
                        {solicitud.str_DeTerceros === "si" ? (
                          <Tooltip title="Modelo de Terceros" placement="top">
                            <span className="identificador-modelo">.</span>{" "}
                          </Tooltip>
                        ) : (
                          ""
                        )}
                        {solicitud.str_CodSolicitudes}
                      </td>
                      <td>{formatDate(solicitud.dt_FechaRegistro)}</td>
                      <td>
                        {solicitud.dt_FechaEsperada
                          ? formatDate(solicitud.dt_FechaEsperada)
                          : ""}
                      </td>
                      <td className="colum-empresa-tabla-solicitante">
                        <Tooltip
                          title={solicitud.str_NombreEmpresa}
                          placement="top-start"
                        >
                          {solicitud.str_NombreEmpresa}
                        </Tooltip>
                      </td>
                      <td>
                        {solicitud.db_Honorarios === "0" ||
                        solicitud.db_Honorarios === null ||
                        solicitud.db_Honorarios === 0
                          ? ""
                          : solicitud.str_Moneda === "dolares"
                          ? `$ ${Number(solicitud.db_Honorarios).toLocaleString(
                              "es-PE"
                            )}`
                          : `${
                              solicitud.str_SimboloMoneda
                                ? solicitud.str_SimboloMoneda
                                : "$"
                            } ${Number(
                              solicitud.db_Honorarios
                            ).toLocaleString()}`}
                      </td>
                      <td>
                        <div className="div-estado">
                          <span>{solicitud.estado_nombre}</span>
                          <CircleEstado estado={solicitud.estado_nombre} />
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan="6">
                  <div className="contenedor-footer-tabla">
                    <button
                      onClick={() => recargarSolicitudes()}
                      className="boton-recargar"
                    >
                      Recargar
                    </button>
                    <div className="paginacion-tabla-solicitante">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="numero-pagina"
                      >
                        <IconoLeft size={"1rem"} color={"#000"} />
                      </button>
                      {renderNumeritosPaginacion()}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={indiceUltimaSolicitud >= solicitudes.length}
                        className="numero-pagina"
                      >
                        <IconoRight size={"1.5rem"} color={"#000"} />
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
        <div className="barraLateral-inicio-solicitante">
          {/* <span className="titulo-barra-Lateral lato-font-400">{ !selectedSolicitud ?"" : "Detalle" } </span> */}
          {selectedSolicitud?.estado_nombre === "Nuevo" ? (
            <EstadoNuevo
              solicitudSeleccionada={selectedSolicitud}
              historiales={HistorialesUsuario}
              idAplicacion={idAplicacion}
              Suscripcion={Suscripcion}
              idUsuario={idUsuario}
              SubmitObtenerDatos={SubmitObtenerDatos}
              setSelectedSolicitud={setSelectedSolicitud}
              EditarSolicitud={EditarSolicitud}
              verSolicitud={verSolicitud}
            />
          ) : selectedSolicitud?.estado_nombre === "En Validación" ? (
            <EstadoValidacion
              solicitudSeleccionada={selectedSolicitud}
              historiales={HistorialesUsuario}
              idUsuario={idUsuario}
              SubmitObtenerDatos={SubmitObtenerDatos}
              Suscripcion={Suscripcion}
              setSelectedSolicitud={setSelectedSolicitud}
              aprobadores={aprobadores}
              idAplicacion={idAplicacion}
              Suscriptor={Suscriptor}
            />
          ) : selectedSolicitud?.estado_nombre === "Firmado" ? (
            <EstadoFirmado
              solicitudSeleccionada={selectedSolicitud}
              historiales={HistorialesUsuario}
              idUsuario={idUsuario}
              SubmitObtenerDatos={SubmitObtenerDatos}
              Suscripcion={Suscripcion}
              setSelectedSolicitud={setSelectedSolicitud}
              aprobadores={aprobadores}
              Suscriptor={Suscriptor}
              idAplicacion={idAplicacion}
            />
          ) : selectedSolicitud?.estado_nombre === "Asignado" ||
            selectedSolicitud?.estado_nombre === "En Proceso" ||
            selectedSolicitud?.estado_nombre === "Aceptado" ||
            selectedSolicitud?.estado_nombre === "En Aprobación" ||
            selectedSolicitud?.estado_nombre === "Aprobado" ? (
            <OtrosEstados
              solicitudSeleccionada={selectedSolicitud}
              historiales={HistorialesUsuario}
              aprobadores={aprobadores}
              idUsuario={idUsuario}
              Suscripcion={Suscripcion}
              Suscriptor={Suscriptor}
              idAplicacion={idAplicacion}
            />
          ) : (
            <BarraLimpia />
          )}
        </div>
      </div>
      {isModalVisible && (
        <div className="modal-filtros">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarFiltro}
            ></button>
          </div>
          <div className="titulo-modal-filtros">
            <span className="text-titulo-modal-filtros lato-font size-titulos">
              Buscar Por
            </span>
            <span
              className="borrarTodo-modal-filtros montserrat-font-500"
              onClick={BorrarTodosFiltros}
            >
              Borrar Todo
            </span>
          </div>
          <div className="inputs-modal-filtros">
            <input
              type="text"
              className="form-control"
              value={codSolicitudFiltro}
              onChange={(e) => setCodSolicitudFiltro(e.target.value)}
              placeholder="Cod.Solicitud"
            />
            <input
              type="text"
              className="form-control"
              value={documentoCliente}
              onChange={(e) => setDocumentoCliente(e.target.value)}
              placeholder="Doc.Cliente/Proveedor"
            />
            <input
              type="text"
              className="form-control"
              value={razonSocial}
              onChange={(e) => setRazonSocial(e.target.value)}
              placeholder="Razón social"
            />
            <TagInput setTags={setTags} tags={tags} />
            <Select
              options={tipoSolicitudes}
              value={tipoSolicitudes.find(
                (option) => option.value === selectedTipoSolicitudFiltro
              )}
              onChange={handleChangeTipoSolicitudFiltro}
              placeholder="Tipo de Solicitud"
            />
            <Select
              options={estadosFiltro}
              value={estadosFiltro.find(
                (option) => option.value === selectedEstadoFiltro
              )}
              onChange={handleChangeestadoFiltro}
              placeholder="Estado de solicitud"
            />
            <div className="anno-mes-modal-filtros">
              <select
                className="form-select"
                aria-label="Default select example"
                value={anioSeleccionado}
                onChange={handleAnioChange}
              >
                <option value="" selected disabled>
                  Año
                </option>
                {anios.map((anio) => (
                  <option key={anio} value={anio}>
                    {anio}
                  </option>
                ))}
              </select>
              <select
                className="form-select"
                aria-label="Default select example"
                value={mesSeleccionado}
                onChange={handleMesChange}
              >
                <option value="" selected disabled>
                  Mes
                </option>
                {meses.map((mes) => (
                  <option key={mes.value} value={mes.value}>
                    {mes.label}
                  </option>
                ))}
              </select>
            </div>
            <Select
              options={empresasFiltro}
              value={empresasFiltro.find(
                (option) => option.value === selectedEmpresaFiltro
              )}
              onChange={handleChangeEmpresaFiltro}
              placeholder="Empresa"
            />
          </div>
          <div className="div-btn-filtrar">
            <div className="form-check">
              <input
                className="form-check-input"
                type="checkbox"
                value=""
                checked={mostrarArchivadas}
                onChange={handleChangeMostrarArchivados}
              />
              <label className="form-check-label">Mostrar Archivadas</label>
            </div>
            <button className="btn btn-filtrar" onClick={handleFiltrar}>
              Aplicar Filtros
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inicio;
