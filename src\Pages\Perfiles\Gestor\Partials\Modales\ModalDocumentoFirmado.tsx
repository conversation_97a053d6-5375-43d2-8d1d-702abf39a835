import React, { useState } from "react";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { TextField } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import Swal from "sweetalert2";
import Cookies from "js-cookie";
import { validateToken } from "../../../../Components/Services/TokenService";
import getLogs from "../../../../Components/Services/LogsService";

interface ModalTagsProps {
    isModalVisiblContratoFirmado: boolean;
    CerrarModalContratoFirmado: () => void;
    file: File;
    handleSubmitFiles: () => void;
    idUsuario: number;
    Suscripcion: string;
    idSolicitud: number;
    SubmitObtenerDatos: () => void;
    setSelectedSolicitud: (solicitud: object) => void;
  }

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");
const ModalDocumentoFirmado: React.FC<ModalTagsProps> = ({
  isModalVisiblContratoFirmado,
  CerrarModalContratoFirmado,
  file,
  handleSubmitFiles,
  idUsuario,
  Suscripcion,
  idSolicitud,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  solicitudActual
}) => {
  const [fechaFirmado, setFechaFirmado] = useState(new Date().toISOString().split("T")[0]);
  const [horasTrabajadas, setHorasTrabajadas] = useState<number>(0);
  const [fechaFin, setFechaFin] = useState(new Date().toISOString().split("T")[0]);
  const token = Cookies.get("Token");

  const fechaMaxima = solicitudActual?.dt_FechaRenAut || "";
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value; 
    setFechaFirmado(`${selectedDate}T00:00:00`);
  };
  const handleDateChangeFin = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = e.target.value; 
    setFechaFin(`${selectedDate}T00:00:00`);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setHorasTrabajadas(parseInt(value));
  };
  const handleEnviarFirma = async () => {
await validateToken();
    if (!fechaFirmado || !horasTrabajadas || !fechaFin) {
      Swal.fire("", "Todos los campos son obligatorios", "error");
      return;
    }
    if(fechaFin < fechaFirmado){
      Swal.fire("", "La fecha de fin no puede ser menor a la fecha de inicio", "error");
      return;
    }
    try {
      
      const response = await axios.put(API_GESTOR["EnviarDocumentoFirmado"](), {
        int_idSolicitudes: idSolicitud,
        dt_FirmaContrato: fechaFirmado,
        int_HorasTrabajadas: horasTrabajadas,
        int_idUsuarioModificacion: idUsuario,
        dt_FechaFin:fechaFin 
      },{
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        try {
            handleSubmitFiles()
            
          const respuestaActualizarEstado = await axios.put(
            API_GESTOR["ActualizarEstado"](),
            {
              str_idSuscriptor: Suscripcion,
              nombre_estado: "Firmado",
              int_idUsuarioCreacion: idUsuario,
              int_idSolicitudes: idSolicitud,
            },{
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
              }
            }
          );
          if(respuestaActualizarEstado.status >= 200 && respuestaActualizarEstado.status < 300){
            Swal.fire("", "El Documento se subió correctamente", "success");
            CerrarModalContratoFirmado();
          SubmitObtenerDatos();
          setSelectedSolicitud({});
          
          }
        } catch {
          console.error(`Error para cambiar el estado ${response.status}`);
        }
        await getLogs(JSON.stringify({
           dt_FirmaContrato: fechaFirmado,
          int_HorasTrabajadas: horasTrabajadas,
           dt_FechaFin:fechaFin 
        }),solicitudActual || {},idSolicitud,"Listado de Solicitudes","Solicitudes","Editar Solicitud","Contratos","POST");

      } else {
        console.error(`Error: código de estado ${response.status}`);
      }

      
    } catch (error) {
      console.error("Error al asignar el gestor:", error);
    }
  };
  return (
    <>
      {isModalVisiblContratoFirmado && (
        <div className="modal-tags">
          <div className="boton-cerrar-modal-filtros">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={CerrarModalContratoFirmado}
            ></button>
          </div>
          <div className="modalDocFirmado lato-font">
            {file ?<span>
              Se está subiendo el documento <br />
              <span className="nombre_archivo">{file.name}</span>{" "}
            </span> : 
            ""

            }
            
          </div>
          <div className="input-fecha-firmado">
            <label className="form-label">Fecha de Firma del contrato: </label>

            <input
              type="date"
                          onKeyDown={(e) => e.preventDefault()}
              className="form-control"
              onChange={handleDateChange}
              name="fechaFirmado"
              max={fechaMaxima ? fechaMaxima.split("T")[0] : ""}

            />
          </div>

          <div className="input-fecha-firmado">
            <label className="form-label">Fecha de Fin del contrato: </label>
            <input
              type="date"
                          onKeyDown={(e) => e.preventDefault()}
              max={fechaMaxima ? fechaMaxima.split("T")[0] : ""}
              min={fechaMaxima ? "" : fechaFirmado ? fechaFirmado.split("T")[0]  :   new Date().toISOString().split("T")[0]}

              className="form-control"
              onChange={handleDateChangeFin}
              name="fechaFin"
            />
          </div>
          <div className="input-fecha-firmado">
            <label className="form-label">Horas Trabajadas: </label>
            <input
              type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-","."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
              className="form-control"
              placeholder={horasTrabajadas}
              onChange={handleInputChange}
              required
            />
          </div>
          <div className="botones-modal-solicitante">
            <button
              className="btn btn-outline-primary"
              onClick={CerrarModalContratoFirmado}
            >
              Cancelar
            </button>
            <button className="btn btn-primary" onClick={handleEnviarFirma}>Guardar</button>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalDocumentoFirmado;
