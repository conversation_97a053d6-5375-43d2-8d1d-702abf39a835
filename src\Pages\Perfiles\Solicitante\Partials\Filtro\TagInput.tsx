import React, { useState } from "react";
type TagInputProps = {
  setTags: (tags: string[]) => void;
  tags: string[];
}
const TagInput: React.FC<TagInputProps> = ({ setTags, tags }) => {
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (e: { key: string; preventDefault: () => void; }) => {
    if (e.key === "Enter" && inputValue) {
      e.preventDefault();
      if (tags.length < 4) {
        setTags([...tags, inputValue]);
        setInputValue("");
      } else {
        alert("No se pueden agregar más de 4 tags.");
      }
    }
  };

  const handleTagRemove = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  return (
    <div className="tag-input-container">
      <div className="tag-input">
        {tags.map((tag, index) => (
          <div key={index} className="tag">
            {tag}
            <button
              type="button"
              className="tag-remove"
              onClick={() => handleTagRemove(index)}
            >
              &times;
            </button>
          </div>
        ))}
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={tags.length >= 4 ? "" : "Buscar tag"}
          disabled={tags.length >= 4}
        />
      </div>
    </div>
  );
};

export default TagInput;