import React from "react";


const IconoRight = ({size,selected,color,colorselected}) => {
  return (
    <svg
      height={size}
      id="Layer_1"
      version="1.1"
      viewBox="0 0 512 512"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M184.7,413.1l2.1-1.8l156.5-136c5.3-4.6,8.6-11.5,8.6-19.2c0-7.7-3.4-14.6-8.6-19.2L187.1,101l-2.6-2.3  C182,97,179,96,175.8,96c-8.7,0-15.8,7.4-15.8,16.6h0v286.8h0c0,9.2,7.1,16.6,15.8,16.6C179.1,416,182.2,414.9,184.7,413.1z" fill={ selected ? colorselected : color}/>
    </svg>
  );
};

export default IconoRight;
