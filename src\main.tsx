import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { ErrorBoundary, errorHandlers } from './errorConfig'
import { AppRoutes } from './Security/Routes/AppRoutes.tsx'

ReactDOM.createRoot(document.getElementById('root')!, {
  onUncaughtError: errorHandlers.onUncaughtError,
  onCaughtError: errorHandlers.onCaughtError
}).render(
  <React.StrictMode>
    <ErrorBoundary>
      <AppRoutes />
    </ErrorBoundary>
  </React.StrictMode>,
)