import React, { useState } from "react";
import ModalGestores from "../Modals/ModalGestores";
import Swal from "sweetalert2";
import HistorialSolicitud from "./HistorialSolicitud";
import GestorAsignado from "./GestorAsignado";
import { Tooltip } from "@mui/material";
import ModalEliminar from "../Modals/ModalEliminar";
import IconoEditar from "../../../../../assets/SVG/IconoEditar";
import IconoBasureroEliminar from "../../../../../assets/SVG/IconoBasureroEliminar";
import IconoVer from "../../../../../assets/SVG/IconoVer";

interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes:string
  };
  historiales: any[]; 
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: () => void;
  setSelectedSolicitud: (solicitud: any) => void,
  EditarSolicitud: ()=>void,
  verSolicitud: ()=>void
}

const EstadoNuevo: React.FC<EstadoNuevoProps> = ({
  solicitudSeleccionada,
  historiales,
  idAplicacion,
  Suscripcion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  EditarSolicitud,
  verSolicitud
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAsignar: false,
    isModalVisibleEliminar: false,
  });

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };

  const AbrirAsignar = () => {
    if (solicitudSeleccionada.int_idGestor) {
      Swal.fire('', 'La solicitud ya tiene un gestor asignado', 'error');
    } else {
      toggleModal('isModalVisibleAsignar');
    }
  };

  return (
    <>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
    
        <Tooltip title="Editar" placement="top" >
        <div className="icono-barralateral-acciones" onClick={EditarSolicitud}>
          <IconoEditar size={"1.3rem"} color={"#fff"}/>
        </div>
        </Tooltip>
        <Tooltip title="Eliminar" placement="top">
      <div className="icono-barralateral-acciones" onClick={() => toggleModal('isModalVisibleEliminar')}>
        <IconoBasureroEliminar size={"1.3rem"} color={"#000"}/>
      </div>
        </Tooltip>
        <Tooltip title="Ver Solicitud" placement="top">
        <div className="icono-barralateral-acciones" onClick={verSolicitud}>
          <IconoVer size={"1.3rem"} color={"#000"}/>
        </div>
        </Tooltip>
      </div>
      
      <GestorAsignado solicitudSeleccionada={solicitudSeleccionada} />
      <HistorialSolicitud historiales={historiales} />
      
      <ModalGestores
        isModalVisibleAsignar={modales.isModalVisibleAsignar}
        CerrarAsignar={() => toggleModal('isModalVisibleAsignar')}
        idAplicacion={idAplicacion}
        Suscripcion={Suscripcion}
        idSolicitud={solicitudSeleccionada.int_idSolicitudes}
        idUsuario={idUsuario}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
      />
      
      <ModalEliminar 
        isModalVisibleEliminar={modales.isModalVisibleEliminar}
        CerrarModalEliminar={() => toggleModal('isModalVisibleEliminar')}
        solicitudSeleccionada={solicitudSeleccionada}
        SubmitObtenerDatos={SubmitObtenerDatos}
        setSelectedSolicitud={setSelectedSolicitud}
      />
    </>
  );
};

export default EstadoNuevo;