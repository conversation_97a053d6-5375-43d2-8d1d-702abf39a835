const BASE_URL = import.meta.env.VITE_BASE_URL;

type Suscripcion = string | number | any;
type IdUsuario = string | number | any;

type Solicitud = string | number;

const API_CONTROLLER= {
  ObtenerSolicitudesController: (Suscripcion: Suscripcion): string => 
      `${BASE_URL}api/solicitudes/controller/?str_idSuscriptor=${Suscripcion}`,
  
  AprobarSolicitud: (solicitud: Solicitud, idUsuario: IdUsuario, suscripcion: Suscripcion): string => 
    `${BASE_URL}api/solicitudes/aprobador/aprobar/${solicitud}/${idUsuario}/${suscripcion}/`,

  ListarAprobadores: (Suscripcion:Suscripcion,Solicitud:Solicitud): string => 
    `${BASE_URL}api/solicitudes/aprobadores/${Suscripcion}/${Solicitud}/`,
};

export default API_CONTROLLER;