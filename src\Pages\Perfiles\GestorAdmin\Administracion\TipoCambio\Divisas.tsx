/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import IconoEliminarMaestros from "../../../../../assets/SVG/IconoEliminarMaestros";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";
import axios from "axios";
import Cookies from "js-cookie";
import Swal from "sweetalert2";
import { decrypt, validateToken } from "../../../../Components/Services/TokenService";

interface divisa {
    str_valorCambio: string;
    int_estado: number;
    dt_FechaCambio: string;
}
function formatDate(dateString: string) {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString("es-ES", { month: "long" });
    const year = date.getFullYear();
  
    return `${day} de ${month} de ${year}`;
  }
const Divisas: React.FC = () => {
  const [divisas, setDivisas] = useState<divisa[]>([]);
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const [nuevaDivisa, setNuevaDivisa] = useState("");
  const token = Cookies.get("Token");

  const fetchDivisas = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_GESTOR["ObtenerDivisas"](Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setDivisas(response.data);
    } catch (error) {
      console.log("No se pudo obtener las unidades", error);
    }
  };


  const agregarDivisa = async () => {
    await validateToken();
    if (!nuevaDivisa.trim()) {
      Swal.fire(
        "",
        "Por favor ingresa un nombre para la nueva unidad",
        "error"
      );
      return;
    }
    try {
      await axios.post(
        API_GESTOR["AgregarDivisa"](),
        {
          str_valorCambio: nuevaDivisa,
          str_idSuscripcion: Suscripcion,
          int_idUsuarioModificacion: idUsuario,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      await fetchDivisas();
      setNuevaDivisa("");
      Swal.fire("", "La unidad ha sido agregada.", "success");
    } catch (error) {
      Swal.fire("", "No se pudo agregar la unidad", "error");
      console.log("Error al agregar la unidad", error);
    }
  };
  useEffect(() => {
    fetchDivisas();
  }, []);

  return (
    <div className="global-maestros">
      <div className="titulo-maestros lato-font">
        Maestro de Divisas
      </div>
      <div className="container-gestor-administracion">
        <table className="tabla-gestor-administracion-divisas">
            <thead>
              <tr>
                <th>Valor</th>
                <th>Fecha de Cambio</th>
                <th>Estado</th>

              </tr>
            </thead>
          <tbody>
            {divisas.length === 0 ? (
              <tr>
                <td colSpan={6} style={{ textAlign: "center" }}>
                  No se encontraron registros
                </td>
              </tr>
            ) : (
                divisas.map((divisa, index) => (
                <tr key={index}>
                  <td>
                      {divisa.str_valorCambio}
                   
                  </td>
                  <td>
                      {divisa.dt_FechaCambio ? formatDate(divisa.dt_FechaCambio) : "Actual"}
                    </td>
                    <td>
                      {divisa.int_estado === 1 ? "Activo" : "Inactivo"}
                    </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        <div className="div-registros-gestor-administracion-divisas">
          <input
            type="number"
            className="form-control"
            placeholder="Ingresar Valor"
            value={nuevaDivisa}
            onChange={(e) => setNuevaDivisa(e.target.value)}
            onKeyDown={(e) => {
              if (["e", "E", "+", "-","."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            onWheel={(e) => e.target.blur()}
          />
          <button className="btn-nuevo-maestros" onClick={agregarDivisa}>
            <i className="fa-solid fa-plus"></i> Agregar
          </button>
        </div>
      </div>
    </div>
  );
};

export default Divisas;
