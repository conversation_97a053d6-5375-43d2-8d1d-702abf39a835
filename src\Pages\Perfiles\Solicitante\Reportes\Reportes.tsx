 import Header from "../../../Components/Partials/Header/Header";
import Titulo from "../../../Components/Partials/Seccion/Titulo";
 import "./Reportes.css";
import Cookies from "js-cookie";
import { useEffect, useState,  useCallback } from "react";
 
import axios from "axios";
 import Select from "react-select";
import { decrypt, validateToken } from "../../../Components/Services/TokenService";
import { RoutesPrivate } from "../../../../Security/Routes/ProtectedRoute";
 import getLogs from "../../../Components/Services/LogsService";
import Contadores from "../../Gestor/Reportes/Partials/Contadores/Contadores";
import Grafico2 from "../../Gestor/Reportes/Partials/Graficos/Grafico2";
import Grafico1 from "../../Gestor/Reportes/Partials/Graficos/Grafico1";
import Grafico3 from "../../Gestor/Reportes/Partials/Graficos/Grafico3";
import Grafico4 from "../../Gestor/Reportes/Partials/Graficos/Grafico4";
import API_GESTOR from "../../../../assets/Api/ApisGestor";
import API_SOLICITANTE from "../../../../assets/Api/ApisSolicitante";
import Matriz from "../../Gestor/Reportes/Partials/Matriz/Matriz";
import ModalSeleccion from "../../Gestor/Reportes/Partials/Matriz/ModalSeleccion";
import * as XLSX from "xlsx";
const anioActual = new Date().getFullYear();
 
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();
  return `${day} de ${month} de ${year}`;
};
const Reportes = () => {
  
     const Suscripcion = decrypt(Cookies.get("suscripcion"));
     const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscriptor = decrypt(Cookies.get("suscriptor"));
  const idAplicacion = decrypt(Cookies.get("idAplicacion"));
  const [empresasFiltro, setEmpresasFiltro] = useState([]);
  const [tiposSolicitudFiltro, setTiposSolicitudFiltro] = useState([]);
    const [codSolicitud, setCodSolicitud] = useState("");

      const [filtroMatrizEstado , setFiltroMatrizEstado] = useState('');

  const [selectedTipoSolicitudFiltro, setSelectedTipoSolicitudFiltro] =
    useState("");
      const [mostrarMatriz, setMostrarMatriz] = useState(false);
  const [solicitudes, setSolicitudes] = useState([]);
  
  const [selectedEmpresaFiltro, setSelectedEmpresaFiltro] = useState("");
  console.log("selectedEmpresaFiltro", selectedEmpresaFiltro);
  const [aniosSeleccionados, setAniosSeleccionados] = useState({
    grafico1: anioActual,
    grafico2: anioActual,
    grafico3: anioActual,
    grafico4: anioActual,
    matriz: anioActual,
    contadores: anioActual,
    gestores: anioActual,
  });
  
  
  // Estados de carga para optimización
  const [isLoadingGraphics, setIsLoadingGraphics] = useState(false);
   const [graphicsData, setGraphicsData] = useState({
    contadores: null,
    grafico1: null,
    grafico2: null,
    grafico3: null,
    grafico4: null
  });
 const [selectedFields, setSelectedFields] = useState([
    "Cod.Solicitud",
    "Cliente/Proveedor",
    "Presupuesto",
    "Empresa",
    "Unidad Negocio",
    "Estado",
  ]);
  const token = Cookies.get("Token");
  const rol = decrypt(Cookies.get("rol"));
 const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setCodSolicitud(value);
  };
  const fields = [
    "Cod.Solicitud",
    "Cliente/Proveedor",
    "Presupuesto",
    "Empresa",
    "Unidad Negocio",
    "Estado",
    "Tipo Contrato",
    "Descripcion Contrato",
    "Fecha Firma",
    "Fecha Registro",
    "Gestor",
    "Renovación Automatica",
    "Garantía",
    "Monto del contrato",
    "Margen",
    "Forma Pago",
    "Resolución Anticipada",
    "Horas Trabajadas",
    "Fecha Fin",
    "Moneda",
    "Penalidades",
    "Cláusula de no competencia",
  ];
  const handleChangeEmpresaFiltro = (selectedOption: any) => {
    setSelectedEmpresaFiltro(selectedOption);
  };
 
 

  // Función optimizada para cargar todos los datos de gráficos en paralelo
  const loadAllGraphicsData = useCallback(async () => {
    if (!selectedEmpresaFiltro?.value) return;

    setIsLoadingGraphics(true);
    await validateToken();

    try {
      const requests = [
        // Contadores
        axios.get(API_SOLICITANTE["PromedioAtencion"](idUsuario, Suscripcion, selectedEmpresaFiltro.value, aniosSeleccionados.contadores, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["PromedioPreparacion"](idUsuario, Suscripcion, selectedEmpresaFiltro.value, aniosSeleccionados.contadores, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["PromedioTotal"](idUsuario, Suscripcion, selectedEmpresaFiltro.value, aniosSeleccionados.contadores, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["SolicitudesTotal"](idUsuario, Suscripcion, selectedEmpresaFiltro.value, aniosSeleccionados.contadores, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["ContratosFirmados"](idUsuario, Suscripcion, selectedEmpresaFiltro.value, aniosSeleccionados.contadores, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        // Gráficos
        axios.get(API_SOLICITANTE["PendientesporUN"](Suscripcion, aniosSeleccionados.grafico1, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["PresupuestoUN"](Suscripcion, aniosSeleccionados.grafico2, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["SolicitudesPorEstado"](Suscripcion, aniosSeleccionados.grafico3, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        }),
        axios.get(API_SOLICITANTE["HorasUN"](Suscripcion, aniosSeleccionados.grafico4, idUsuario, selectedEmpresaFiltro.value, selectedTipoSolicitudFiltro.value), {
          headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
        })
      ];

      const responses = await Promise.all(requests);
      console.log("Responses from all graphics data:", responses[6].data);
      setGraphicsData({
        contadores: {
          promedioAtencion: responses[0].data[0] || {},
          promedioPreparacion: responses[1].data[0] || {},
          promedioTotal: responses[2].data[0] || {},
          solicitudesTotal: responses[3].data || {},
          contratosFirmados: responses[4].data || {}
        },
        grafico1: responses[5].data || [],
        grafico2: responses[6].data || [],
        grafico3: responses[7].data || [],
        grafico4: responses[8].data || []
      });
    } catch (error) {
      console.error("Error al cargar datos de gráficos:", error);
    } finally {
      setIsLoadingGraphics(false);
    }
  }, [idUsuario, Suscripcion, selectedEmpresaFiltro, aniosSeleccionados, selectedTipoSolicitudFiltro, token]);
 
   const fetchSolicitudes = async () => {
    await validateToken();
    if (selectedFields.length > 0 && aniosSeleccionados.matriz) {
      const response = await axios.get(
        API_SOLICITANTE["MatrizSolicitudesSolicitante"](
          idUsuario,
          Suscripcion,
          aniosSeleccionados.matriz,
          codSolicitud,
          filtroMatrizEstado
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setSolicitudes(response.data);
    }
  };

  useEffect(() => {
    fetchSolicitudes();
  }, [aniosSeleccionados.matriz, selectedFields,filtroMatrizEstado]);
  const handleAnioMatrizChange = (e) => {
    const { value } = e.target;
    setAniosSeleccionados((prevState) => ({
      ...prevState,
      matriz: value,
    }));
  };
  const handleDownload = () => {
    const workbook = XLSX.utils.book_new();
    const data = [];

    // Ordena las columnas en el orden deseado
    const headers = [
      "Cod.Solicitud",
      "Cliente/Proveedor",
      "Empresa",
      "Unidad Negocio",
      "Estado",
      "Tipo Contrato",
      "Descripcion Contrato",
      "Fecha Firma",
      "Fecha Registro",
      "Gestor",
      "Renovación Automatica",
      "Garantía",
      "Monto del contrato",
      "Moneda",
      "Margen",
      "Presupuesto",
      "Forma Pago",
      "Resolución Anticipada",
      "Horas Trabajadas",
      "Fecha Fin",
      "Penalidades",
      "Cláusula de no competencia",
    ];

    data.push(headers);

    const camposDeFecha = ["Fecha Firma", "Fecha Registro", "Fecha Fin"];

    solicitudes.forEach((solicitud) => {
      const row = headers.map((header) => {
        if (camposDeFecha.includes(header) && solicitud[header]) {
          return formatDate(solicitud[header]);
        }
        return solicitud[header] || "-";
      });
      data.push(row);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, "Solicitudes");
    XLSX.writeFile(workbook, "solicitudes.xlsx");
  };


  const handleBorrarFiltro = () => {
  setSelectedFields([
      "Cod.Solicitud",
      "Cliente/Proveedor",
      "Presupuesto",
      "Empresa",
      "Unidad Negocio",
      "Estado",
    ]);
    setCodSolicitud("");
     setAniosSeleccionados({
      grafico1: anioActual,
      grafico2: anioActual,
      grafico3: anioActual,
      grafico4: anioActual,
      matriz: anioActual,
      contadores: anioActual,
    });
  };

  
   const handleFieldChange = (field) => {
    setSelectedFields((prevSelectedFields) => {
      if (prevSelectedFields.includes(field)) {
        return prevSelectedFields.filter((f) => f !== field);
      } else if (prevSelectedFields.length < 6) {
        return [...prevSelectedFields, field];
      } else {
        return prevSelectedFields;
      }
    });
  };
 
  const handleAnioChange = (e) => {
    const { value } = e.target;
    setAniosSeleccionados((prevState) => ({
      ...prevState,
      grafico1: value,
      grafico2: value,
      grafico3: value,
      grafico4: value,
      contadores: value,
    }));
  };
  
  const handleChangeTipoSolicitudFiltro = (selectedOption: any) => {
    setSelectedTipoSolicitudFiltro(selectedOption);
  };
  const anios = Array.from({ length: 21 }, (_, i) => 2010 + i);

  // Cargar filtros iniciales de manera optimizada
  useEffect(() => {
    const loadInitialData = async () => {
       try {
        // Cargar filtros en paralelo
        const [empresasResponse, tiposSolicitudResponse] = await Promise.all([
          axios.get(API_GESTOR["ObtenerEmpresas"](idAplicacion, Suscriptor), {
            headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
          }),
          axios.get(API_GESTOR["ObtenerTipoSolicitud"](Suscripcion), {
            headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` }
          })
        ]);

        const empresasOpciones = empresasResponse.data.map(
          (tipo: { int_idEmpresa: any; str_NombreEmpresa: any ; str_SimboloMoneda: any}) => ({
            value: tipo.int_idEmpresa,
            label: tipo.str_NombreEmpresa,
            moneda: tipo.str_SimboloMoneda
          })
        );

        const tiposOpciones = [
          { value: "", label: "Todos" },
          ...tiposSolicitudResponse.data.map(
            (tipo: { int_idTipoSolicitud: any; int_Nombre: any }) => ({
              value: tipo.int_idTipoSolicitud,
              label: tipo.int_Nombre,
              
            })
          )
        ];

        setEmpresasFiltro(empresasOpciones);
        setTiposSolicitudFiltro(tiposOpciones);

        if (empresasOpciones.length > 0) {
          setSelectedEmpresaFiltro(empresasOpciones[0]);
        }

        await getLogs(null,null,null,"Reporte de Solicitudes","Solicitudes","Ver Reportes","Contratos","GET");
      } catch (error) {
        console.error("Error al cargar datos iniciales:", error);
      } 
    };

    loadInitialData();
  }, [idAplicacion, Suscriptor, Suscripcion, token]);

  // Cargar datos de gráficos cuando cambien los filtros
  useEffect(() => {
    if (selectedEmpresaFiltro?.value  ) {
      loadAllGraphicsData();
    }
  }, [selectedEmpresaFiltro, aniosSeleccionados, selectedTipoSolicitudFiltro, loadAllGraphicsData]);
  return (
    <div>
      <Header />
      <Titulo
        seccion={
            "Estadísticas Generales"
        }
        salir={true}
        paginaSalir={
          rol === "Asistente"
            ? RoutesPrivate.INICIOASISTENTE
            : RoutesPrivate.INICIOGERENTELEGAL
        }
      />

      <div className="div-seleccion-reportes-gestor">
        <div className="menu-y-filtros-container">
 
                 <div className="menu-tabs-container">
            <div
              className={`titulo-barra-Lateral-graficos ${
                mostrarMatriz  ? "unselect-matriz" : ""
              } lato-font-400`}
              onClick={() => {
                setMostrarMatriz(false);
               }}
            >
              Gráficos
            </div>
            <div
              className={`titulo-barra-Lateral-graficos ${
                mostrarMatriz ? "" : "unselect-matriz"
              } lato-font-400`}
              onClick={() => {
                setMostrarMatriz(true);
                 getLogs(null,null,null,"Reporte de Solicitudes","Solicitudes","Ver Matriz","Contratos","GET");

              }}
            >
              Matriz
            </div>
           
          </div>
          { !mostrarMatriz 
          && ( 
             <div className="filtros-nivel-menu">
              <Select
                options={tiposSolicitudFiltro}
                className="select-tiposolicitud-graficos"
                value={tiposSolicitudFiltro.find(
                  (option) => option === selectedTipoSolicitudFiltro
                )}
                onChange={handleChangeTipoSolicitudFiltro}
                placeholder="Tipo de Solicitud"
              />
              <Select
                options={empresasFiltro}
                className="select-empresa-graficos"
                value={empresasFiltro.find(
                  (option) => option === selectedEmpresaFiltro
                )}
                onChange={handleChangeEmpresaFiltro}
                placeholder="Empresa"
              />
              <select
                className="form-select"
                aria-label="Default select example"
                value={aniosSeleccionados.grafico4}
                onChange={handleAnioChange}
                style={{ width: "20%" }}
              >
                <option disabled>Año</option>
                {anios.map((anio) => (
                  <option key={anio} value={anio}>
                    {anio}
                  </option>
                ))}
              </select>
            </div>)} 
 
        
        </div>
      </div>
      <div
        className="div-seleccion-reportes-gestor"
        style={{ marginBottom: "0.5rem" }}
      >
 
          {!mostrarMatriz ?(<div className="div-graficos-Reportes-gestor">
            {isLoadingGraphics ? (
              <div className="loading-container" style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '60vh',
                width: '100%',
                backgroundColor: '#f8f9fa',
                borderRadius: '10px',
                margin: '1.25rem 0',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '40px',
                  backgroundColor: 'white',
                  borderRadius: '15px',
                  boxShadow: '0 4px 1.25rem rgba(0,0,0,0.15)',
                  minWidth: '300px'
                }}>
                  <div
                    className="spinner-border"
                    role="status"
                    style={{
                      width: '3rem',
                      height: '3rem',
                      color: '#2c4cb3',
                      borderWidth: '4px'
                    }}
                  >
                    <span className="visually-hidden">Cargando...</span>
                  </div>
                  <h5 style={{
                    marginTop: '1.25rem',
                    color: '#2c4cb3',
                    fontWeight: '600',
                    textAlign: 'center'
                  }}>
                    Cargando Reportes
                  </h5>
                  <p style={{
                    marginTop: '10px',
                    color: '#6c757d',
                    fontSize: '14px',
                    textAlign: 'center',
                    margin: '10px 0 0 0'
                  }}>
                    Obteniendo datos de gráficos y estadísticas...
                  </p>
                </div>
              </div>
            ) : (
              <>
                <Contadores
                  data={graphicsData.contadores}
                  isLoading={isLoadingGraphics}
                />

               <div className="contenedorGrafico-reportes-gestor">
                  {/* Primera fila de gráficos */}
                  <div className="fila-graficos">
                    <div className="grafico-col-8">
                      <div className="grafico-container">
                        <Grafico2
                          data={graphicsData.grafico2}
                          isLoading={isLoadingGraphics}
                          selectedEmpresaFiltro={selectedEmpresaFiltro}
                        />
                      </div>
                    </div>
                    <div className="grafico-col-4">
                      <div className="grafico-container">
                        <Grafico1
                          data={graphicsData.grafico1}
                          isLoading={isLoadingGraphics}
                          selectedEmpresaFiltro={selectedEmpresaFiltro}
                          aniosSeleccionados={aniosSeleccionados}
                          selectedTipoSolicitudFiltro={selectedTipoSolicitudFiltro}
                          idAplicacion={idAplicacion}
                          Suscripcion={Suscripcion}
                          idUsuario={idUsuario}
                          rol="Solicitante"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Segunda fila de gráficos */}
                  <div className="fila-graficos">
                    <div className="grafico-col-8">
                      <div className="grafico-container">
                        <Grafico3
                          data={graphicsData.grafico3}
                          isLoading={isLoadingGraphics}
                        />
                      </div>
                    </div>
                    <div className="grafico-col-4">
                      <div className="grafico-container">
                        <Grafico4
                          data={graphicsData.grafico4}
                          isLoading={isLoadingGraphics}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>) : (
            <div className="container-matriz-selector">
            <Matriz
               solicitudes={solicitudes}
              selectedFields={selectedFields}
              handleDownload={handleDownload}
              formatDate={formatDate}
              setFiltroMatrizEstado={setFiltroMatrizEstado}
          filtroMatrizEstado = {filtroMatrizEstado}
            />
            <ModalSeleccion
              Suscripcion={Suscripcion}
              idUsuario={idUsuario}
              handleAnioChange={handleAnioMatrizChange}
              anios={anios}
              aniosSeleccionados={aniosSeleccionados}
              handleFieldChange={handleFieldChange}
              selectedFields={selectedFields}
              fields={fields}
              fetchSolicitudes={fetchSolicitudes}
              handleBorrarFiltro={handleBorrarFiltro}
              handleInputChange={handleInputChange}
              codSolicitud={codSolicitud}
            />
          </div>
          )}
       </div>
    </div>
  );
};

export default Reportes;
