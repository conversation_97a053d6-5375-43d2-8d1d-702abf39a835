import React from "react";

interface ContadoresProps {
  data: {
    promedioAtencion: any;
    promedioPreparacion: any;
    promedioTotal: any;
    solicitudesTotal: any;
    contratosFirmados: any;
  };
  isLoading: boolean;
}

const Contadores: React.FC<ContadoresProps> = ({ data, isLoading }) => {

  
  return (
    <div className="container-contador-gestor">
      <div className="contador-estados-inicio-solicitante montserrat-font">
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Promedio total de atención</div>
          <div className="valor-contador-estados-IS montserrat-font-500">
            {data?.promedioAtencion || "0"}
          </div>
          <div className='titulo-conteo'>días</div>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Tiempo Promedio de firma</div>
          <div className="valor-contador-estados-IS montserrat-font-500">
            {data?.promedioPreparacion || "0"}
          </div>
          <div className='titulo-conteo'>días</div>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Tiempo promedio total</div>
          <div className="valor-contador-estados-IS montserrat-font-500">
            {data?.promedioTotal || "0"}
          </div>
          <div className='titulo-conteo'>días</div>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo-header'>Contratos trabajados por año</div>
          <div className="valor-contador-estados-IS montserrat-font-500">
            {data?.solicitudesTotal || "0"}
          </div>
          <div className='titulo-conteo'>Contratos</div>
        </div>
        <div className="estado-contador-IS">
          <div className='titulo-conteo-header'>Contratos Firmados</div>
          <div className="valor-contador-estados-IS montserrat-font-500">
            {data?.contratosFirmados || "0"}
          </div>
          <div className='titulo-conteo'>Contratos</div>
        </div>
      </div>
    </div>
  )
}

export default Contadores
