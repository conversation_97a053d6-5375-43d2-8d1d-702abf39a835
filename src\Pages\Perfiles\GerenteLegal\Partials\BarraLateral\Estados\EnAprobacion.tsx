import { Tooltip } from "@mui/material";
import axios from "axios"; // Para realizar solicitudes HTTP
import { useState } from "react"; // Para manejar el estado local
import Swal from "sweetalert2";
import API_APROBADOR from "../../../../../../assets/Api/ApisAprobador";
import ModalAceptar from "./Modals/ModalAceptar";
import ListaAprobadores from "./ListaAprobadores";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import Cookies from "js-cookie";
import { getTokenFromCookie, validateToken } from "../../../../../Components/Services/TokenService";
import IconoCheck from "../../../../../../assets/SVG/IconoCheck";
import IconoDownload from "../../../../../../assets/SVG/IconoDownload";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";

interface SolicitudSeleccionada {
  int_idSolicitudes: number;
  str_CodSolicitudes: string;
  nombre_TipoSolicitud: string;
}

interface EnAprobacion {
  solicitudSeleccionada: SolicitudSeleccionada;
  aprobadores: any[];
  idUsuario: string | number;
  SubmitObtenerDatos: () => void;
  obtenerAprobadores: () => void;
  Suscripcion: string | number;
}

const EnAprobacion: React.FC<EnAprobacion> = ({
  solicitudSeleccionada,
  aprobadores,
  idUsuario,
  SubmitObtenerDatos,
  obtenerAprobadores,
  Suscripcion,
  setSelectedSolicitud
  ,Suscriptor,idAplicacion
}) => {
  const [modales, setModales] = useState({
    isModalVisibleAceptar: false,
  });
  const token = Cookies.get("Token");
  const navigate = useNavigate();
  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const FirmarAprobación = async () => {
    await validateToken();
    try {
      await axios.post(
        API_SOLICITANTE["FirmarAceptacion"](),
        {
          str_idSuscriptor: Suscripcion,
          str_CodSolicitudes: solicitudSeleccionada.str_CodSolicitudes,
          int_idUsuarioCreacion: idUsuario,
          str_CodTipoDocumento: "COAP",
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return true;
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
      return false;
    }
  };

  const handleAprobarSolicitud = async () => {
    await validateToken();
    try {
      const response = await axios.put(
        API_APROBADOR["AprobarSolicitud"](
          solicitudSeleccionada.int_idSolicitudes,
          idUsuario,
          Suscripcion
        ), {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const firma = await FirmarAprobación();
      if (response.status === 200 && firma === true) {
        Swal.fire("", "Aprobador actualizado correctamente", "success");
        SubmitObtenerDatos();
        obtenerAprobadores();
        setSelectedSolicitud(solicitudSeleccionada);
        setModales((prev) => ({ ...prev, isModalVisibleAceptar: false }));
        
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        "Alguno de los aprobadores anteriores no ha aprobado aún la solicitud";

      if (errorMessage === "Aprobador no encontrado") {
        Swal.fire("", "El aprobador no fue asignado a esta solicitud", "error");
      } else {
        Swal.fire("", errorMessage, "error");
      }
    }
  };
  const EstadoRechazar = async () => {
    await validateToken();
    try {
      await axios.put(
        API_SOLICITANTE["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: "En Proceso",
          int_idUsuarioCreacion: idUsuario,
          int_idSolicitudes: solicitudSeleccionada.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );
      Swal.fire("Rechazado", "Documento Rechazado", "success");
      SubmitObtenerDatos();
      obtenerAprobadores();
      setSelectedSolicitud(solicitudSeleccionada);

    } catch (error) {
      console.error("Error al cambiar el estado:", error);
      Swal.fire("", "No se pudo cambiar el estado de la solicitud", "error");
    }
  };
  const handleRechazarSolicitud = async () => {
    await validateToken();
    try {
      const response = await axios.put(
        API_APROBADOR["RechazarSolicitud"](
          solicitudSeleccionada.int_idSolicitudes,
          idUsuario
        ),
        { idUsuario : idUsuario },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );
      if (response.status === 200) {
        Swal.fire("", "Aprobador actualizado correctamente", "success");
        EstadoRechazar();
        setModales((prev) => ({ ...prev, isModalVisibleAceptar: false }));

      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        "Alguno de los aprobadores anteriores no ha aprobado aún la solicitud";

      if (errorMessage === "Aprobador no encontrado") {
        Swal.fire("", "El aprobador no fue asignado a esta solicitud", "error");
      } else {
        Swal.fire("", errorMessage, "error");
      }
    }
  };
  const handleDownload = async () => {
    await validateToken();
    try {
      const response = await axios.get(
        API_APROBADOR["ListarArchivo"](
          Suscripcion,
          solicitudSeleccionada.str_CodSolicitudes
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie()}`,
          },
        }
      );
      if (response.data) {
        const response2 = await axios.get(
          API_APROBADOR["DescargarArchivo"](
            Suscripcion,
            solicitudSeleccionada.str_CodSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${getTokenFromCookie()}`,
            },
            responseType: "blob",
          }
        );
        const contentDisposition = response2.headers["content-disposition"];
        const filename = contentDisposition
          ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
          : `${response.data.nombre_archivo}`;

        const url = window.URL.createObjectURL(new Blob([response2.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error al descargar el archivo:", error);
      Swal.fire("", "No se pudo descargar el archivo", "error");
    }
  };
     const handleEditarSolicitud = () =>{
          navigate(RoutesPrivate.VERSOLICITUD, {state:{solicitudSeleccionada,Suscripcion,idAplicacion,idUsuario,Suscriptor, ver:true}})
        }
  return (
    <div className="barraLateral-inicio-GL">
      <span className="titulo-barra-Lateral lato-font-400">Resumen</span>
      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        {solicitudSeleccionada.tieneOrdenAprobacion === "No" ? (
          ""
        ) : (
          <Tooltip title="Aprobar Solicitud" placement="top">
          
            <div
              className="icono-barralateral-acciones"
              onClick={() => toggleModal('isModalVisibleAceptar')}
            > 
            <IconoCheck size={"1.3rem"} color={"#000"}/>
            </div>
          </Tooltip>
        )}
        <Tooltip title="Descargar" placement="top">
        <div
              className="icono-barralateral-acciones"
              onClick={handleDownload}
            > 
            <IconoDownload size={"1.3rem"} color={"#000"}/>
            </div>
        </Tooltip>
        <Tooltip title="Ver Solicitud" placement="top">
    
        <div
              onClick={handleEditarSolicitud}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
        </Tooltip>
      </div>
      <ListaAprobadores aprobadores={aprobadores} />
      <ModalAceptar
        isModalVisibleAceptar={modales.isModalVisibleAceptar}
        CerrarModalEliminar={() => toggleModal("isModalVisibleAceptar")}
        solicitudSeleccionada={solicitudSeleccionada}
        SubmitObtenerDatos={SubmitObtenerDatos}
        accion={handleAprobarSolicitud}
        handleRechazarSolicitud={handleRechazarSolicitud}
      />
    </div>
  );
};

export default EnAprobacion;
