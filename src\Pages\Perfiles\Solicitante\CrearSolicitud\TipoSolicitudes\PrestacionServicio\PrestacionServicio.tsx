import React from 'react'
import CrearPrestacionServicio from './Crear'
import EditarPrestacionServicio from './Editar'

const PrestacionServicio = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  esEdicion,
  selectedSolicitud,
  ver,
  NomTipoSolicitud
}) => {
  return (
    <>    
    {!esEdicion ? 
      <CrearPrestacionServicio Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} idTipoSolicitud={idTipoSolicitud} tipoModelo={tipoModelo} NomTipoSolicitud={NomTipoSolicitud} />
      :
      <EditarPrestacionServicio Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} selectedSolicitud={selectedSolicitud} tipoModelo={tipoModelo} esEdicion={esEdicion} ver={ver} NomTipoSolicitud={NomTipoSolicitud}/>

    }
    </>

  )
}

export default PrestacionServicio