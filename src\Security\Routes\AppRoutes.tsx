import { FC, useEffect, useState } from "react"; 
import { Routes, Route, BrowserRouter, Navigate } from "react-router-dom";
import { PrivateRoutes } from "./PrivateRoutes";
import Cookies from "js-cookie";
import axios, { isAxiosError } from "axios";
import { checkSesion, decrypt, encrypt, logout, validateToken } from "../../Pages/Components/Services/TokenService";
import Swal from "sweetalert2";

const AppRoutes: FC = () => {
  const [verificado, setVerificado] = useState(false);
  const [rol, setRol] = useState("");
  const [ipAddress, setIpAddress] = useState("");

  const getCookieOrParam = (
    param: string,
    cookieName: string
  ): string | null => {
    const params = new URLSearchParams(window.location.search);
    const value =
      params.get(param) ||
      (Cookies.get(cookieName) ? decrypt(Cookies.get(cookieName)) : null);
    if (value) {
      Cookies.set(cookieName, encrypt(value));
    }
    return value;
  };
 
  const baseUrl =  import.meta.env.VITE_BASE_URL;
  const baseSeguridad = import.meta.env.VITE_SEGURIDAD_URL;
  const paginaSeguridad = import.meta.env.VITE_REDIR_SEGURIDAD;

  const idAplicacion = getCookieOrParam("idAplicacion", "idAplicacion");
  const suscriptor = getCookieOrParam("Suscriptor", "suscriptor");
  const correoUser = getCookieOrParam("correoUser", "correoUser");
  const sesion = getCookieOrParam("sesion", "sesion");
  const suscripcion = getCookieOrParam("Suscripcion", "suscripcion");
  const app = getCookieOrParam("app", "app");
  const params = new URLSearchParams(window.location.search);
  const value =  params.get("sesion")
  if (value) {
    localStorage.removeItem("isVerified");
  } 

  useEffect(() => {
    const fetchIpAddress = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setIpAddress(data.ip);
      } catch (error) {
        console.error("Error al obtener la IP:", error);
      }
    };
    fetchIpAddress();
  }, []);

  useEffect(() => {
    const VerificacionSeguridad = async () => {
      try {
        if (localStorage.getItem("isVerified")) {
          await validateToken();
          await VerificarSesion()




          setVerificado(true);
          setRol(decrypt(Cookies.get("rol")) || "");
          return
        }
  
        const response = await axios.get(`${baseSeguridad}seguridad/consultar/sesion/${sesion}/`);
        if (isAxiosError(response)) {
          logout();
          return;
        }
        if (response.status >= 200 && response.status < 300) {
          const responseSeguridad = await axios.get(`${baseUrl}api/get-token/`, {
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${response.data.str_token}`
            }
          });
  
          if (responseSeguridad.status >= 200 && responseSeguridad.status < 300) {
            Cookies.set("Token", responseSeguridad.data.token);
            Cookies.set("refreshToken", responseSeguridad.data.refresh_token);
  
            await validateToken();
  
            try {
              const respuestaAppsAsignadas = await axios.get(
                `${baseSeguridad}asignacion_aplicacion/suscriptor/${suscriptor}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
  
              const dataRespuestaAppsAsignadas = respuestaAppsAsignadas.data;
              
              if (
                !dataRespuestaAppsAsignadas.some(
                  (app) => app.int_idAplicacion === Number(idAplicacion)
                )
              ) {
                return;
              }

  
              const { data: userData } = await axios.get(
                `${baseSeguridad}usuarios/correo/${correoUser}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
  
              if (!userData) {
                logout();
                return;
              }
              console.log(userData)
              Cookies.set("nombres", encrypt(userData.str_Nombres));
              Cookies.set("apellidos", encrypt(userData.str_Apellidos));
              Cookies.set("correo", encrypt(userData.str_Correo));
              Cookies.set("hora_llegada", encrypt(userData.int_idUsuarios.toString()) || "");
              localStorage.setItem("fotoPerfil", userData.str_RutaFoto || "");
              const { data: perfilData } = await axios.get(
                `${baseSeguridad}perfiles/asignado/aplicacion/${idAplicacion}/usuario/${userData.int_idUsuarios}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
  
              if (perfilData) {
                setRol(perfilData.str_Nombre);
                setVerificado(true);
                Cookies.set("rol", encrypt(perfilData.str_Nombre));
                localStorage.setItem("isVerified", "true");
              } else {
                logout();
              }
            } catch (error) {
              if (error.response?.status === 401) {
                localStorage.removeItem("isVerified");
                VerificacionSeguridad();
              } else {
                console.error("Error al verificar aplicaciones:", error);
              }
            }
          }
        }
      } catch (error) {
        console.error(error);

        // window.location.replace("${paginaSeguridad}auth?app=prisma");
      }
    };
  
    VerificacionSeguridad();
  }, [ipAddress]);
  
  const VerificarSesion = async () => {
    const response = await checkSesion(sesion);

    if (response.data.dt_FechaCierre != null) {
      Swal.fire({
        title: "Fin de sesión",
        text: "Su sesión se ha cerrado",
        icon: "info",
        allowOutsideClick: false,
        confirmButtonText: "OK",
      }).then(() => {
        logout();
      });
    }else{
      console.log("ok")
    }
  };
  useEffect(() => {
    const interval = setInterval(() => {
      VerificarSesion();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <BrowserRouter>
      <Routes>
        {verificado && (
          <>
            <Route path="/*" element={<PrivateRoutes perfil={rol} />} />
            <Route index element={<Navigate to="/Prisma-Contratos/Inicio-Solicitante" />} />
          </>
        )}
      </Routes>
    </BrowserRouter>
  );
};

export { AppRoutes };