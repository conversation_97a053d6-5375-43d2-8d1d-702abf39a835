import React, { useState } from "react";
import { Tooltip } from "@mui/material";
import HistorialSolicitud from "../../../../Solicitante/Partials/BarraLateral/HistorialSolicitud";
import ModalAceptar from "../../Modales/ModalAceptar";
import ModalAprobadores from "../../Modales/ModalAprobadores";
import API_SOLICITANTE from "../../../../../../assets/Api/ApisSolicitante";
import { decrypt, validateToken } from "../../../../../Components/Services/TokenService";
import axios from "axios";
import IconoCheck from "../../../../../../assets/SVG/IconoCheck";
import { useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../../../../../Security/Routes/ProtectedRoute";
import IconoVer from "../../../../../../assets/SVG/IconoVer";
import Cookies from "js-cookie";

interface EstadoNuevoProps {
  solicitudSeleccionada: {
    int_idGestor: number;
    int_SolicitudGuardada: number;
    int_idSolicitudes: number;
    str_CodSolicitudes:string
  };
  historiales: any[]; 
  idAplicacion: number;
  Suscripcion: string;
  idUsuario: number;
  SubmitObtenerDatos: ()=>void
  setSelectedSolicitud: ()=>void

}

const Aceptado: React.FC<EstadoNuevoProps> = ({
  historiales,
  solicitudSeleccionada,
  Suscripcion,
  idAplicacion,
  idUsuario,
  SubmitObtenerDatos,
  setSelectedSolicitud,
  aprobadores
}) => {
  const navigate = useNavigate();
  const [modales, setModales] = useState({
    isModalVisibleAceptar: false,isModalVisible: false
  });
    const Suscriptor = decrypt(Cookies.get("suscriptor"));

  const toggleModal = (modal: keyof typeof modales) => {
    setModales((prev) => ({ ...prev, [modal]: !prev[modal] }));
  };
  const token = localStorage.getItem("token");
  const fechaActual = new Date().toISOString();
  // const Aceptar = async () => {
  //   await validateToken();
  //   try {
  //     await axios.post(API_SOLICITANTE["FirmarAceptacion"](), {
  //       str_idSuscriptor: Suscripcion,
  //       str_CodSolicitudes: solicitudSeleccionada.str_CodSolicitudes,
  //       int_idUsuarioCreacion: idUsuario,
  //       str_CodTipoDocumento: "COAP",
  //     },{
  //       headers: {
  //         "Content-Type": "application/json",
  //         "Authorization": `Bearer ${token}`
  //       }
  //     });
  //   } catch (error) {
  //     return false
  //   }
  // };
  const AsignarAprobadores = () =>{
 
    toggleModal('isModalVisibleAceptar')
    toggleModal('isModalVisible')
  }
    const handleVerSolicitud = () => {
      navigate(RoutesPrivate.EDITARSOLICITUD, {
        state: {
          solicitudSeleccionada,
          Suscripcion,
          idAplicacion,
          idUsuario,
          Suscriptor,
          ver: true,
        },
      });
    };
  return (
   <div className={`conteo-inicio-gestor-pageSolicitudes`}>

      <span className="subtitulo-barra-Lateral lato-font-400">Acciones</span>
      <div className="opcionesSolicitud-barra-Lateral">
        <Tooltip title="Solicitar Aprobación" placement="top" >
        <div
              className="icono-barralateral-acciones"
              onClick={() => toggleModal('isModalVisibleAceptar')}
            > 
            <IconoCheck size={"1.3rem"} color={"#000"}/>
            </div>
        </Tooltip>
       <Tooltip title="Ver Solicitud" placement="top">
            <div
              onClick={() => handleVerSolicitud()}
              style={{ cursor: "pointer" }}
            >
              {" "}
              <IconoVer size=" 1.3rem" color="#4B4B4B" />
            </div>
          </Tooltip>
      </div>
      
      <HistorialSolicitud historiales={historiales} aprobadores={aprobadores}/>

      <ModalAceptar
        isModalVisibleAceptar={modales.isModalVisibleAceptar}
        CerrarModalEliminar={() => toggleModal('isModalVisibleAceptar')}
        solicitudSeleccionada={solicitudSeleccionada}

        accion={AsignarAprobadores}
      />
      <ModalAprobadores
       isModalVisible={modales.isModalVisible}
       CerrarModal={() => toggleModal('isModalVisible')}
       Suscripcion={Suscripcion}
       idAplicacion={idAplicacion}
       solicitudSeleccionada={solicitudSeleccionada}
       historiales={historiales}
       fecha={fechaActual}
       idUsuario={idUsuario}
       SubmitObtenerDatos={SubmitObtenerDatos}
       setSelectedSolicitud={setSelectedSolicitud}
      />
      </div>
      
  );
};

export default Aceptado;