import React, { useEffect, useState } from 'react';
import API_SOLICITANTE from '../../../../../assets/Api/ApisSolicitante';
import axios from 'axios';

interface Solicitud {
  nombre_gestor: string;
}

interface GestorAsignadoProps {
  solicitudSeleccionada: {
    int_idSolicitudes: number;
    int_idGestor: number | null; 
    nombre_completo:string|null
  };
}

const SolicitanteAsignado: React.FC<GestorAsignadoProps> = ({ solicitudSeleccionada }) => {

  return (
    <>
      {solicitudSeleccionada.nombre_completo ? (
        <>
          <span className="subtitulo-barra-Lateral lato-font-400">Solicitante</span>
          <span className="nombre-solicitante-barra-lateral">
            {solicitudSeleccionada?.nombre_completo}
          </span>
        </>
      ) : null}
    </>
  );
};

export default SolicitanteAsignado;