import React from 'react'
import { RoutesPrivate } from '../../../../../Security/Routes/ProtectedRoute';
import { useNavigate } from 'react-router-dom';
import IconoRight from '../../../../../assets/SVG/IconoRight';
type DataItem = {
  estado_nombre: string;
};

type ResumenConteoProps = {
  data: DataItem[];
};
const ResumenConteo: React.FC<ResumenConteoProps> = ({ data }) => {
  const navigate = useNavigate();
  const nuevoCount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Nuevo").length;
  const asignadocount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Asignado").length;
  const enprocesocount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Proceso").length;
  const envalidacioncount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Validación").length;
  const FirmadoCount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Firmado").length;
  const Aceptadocount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aceptado").length;
  const EnAprobacioncount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "En Aprobación").length;
  const Aprobadocount = data.filter((item: { estado_nombre: string; }) => item.estado_nombre === "Aprobado").length;

  return (
      <div className="contador-estados-inicio-solicitante montserrat-font">
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Solicitudes Asignadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{asignadocount}</div>
          <span className='color-estado-IS-Asignadas'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Solicitudes En Proceso</div> 
          <div  className="valor-contador-estados-IS montserrat-font-500">{enprocesocount}</div>
          <span className='color-estado-IS-EnProceso'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Solicitudes En Validación</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{envalidacioncount}</div>
          <span className='color-estado-IS-EnValidacion'> </span>
        </div>
        <div className="estado-contador-IS" style={{borderRight: "1px solid #C4C4C4"}}>
          <div className='titulo-conteo'>Solicitudes Aprobadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{Aprobadocount}</div>
          <span className='color-estado-IS-Aprobadas'> </span>
        </div>
        <div className="estado-contador-IS">
          <div className='titulo-conteo'>Solicitudes Firmadas</div> 
          <div className="valor-contador-estados-IS montserrat-font-500">{FirmadoCount}</div>
          <span className='color-estado-IS-Firmadas'> </span>
        </div>
        <div className="estado-flecha-estadisticas" onClick={() => navigate(RoutesPrivate.REPORTESSOLICITANTE )}>
              <IconoRight size={"2rem"} selected={true} colorselected={"#156CFF"} color={"#156CFF"} />
            </div>
      </div>
  );
}
{/* <li>
          <span>Asignados</span>
          <span className="montserrat-font-500">{asignadocount}</span>
        </li>
        <li>
          <span>En Proceso</span>
          <span className="montserrat-font-500">{enprocesocount}</span>
        </li>
        <li>
          <span>En Validación</span>
          <span className="montserrat-font-500">{envalidacioncount}</span>
        </li>
        <li>
          <span>Aceptado</span>
          <span className="montserrat-font-500">{Aceptadocount}</span>
        </li>
        <li>
          <span>En Aprobación</span>
          <span className="montserrat-font-500">{EnAprobacioncount}</span>
        </li>
        

        <li>
          <span>Aprobado</span>
          <span className="montserrat-font-500">{Aprobadocount}</span>
        </li>
        <li>
          <span>Firmado</span>
          <span className="montserrat-font-500">{FirmadoCount}</span>
        </li> */}
export default ResumenConteo