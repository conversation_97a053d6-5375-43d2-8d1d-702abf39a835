/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import Header from "../../Components/Partials/Header/Header";
import Titulo from "../../Components/Partials/Seccion/Titulo";
import Cookies from "js-cookie";
import "./Inicio.css";
import axios from "axios";
import CircleEstado from "../../../assets/SVG/CircleEstado";
import ResumenConteo from "./Partials/BarraLateral/ResumenConteo";
import OtrosEstados from "./Partials/BarraLateral/OtrosEstados";
import { Tooltip } from "@mui/material";
import API_APROBADOR from "../../../assets/Api/ApisAprobador";
import { decrypt, validateToken } from "../../Components/Services/TokenService";
import IconoLeft from "../../../assets/SVG/IconoLeft";
import IconoRight from "../../../assets/SVG/IconoRight";

// Definición de tipos
interface Solicitud {
  str_CodSolicitudes: string;
  dt_FechaRegistro: string | number | Date;
  dt_FechaEsperada: string | number | Date;
  str_NombreEmpresa: string | any;
  db_Honorarios: number;
  estado_nombre: string;
  str_DeTerceros: string;
  int_idSolicitudes?: number; // Puede no estar presente en algunas solicitudes
}

const formatDate = (dateString: string | number | Date) => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("es-ES", { month: "long" });
  const year = date.getFullYear();

  return `${day} de ${month} de ${year}`;
};

const Inicio: React.FC = () => {
  const Nombres = decrypt(Cookies.get("nombres"));
  const Apellidos = decrypt(Cookies.get("apellidos"));
  const idUsuario = decrypt(Cookies.get("hora_llegada"));
  const Suscripcion = decrypt(Cookies.get("suscripcion"));
  const Suscriptor = decrypt(Cookies.get("suscriptor"));
  const idAplicacion = decrypt(Cookies.get("idAplicacion"));
  const token = Cookies.get("Token");

  const [selectedSolicitud, setSelectedSolicitud] = useState<Solicitud | null>(null);
  const [solicitudes, setSolicitudes] = useState<Solicitud[]>([]);
  const [aprobadores, setAprobadores] = useState<any[]>([]); 
  
  /* PAGINACION */
  const [currentPage, setCurrentPage] = useState<number>(1);
  const solicitudesPorPagina = 8;
  const indiceUltimaSolicitud = currentPage * solicitudesPorPagina;
  const indicePrimeraSolicitud = indiceUltimaSolicitud - solicitudesPorPagina;
  const solicitudesActuales = solicitudes.slice(indicePrimeraSolicitud, indiceUltimaSolicitud);
  
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
  
  const totalPaginas = Math.ceil(solicitudes.length / solicitudesPorPagina);

  const renderNumeritosPaginacion = () => {
    const numeritos = [];
    for (let i = 1; i <= totalPaginas; i++) {
      numeritos.push(
        <button
          key={i}
          className={`numero-pagina ${currentPage === i ? "activo" : ""}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }
    return numeritos;
  };

  const obtenerSolicitudesUsuario = async () => {
await validateToken();
    try {
      const response = await axios.get(
        API_APROBADOR["ObtenerSolicitudesAprobador"](Suscripcion, idUsuario)
        ,{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        });
      setSolicitudes(response.data);
    } catch (error: any) {
      return error    }
  };

  useEffect(() => {
    obtenerSolicitudesUsuario();
  }, []);

  const SubmitObtenerDatos = () => {
    obtenerSolicitudesUsuario();
  };

  const obtenerAprobadores = async () => {
    setAprobadores([]);
await validateToken();
    if (selectedSolicitud?.int_idSolicitudes) {
      try {
        const response = await axios.get(API_APROBADOR["ListarAprobadores"](Suscripcion, selectedSolicitud.int_idSolicitudes),{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          }
        });
        const aprobadores = response.data;
        setAprobadores(aprobadores);
      } catch (error) {
        setAprobadores([]);
        return error      
      }
    }
  };


  useEffect(() => {
    obtenerAprobadores();
  }, [selectedSolicitud]);

  const CambiarBarraLateral = (solicitud: Solicitud) => {
    setSelectedSolicitud(solicitud);
  };

  return (
    <div className="global-inicio-solicitante">
      <Header Nombres={Nombres} Apellidos={Apellidos} />
      <Titulo seccion={`Bienvenido ${Nombres} ${Apellidos} `} />
      <div className="div-container-tabla-inicio-solicitante">
        <div className="div-tabla-inicio-solicitante">
          <table className="tabla-inicio-solicitante">
            <thead>
              <tr>
                <th>Num. Solicitud</th>
                <th>Fech. Solicitud</th>
                <th>Fech. Esperada</th>
                <th>Empresa</th>
                <th>Monto del contrato</th>
                <th>Estado</th>
              </tr>
            </thead>
            <tbody>
              {solicitudesActuales.length === 0 ? (
                <tr>
                  <td colSpan={6} style={{ textAlign: "center" }}>
                    No se encontraron registros
                  </td>
                </tr>
              ) : (
                solicitudesActuales.map((solicitud, index) => (
                  <tr key={index} onClick={() => CambiarBarraLateral(solicitud)}>
                    <td>
                      {solicitud.str_DeTerceros === "si" ? (
                        <Tooltip title="Modelo de Terceros" placement="top">
                          <span className="identificador-modelo">.</span>
                        </Tooltip>
                      ) : (
                        ""
                      )}
                      {solicitud.str_CodSolicitudes}
                    </td>
                    <td>{formatDate(solicitud.dt_FechaRegistro)}</td>
                    <td>{solicitud.dt_FechaEsperada === null || solicitud.dt_FechaEsperada === ""  ? "": formatDate(solicitud.dt_FechaEsperada) }</td>
                    <td className="colum-empresa-tabla-solicitante">
                      <Tooltip title={solicitud.str_NombreEmpresa} placement="top-start">
                        {solicitud.str_NombreEmpresa}
                      </Tooltip>
                    </td>
                    <td>
                        {solicitud.db_Honorarios === "0" ||
                        solicitud.db_Honorarios === null ||
                        solicitud.db_Honorarios === 0
                          ? ""
                          : solicitud.str_Moneda === "dolares"
                          ? `$ ${Number(
                              solicitud.db_Honorarios
                            ).toLocaleString("es-PE")}`
                          : 
                          `${solicitud.str_SimboloMoneda} ${Number(
                              solicitud.db_Honorarios
                            ).toLocaleString()}`
                          }
                      </td>
                    <td className="div-estado">
                      <span>{solicitud.estado_nombre}</span>
                      <CircleEstado estado={solicitud.estado_nombre} />
                    </td>
                  </tr>
                ))
              )}
            </tbody>
            <tfoot>
              <tr>
                <td colSpan={6}>
                  <div className="paginacion-tabla-solicitante">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="numero-pagina"
                    >
                                            <IconoLeft  size={"1.3rem"} color={"#000"}/>

                    </button>
                    {renderNumeritosPaginacion()}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={indiceUltimaSolicitud >= solicitudes.length}
                      className="numero-pagina"
                    >
                      <IconoRight size={"1.5rem"} color={"#000"}/>
                    </button>
                  </div>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
        <div className="barraLateral-inicio-solicitante">
          <span className="titulo-barra-Lateral lato-font-400">Resumen</span>
          {selectedSolicitud?.estado_nombre === "En Aprobación" ? (
            <OtrosEstados
              solicitudSeleccionada={selectedSolicitud}
              aprobadores={aprobadores}
              idUsuario={idUsuario}
              obtenerSolicitudesUsuario={SubmitObtenerDatos}
              obtenerAprobadores={obtenerAprobadores}
              Suscripcion={Suscripcion}
              setSelectedSolicitud={setSelectedSolicitud}
              Suscriptor={Suscriptor}
              idAplicacion={idAplicacion}
            />
          ) : (
            <ResumenConteo data={solicitudes} />
          )}
        </div>
      </div>
    </div>
  );
};

export default Inicio;