import React, { useState, useEffect, useRef } from "react";
import "./ChatBot.css";
import IconoChatBot from "../src/assets/Img/chatbot.png";
import Cookies from "js-cookie";
import axios from "axios";
import chatbotSaludo from "../src/assets/Img/chatbot-saludo.png";
import avatarBot from "../src/assets/Img/avatar bot.png";
import GifSaludo from "../src/assets/Img/gif bot.gif";

import IconoMenu from "./assets/SVG/IconoMenu";
import API_GESTOR from "./assets/Api/ApisGestor";
import Swal from "sweetalert2";
import { decrypt } from "./Pages/Components/Services/TokenService";
const añoactual = new Date().getFullYear();
interface Message {
  sender: "user" | "bot";
  text: string | JSX.Element;
}
const baseURL=import.meta.env.VITE_BASE_URL
const ChatBot: React.FC = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  console.log(messages.length);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [datos, setDatos] = useState([]);
  const chatEndRef = useRef<HTMLDivElement>(null);
   const nombres = decrypt(Cookies.get("nombres")) || "Usuario";
  const apellidos = decrypt(Cookies.get("apellidos")) || "";
  const idUsuario = decrypt(Cookies.get("hora_llegada")) || "";
  const suscripcion = decrypt(Cookies.get("suscripcion")) || "";
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };
  const [key, setKey] = useState("");
  const [codigoGenerado, setCodigoGenerado] = useState("");
  const generateCode = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';
    for (let i = 0; i < 40; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    setCodigoGenerado(code);
  };
  useEffect(() => {

  
    generateCode();
  }, []);
  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: "smooth", block: "end" });
    }
  }, [messages, isChatOpen]);
  const obtenerDatos = async () => {
    try {
      const response = await axios.get(
        `${baseURL}api/solicitudes/data/bot/?str_idSuscriptor=${suscripcion}&int_idGestor=${idUsuario}`
      );
      console.log(response);
      setDatos(response.data);
    } catch (error) {
      
    }
  };
  const obtenerKey = async () => {
    try {
      const response = await axios.get(
        API_GESTOR["ObtenerKeysBot"](suscripcion)
      );
      if(response.data.length > 0){
      const data = response.data[0];
      setKey(data.str_claveApi);
      } else{
        Swal.fire("", "No se encontró una clave de acceso para el chatbot", "error");
      }
    } catch (error) {
      console.error("Error al obtener los datos:", error);
    }
  };
  useEffect(() => {
    obtenerDatos();
  }, []);
  useEffect(() => {
    if (isChatOpen) {
      obtenerKey();
    }

  },[isChatOpen])
  const handleDowndloadManual = async () => {
    const base_url = import.meta.env.VITE_BASE_URL
     const fileUrl = `${base_url}api/archivo/download-manual-usuario/?nombre_manual=contratos.pptx`;
      window.open(fileUrl, '_blank');
      
  };
  const handleSendMessage = async () => {
    if (input.trim() === "") return;

    const userMessage: Message = { sender: "user", text: input };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    if (input.trim().toLowerCase().includes("ayuda")) {
      const helpMessage: Message = {
        sender: "bot",
        text: (
          <div className="help-buttons">
            <button
              onClick={() => {
                handleDowndloadManual();
              }}
            >
              Descargar Manual de Usuario
            </button>
            <button
               onClick={() => {
                const userButtonMessage: Message = {
                  sender: "user",
                  text: "Contáctar con Soporte",
                };
                setMessages((prevMessages) => [...prevMessages, userButtonMessage]);

                const userButtonMessage2: Message = {
                  sender: "bot",
                  text: <div className="contactame-con-asesor">
                        <div className="titulo-contactame">¡Contáctate con Soporte Técnico!</div>
                        <div className="">Whatsapp: 999999999</div>
                        <div className="">Horario de Atención : 9:00am - 5:00pm</div>
                        <div className="boton-asesor">Ir a Whatsapp</div>
                  </div>
                };
                setMessages((prevMessages) => [...prevMessages, userButtonMessage2]);
              }}
            >
             Contáctar con Soporte
            </button>
          </div>
        )
      };
      setMessages((prevMessages) => [...prevMessages, helpMessage]);
      setInput("");
      return;  
    }
    setInput("");
    setIsLoading(true);
    const isPreguntaSobreRegistros = (texto: string) => {
      const palabrasClave = [
        "solicitud",
        "registro",
        "contrato",
        "registros",
        "contratos",
        "solicitudes",
        "fecha",
        "Procesos",
        "Proceso"
      ];
      return palabrasClave.some((palabra) =>
        texto.toLowerCase().includes(palabra)
      );
    };

    const incluirDatos = isPreguntaSobreRegistros(input);
    console.log("incluirDatos:", incluirDatos);
    console.log(datos)
    try {
      const response = await axios.post(
        "http://44.206.69.118/IA/agente/consultar/contexto/",
        {
          // contexto: messages.map((msg) => ({
          //   role: msg.sender === "user" ? "user" : "assistant",
          //   content: msg.text,
          // })),
          pregunta: input,
          sesion: codigoGenerado,
          contexto: `
              Eres una asesora legal virtual llamada greta de una aplicación llamada prisma legal de perú.
              El cliente se llama  ${nombres} ${apellidos}. 
              Limitate a responder de manera precisa, directa y lo más breves posible, sin mucho texto de relleno, manteniendo un tono profesional.
              Limítate a responder solamente preguntas de derecho,o a cerca de la data del usuario dentro de la aplicación. 
              En caso el usuario pregunte sobre sus solicitudes sacar la información de esta respuesta json , en caso el usuario no mencione el año en la pregunta , responderle según el general y detallale por año; y si te envía la fecha ,sacarle los datos de esa fecha según el campo Fecha Registro, toda la información se puede detallar si el cliente lo pide,tener en cuenta que solicitudes o registros abiertos se les considera a aquellas solicitudes que no tienen como estado firmado o nuevo,además si el usuario pregunta por contratos que terminan en alguna fecha o mes , basarse en el campo fecha fin,además tener en cuenta que cuando la columna Cod.Solicitud tiene un -A en el código , signfica que es adenda,y cuando tiene un -EJ significa Contrato ExtraJudicial aquí está la información (solo si es necesario): ${
                incluirDatos
                  ? JSON.stringify(datos)
                  : "La data no se enviará, ya que la pregunta no está relacionada con los registros."
              } `,
        },
        {
          headers: {
            "Content-Type": "application/json",
            // Authorization: `Bearer ${key}`,
          },
        }
      );

      const botMessage: Message = {
        sender: "bot",
        text: response.data ,
      };
      setMessages((prevMessages) => [...prevMessages, botMessage]);
    } catch (error) {
      Swal.fire("", "Hay un error al acceder al chatbot , intente revisar su Clave", "error");
      const errorMessage: Message = {
        sender: "bot",
        text: "Lo siento, ocurrió un error. Inténtalo de nuevo más tarde.",
      };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEliminarChat = async () => {
    try{
      const response = await axios.delete(
        "http://44.206.69.118/IA/agente/eliminar/historial/",
        {
          sesion: codigoGenerado
          
        },
 
      );
           // Aplica la clase fade-out a cada mensaje
    const chatMessages = document.querySelectorAll(".message");
    chatMessages.forEach((msg) => {
      msg.classList.add("fade-out");
    });

    // Después de que termine la animación (1 segundo), limpia los mensajes
    setTimeout(() => {
      generateCode();
      setMessages([]);
    }, 1000); // El tiempo debe coincidir con la duración de la animación
     }catch (error) {
      
    }

  };
  return (
    <div>
      <button className="floating-button" onClick={toggleChat}>
        <img src={IconoChatBot} alt="Chatbot Icon" />
      </button>
      <div className={`chat-window ${isChatOpen ? "open" : ""}`}>
        
        {messages.length > 0 ? (
          <div className="chat-header">
          <div className="header-imagenbot">
            <div className="imagen-header">
              <img src={avatarBot} alt="Chatbot Icon" />
            </div>
            <div className="texto-header">
              <span>Greta</span>
              <span>Asesora Virtual</span>
            </div>
          </div>
          <div className="div-close-button">
            <div className="dropdown">
              <button
                className="boton-lista-bot"
                type="button"
                id="dropdownMenu2"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <IconoMenu />
              </button>
              <ul className="dropdown-menu" aria-labelledby="dropdownMenu2">
                {/* <li>
                  <button className="dropdown-item" type="button">
                    Subir Archivo
                  </button>
                </li>
                <li>
                  <button className="dropdown-item" type="button">
                    Subir Imagen
                  </button>
                </li> */}
                <li>
                  <button
                    className="dropdown-item"
                    type="button"
                    onClick={handleEliminarChat}
                  >
                    Borrar Chat
                  </button>
                </li>
              </ul>
            </div>
            <button
              className="btn-close btn-close-white"
              aria-label="Close"
              onClick={toggleChat}
            ></button>
          </div>
        </div>
        ): (
          <div className="chat-header" style={{backgroundColor: "#fff"}}>
          <div className="header-imagenbot">
            
          </div>
          <div className="div-close-button">
      
            <button
              className="btn-close btn-close-info"
              aria-label="Close"
              onClick={toggleChat}
            ></button>
          </div>
        </div>
        )}
        <div className="chat-body">
          {messages.length > 0 ? (
            messages.map((msg, index) => (
              <div
                key={index}
                className={`message ${msg.sender === "user" ? "user" : "bot"}`}
              >
                {msg.text}
              </div>
            ))
          ) : (
            <div className="chat-body">
              <div className="container-chat-vacio">
                <div className="div-imagen-chatbot-saludo">
                  <img src={GifSaludo} alt="" />
                </div>
                <div className="texto-chatbot-saludo">
                  Bienvenido {nombres} {apellidos} 😊 <br />
                  Soy Greta. <br />
                  ¿En qué puedo ayudarte hoy?
                </div>
              </div>
            </div>
          )}
          {isLoading && (
            <div className="message bot">
              <div className="loading-indicator">
                <div className="spinner"></div>
              </div>
            </div>
          )}
           <div ref={chatEndRef} />
        </div>
        <div className="chat-input">
          <input
            type="text"
            placeholder="Escribe un mensaje..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
          />
          <button onClick={handleSendMessage} disabled={isLoading}>
            {isLoading ? "Generando..." : "Enviar"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
