import CrearArrendamientoBienes from "./Crear"
import EditarArrendamientoBienes from "./Editar"



const ArrendamientoBienes = ({
  Nombres,
  Apellidos,
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  idTipoSolicitud,
  tipoModelo,
  esEdicion,
  selectedSolicitud,
  ver,
  NomTipoSolicitud
}) => {
  return (
    <>    
    {!esEdicion ? 
      <CrearArrendamientoBienes Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} idTipoSolicitud={idTipoSolicitud} tipoModelo={tipoModelo} NomTipoSolicitud={NomTipoSolicitud} />
      :
      <EditarArrendamientoBienes Nombres={Nombres} Apellidos={Apellidos} UsuarioId={UsuarioId} Suscriptor={Suscriptor} idAplicacion={idAplicacion} Suscripcion={Suscripcion} selectedSolicitud={selectedSolicitud} tipoModelo={tipoModelo} esEdicion={esEdicion} ver={ver} NomTipoSolicitud={NomTipoSolicitud}/>

    }
    </>

  )
}

export default ArrendamientoBienes