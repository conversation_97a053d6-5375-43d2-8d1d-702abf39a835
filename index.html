<!doctype html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/Icono-Logo.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">   
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/styles.css">

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PRISMA CONTRATOS</title>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ME40XY1S2C"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-ME40XY1S2C');
</script>
  </head>
  <body class="body-principal-web">
    
    <div id="root"></div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://kit.fontawesome.com/f8fd29cd17.js" crossorigin="anonymous"></script>
    <script type="module" src="/src/main.tsx"></script>
    <!-- <script src="https://app.embed.im/snow.js" defer></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/gh/pushlytech/falling-leaves/falling-leaves.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/fireworks-js@latest/dist/fireworks.js" ></script>
    <script  >
      document.addEventListener("DOMContentLoaded", () => {
        const container = document.body;
        const fireworks = new Fireworks(container, { 
          speed: 2, particles: 100
        });
        fireworks.start();
      });
    </script> -->
  </body>
</html>
