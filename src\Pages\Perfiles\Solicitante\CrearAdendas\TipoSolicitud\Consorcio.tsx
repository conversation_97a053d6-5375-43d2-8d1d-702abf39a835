import React, { useEffect, useState } from "react";
import { <PERSON>, Step, Step<PERSON>abel, Stepper } from "@mui/material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import axios from "axios";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import { validateToken } from "../../../../Components/Services/TokenService";
import API_SOLICITANTE from "../../../../../assets/Api/ApisSolicitante";
import API_GESTOR from "../../../../../assets/Api/ApisGestor";

import BarraLateralCrearAdenda from "../BarraLateralCrearAdenda/BarraLateralCrearAdenda";
import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
import Cookies from "js-cookie";
import IconoBasureroEliminar from "../../../../../assets/SVG/IconoBasureroEliminar";
import IconoDolares from "../../../../../assets/SVG/IconoSoles";
import IconoMoneda from "../../../../../assets/SVG/IconoMoneda";
import getLogs from "../../../../Components/Services/LogsService";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("America/Lima");

interface Consorcio {
  UsuarioId: number | null;
  idAplicacion: number | null;
  Suscriptor: number | null;
  Suscripcion: string | null;
  selectedSolicitud: object | null;
  Asignado: boolean;
}

interface FormDataSolicitud {
  int_idUsuarioModificacion: number | string;
  str_idSuscriptor: string;
  int_idEmpresa: string;
  int_idUnidadNegocio: string;
  int_idTipoSol: number;
  int_SolicitudGuardada: null | number;
  str_DeTerceros: string;
  dt_FechaEsperada: string;
  db_Honorarios: number;
}

interface FormDataContenido {
  str_DocAdjuntos: string;
  str_ObjetivoContrato: string;
  str_ObligacionesConjuntas: string;
  str_TipoServicio: string;
  str_InfoAdicional: string;
  int_idInterlocutor: number | null;
  int_idInterlocutorComprador: number | null;
  str_idSuscriptor: string;

  str_RentaPactada: string;
  str_Moneda: string;
  str_BienPartidaCertificada: string;
  dt_FechaArriendo: string;
}
const addDaysToDate = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split("T")[0]; // Formato `YYYY-MM-DD`
};
const getLocalDate = () => {
  const today = new Date();
  today.setMinutes(today.getMinutes() - today.getTimezoneOffset()); // Ajusta la fecha a la zona horaria local
  return today.toISOString().split("T")[0];
};
const Consorcio: React.FC<Consorcio> = ({
  UsuarioId,
  idAplicacion,
  Suscriptor,
  Suscripcion,
  selectedSolicitud,
  Asignado,
  documentoSubido,
  Nombres,
  Apellidos,
  gestor,
  sinCodigo,
  NomTipoSolicitud 
}) => {
  const [files, setFiles] = useState([]);
  const token = Cookies.get("Token");
  const [errors, setErrors] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [clausulasIncluidas, setClausulasIncluidas] = useState([]);
  const [clausulasSeleccionadas, setClausulasSeleccionadas] = useState([]);
  const [
    interlocutorCliAsociadoEncontrado,
    setInterlocutorCliAsociadoEncontrado,
  ] = useState(false);
  const [selectedTipoMoneda, setSelectedTipoMoneda] = useState("dolares");

  const [idInterlocutorCliAsociado, setIdInterlocutorCliAsociado] =
    useState(null);
  const [newFiles, setNewFiles] = useState([]);
  const [idContenidoSolicitud, setIdContenidoSolicitud] = useState(null);
  const [totalAporte, setTotalAporte] = useState(0);
  const navigate = useNavigate();
  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };
  const [asociados, setAsociados] = useState([createInitialFormData()]);
  const [FechaMinima, setFechaMinima] = useState(getLocalDate());
  const [TiposMoneda, setTiposMoneda] = useState({
    str_Moneda: "",
    str_Pais: "",
  });

  function createInitialFormData() {
    return {
      str_TipoDoc: "Documento de identidad personal",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Documento: "",
      str_RLDocumento: "",
      str_Interlocutor: "",
      str_RepLegal: "",
      str_Domicilio: "",
      int_RLPartida: "",
      str_Obligacion: "",
      int_idInterlocutor: "",
      str_ValorAporte: "",
      str_PorcentajeAporte: "",
      str_ValorServicios: "",
      str_ValorHonorarios: "",
    };
  }
  const handleAddAsociado = () => {
    setAsociados([...asociados, createInitialFormData()]);
  };
  const handleRemoveAsociado = (index) => {
    if (asociados.length === 1) {
      Swal.fire(
        "",
        "No se puede eliminar el único asociado existente",
        "error"
      );
      return;
    }
    setAsociados(asociados.filter((_, i) => i !== index));
  };
  useEffect(() => {
    const sumaAporte = asociados.reduce((sum, asociado) => {
      const aporte = parseFloat(asociado.str_PorcentajeAporte) || 0;
      return sum + aporte;
    }, 0);
    setTotalAporte(sumaAporte);
  }, [asociados]);

  const [formDataClienteAsociado, setFormDataClienteAsociado] = useState({
    str_idSuscripcion: Suscripcion,
    str_Interlocutor: "",
    str_TipoDoc: "Documento de identidad personal",
    str_Documento: "",
    int_idUsuarioCreacion: UsuarioId,
    str_RazonSocial: "",
  });

  const [formDataSolicitud, setFormDataSolicitud] = useState({
    int_idUsuarioCreacion: UsuarioId,
    int_idEmpresa: selectedSolicitud.int_idEmpresa,
    int_idUnidadNegocio: selectedSolicitud.int_idUnidadNegocio,
    int_idTipoSol: selectedSolicitud.int_idTipoSol,
    int_SolicitudGuardada: selectedSolicitud.int_SolicitudGuardada,
    str_DeTerceros: selectedSolicitud.str_DeTerceros,
    dt_FechaEsperada: selectedSolicitud.dt_FechaEsperada,
    db_Honorarios: selectedSolicitud.db_Honorarios,
    str_idSuscriptor: Suscripcion,
    int_idClienteAsociado: selectedSolicitud.int_idClienteAsociado,
    str_CodSolicitudes: selectedSolicitud.str_CodSolicitudes,
    int_idSolicitante: gestor ? selectedSolicitud.int_idSolicitante : UsuarioId,
  });
  const [formDataContenido, setFormDataContenido] = useState<FormDataContenido>(
    {
      str_DocAdjuntos: files.length >= 1 ? "si" : "no",
      str_ObjetivoContrato: "",
      str_ObligacionesConjuntas: "",
      str_TipoServicio: "",
      str_InfoAdicional: "",
      int_idInterlocutor: null,
      int_idInterlocutorComprador: null,
      str_idSuscriptor: Suscripcion,
      str_RentaPactada: "",
      str_Moneda: "dolares",
      str_BienPartidaCertificada: "",
      dt_FechaArriendo: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      str_InfoCompartida: "",
      int_idSolicitudes: "",
    }
  );

  const validateForm = (): boolean => {
    let newErrors: { [key: string]: string } = {};

    if (!formDataSolicitud.int_idEmpresa)
      newErrors.int_idEmpresa = "La empresa es requerida";
    if (!formDataSolicitud.int_idUnidadNegocio)
      newErrors.int_idUnidadNegocio = "La unidad de negocio es requerida";
  
 

    setErrors(newErrors);
    // Retornar true si no hay errores
    return Object.keys(newErrors).length === 0;
  };

  const steps = ["DATOS GENERALES", "DATOS DE CONTRATO"];

  const buscarAsociado = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data; // Retorna los datos del interlocutor
    } catch (error) {
      return null; // Retorna null si hay un error
    }
  };
  const handleChangeTipoMoneda = (event) => {
    const newMoneda = event.target.value;
    setSelectedTipoMoneda(newMoneda);

    setFormDataContenido((prevData) => ({
      ...prevData,
      str_Moneda: newMoneda,
    }));
  };
  useEffect(() => {
    const DatosContenidoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerContenidoSolicitud"](
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data) {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: response.data.str_ObjetivoContrato,
            str_PlazoSolicitud: response.data.str_PlazoSolicitud,
            str_InfoAdicional: response.data.str_InfoAdicional,
            int_idInterlocutor: response.data.int_idInterlocutor,
            int_idInterlocutorComprador:
              response.data.int_idInterlocutorComprador,
            str_BienDescripcion: response.data.str_BienDescripcion,
            str_BienUso: response.data.str_BienUso,
            str_BienDireccion: response.data.str_BienDireccion,
            str_RentaPactada: response.data.str_RentaPactada,
            str_BienMuebleInmueble: response.data.str_BienMuebleInmueble,
            dt_FechaArriendo: response.data.dt_FechaArriendo,
            str_Moneda: response.data.str_Moneda,
            str_BienPartidaCertificada:
              response.data.str_BienPartidaCertificada,
            str_ObligacionesConjuntas: response.data.str_ObligacionesConjuntas,
            str_InfoCompartida: response.data.str_InfoCompartida,
          }));
          setIdContenidoSolicitud(response.data.int_idSolicitudCont);
          const responseConsorcio = await axios.get(
            API_SOLICITANTE["ObtenerDatosConsorcio"](
              response.data.int_idSolicitudCont
            ),
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (responseConsorcio.data) {
            const nuevosAsociados = await Promise.all(
              responseConsorcio.data.map(async (data) => {
                try {
                  const interlocutorData = await buscarAsociado(
                    data.str_Documento
                  );

                  if (interlocutorData) {
                    return {
                      str_TipoDoc: "Documento de identidad personal",
                      str_RLTipoDocumento: "Documento de identidad personal",
                      str_Documento: data.str_Documento,
                      str_RLDocumento: interlocutorData.str_RLDocumento || "",
                      str_Interlocutor: interlocutorData.str_Interlocutor || "",
                      str_RepLegal: interlocutorData.str_RepLegal || "",
                      str_Domicilio: interlocutorData.str_Domicilio || "",
                      int_RLPartida: interlocutorData.int_RLPartida || "",
                      int_idInterlocutor:
                        interlocutorData.int_idInterlocutor || "",
                      str_Obligacion: data.str_Obligacion || "",
                      str_ValorAporte: data.str_ValorAporte || "",
                      str_PorcentajeAporte: data.str_PorcentajeAporte || "",
                      str_ValorServicios: data.str_ValorServicios || "",
                      str_ValorHonorarios: data.str_ValorHonorarios || "",
                    };
                  } else {
                    console.warn(
                      "No se encontraron datos del interlocutor para:",
                      data.str_Documento
                    );
                    return null;
                  }
                } catch (error) {
                  return null;
                }
              })
            );
            const filteredAsociados = nuevosAsociados.filter(Boolean);

            setAsociados(filteredAsociados);
          }
        } else {
          setFormDataContenido((prevData) => ({
            ...prevData,
            str_ObjetivoContrato: "",
            str_PlazoSolicitud: "",
            str_Moneda: "dolares",
            str_TipoServicio: "",
            str_InfoAdicional: "",
            str_ObligacionesConjuntas: "",
            str_BienPartidaCertificada: "",
          }));
        }
      } catch (error) {}
    };
    const ObtenerInterlocutorAsociado = async () => {
      await validateToken();
      try {
        const responseInterlocutor = await axios.get(
          API_GESTOR["BuscarInterlocutorID"](
            selectedSolicitud.int_idClienteAsociado
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (responseInterlocutor.data) {
          setFormDataClienteAsociado((prevData) => ({
            ...prevData,
            str_Documento: responseInterlocutor.data.str_Documento,
          }));
          buscarInterlocutor(responseInterlocutor.data.str_Documento);
        } else {
        }
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    const ObtenerTiempoRespuestaTipoSolicitud = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_GESTOR["ObtenerTiempoRespuestaTS"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedSolicitud.int_idEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const responseData = response.data;
        const ultimoObjeto =
          responseData.length > 0
            ? responseData[responseData.length - 1]
            : null;

        const nuevaFechaEsperada = addDaysToDate(
          new Date(),
          ultimoObjeto.int_TiempoRespuesta
            ? ultimoObjeto.int_TiempoRespuesta
            : 0
        );
        setFechaMinima(nuevaFechaEsperada);
        setFormDataSolicitud((prevState) => ({
          ...prevState,
          dt_FechaEsperada: nuevaFechaEsperada,
        }));
      } catch (error) {
        console.error("Error al obtener tipos de solicitud:", error);
      }
    };
    const fetchClausulasIncluidas = async () => {
      await validateToken();
      try {
        const responseIncluidas = await axios.get(
          API_GESTOR["ObtenerClausulasIncluidas"](
            Suscripcion,
            selectedSolicitud.int_idTipoSol,
            selectedSolicitud.int_idEmpresa
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasIncluidas(responseIncluidas.data);

        const responseActivas = await axios.get(
          API_GESTOR["ObtenerClausulasActivas"](
            Suscripcion,
            selectedSolicitud.int_idSolicitudes
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setClausulasSeleccionadas(responseActivas.data.clausulas_activas);
      } catch (error) {
        console.error("Error al obtener las cláusulas:", error);
      }
    };
    fetchClausulasIncluidas();
    ObtenerTiempoRespuestaTipoSolicitud();
    ObtenerInterlocutorAsociado();
    DatosContenidoSolicitud();
  }, []);
  useEffect(() => {
    const fetchMoneda = async () => {
      await validateToken();
      try {
        const response = await axios.get(
          API_SOLICITANTE["ObtenerMoneda"](
            selectedSolicitud.int_idEmpresa,
            Suscripcion
          ),
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setTiposMoneda(response.data);
      } catch (error) {
        console.error("Error al obtener las empresas:", error);
      }
    };
    fetchMoneda();
  }, [selectedSolicitud.int_idEmpresa]);
  const renderClausulas = () => {
    if (!clausulasIncluidas || clausulasIncluidas.length === 0) {
      return (
        <div className="no-clausulas-mensaje">
          No hay cláusulas asignadas para esta solicitud
        </div>
      );
    }

    const groupedClausulas = [];

    for (let i = 0; i < clausulasIncluidas.length; i += 2) {
      groupedClausulas.push(clausulasIncluidas.slice(i, i + 2));
    }

    return groupedClausulas.map((group, index) => (
      <div className="inputs-crear-solicitud" key={index}>
        {group.map((clausula, idx) => (
          <div className="div-input-crear-solicitud" key={idx}>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="checkbox"
                value={clausula.int_idClausulasIncluidas}
                checked={clausulasSeleccionadas.includes(
                  clausula.int_idClausulasIncluidas
                )}
                onChange={(e) =>
                  handleCheckboxChange(
                    clausula.int_idClausulasIncluidas,
                    e.target.checked
                  )
                }
              />
              <label className="form-check-label">
                {clausula.clausula_legal_nombre}
              </label>
            </div>
          </div>
        ))}
      </div>
    ));
  };
  const handleCheckboxChange = (clausulaId, isChecked) => {
    let updatedClausulas;
    if (isChecked) {
      updatedClausulas = [...clausulasSeleccionadas, clausulaId];
    } else {
      updatedClausulas = clausulasSeleccionadas.filter(
        (id) => id !== clausulaId
      );
    }
    setClausulasSeleccionadas(updatedClausulas);
  };
  const handleFileChange = (event) => {
    if (files.length >= 1) {
      Swal.fire({
        icon: "error",
        title: "",
        text: "Solo se puede adjuntar un archivo",
      });
      event.target.value = null;
      return;
    }
    const fileArray = Array.from(event.target.files);
    setFiles((prevFiles) => [...prevFiles, ...fileArray]);
    event.target.value = null;
  };

  const handleFileRemove = (index) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };
  const handleInputChangeAsociados = (index, event) => {
    const { name, value } = event.target;
    const newAsociados = [...asociados];
    newAsociados[index][name] = value;

    if (name.startsWith("str_TipoDoc")) {
      newAsociados[index].str_TipoDoc = value;
    }
    if (name.startsWith("str_RLTipoDocumento")) {
      newAsociados[index].str_RLTipoDocumento = value;
    }

    setAsociados(newAsociados);
  };
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormDataSolicitud((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormDataContenido((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    if (name.startsWith("CliAsociado_")) {
      const fieldName = name.replace("CliAsociado_", "");
      setFormDataClienteAsociado((prevData) => ({
        ...prevData,
        [fieldName]: value,
      }));

      if (fieldName === "str_Documento") {
        buscarInterlocutor(value);
      }
    }
  };

  const buscarInterlocutor = async (ruc: string) => {
    try {
      const response = await axios.get(
        API_GESTOR["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
          str_Interlocutor: response.data.str_Interlocutor,
          str_RazonSocial: response.data.str_RazonSocial,
        }));
        setIdInterlocutorCliAsociado(response.data.int_idInterlocutor);
        setInterlocutorCliAsociadoEncontrado(true);
      } else {
        setInterlocutorCliAsociadoEncontrado(false);
        setFormDataClienteAsociado((prevData) => ({
          ...prevData,
          str_Interlocutor: "",
          str_RazonSocial: "",
        }));
        setIdInterlocutorCliAsociado(null);
      }
    } catch (error) {
      setInterlocutorCliAsociadoEncontrado(false);
      setFormDataClienteAsociado((prevData) => ({
        ...prevData,
        str_Interlocutor: "",
        str_RazonSocial: "",
      }));
      setIdInterlocutorCliAsociado(null);
    }
  };
  const resetInterlocutor = (index: number) => {
    const newAsociados = [...asociados];
    newAsociados[index] = {
      ...newAsociados[index],
      str_Interlocutor: "",
      str_RepLegal: "",
      int_RLPartida: "",
      str_RLTipoDocumento: "Documento de identidad personal",
      str_Domicilio: "",
      str_RLDocumento: "",
      str_TipoDoc: "Documento de identidad personal",
    };
    setAsociados(newAsociados);
  };

  const buscarInterlocutorAsociado = async (ruc: string, index: number) => {
    await validateToken();
    try {
      const response = await axios.get(
        API_SOLICITANTE["BuscarInterlocutor"](ruc, Suscripcion),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data) {
        // Actualiza solo el asociado correspondiente
        const newAsociados = [...asociados];
        newAsociados[index] = {
          ...newAsociados[index],
          str_Interlocutor: response.data.str_Interlocutor,
          str_RepLegal: response.data.str_RepLegal,
          int_RLPartida: response.data.int_RLPartida,
          str_RLTipoDocumento:
            response.data.str_RLTipoDocumento ||
            "Documento de identidad personal",
          str_TipoDoc:
            response.data.str_TipoDoc || "Documento de identidad personal",
          str_Domicilio: response.data.str_Domicilio,
          str_RLDocumento: response.data.str_RLDocumento,
          int_idInterlocutor: response.data.int_idInterlocutor,
        };
        setAsociados(newAsociados);
      } else {
        // Manejo si no se encuentra el interlocutor
        resetInterlocutor(index);
      }
    } catch (error) {
      resetInterlocutor(index);
    }
  };
  const CambiarEstado = async (Solicitud, Estado) => {
    try {
      await axios.put(
        API_GESTOR["ActualizarEstado"](),
        {
          str_idSuscriptor: Suscripcion,
          nombre_estado: Estado,
          int_idUsuarioCreacion: UsuarioId,
          int_idSolicitudes: Solicitud.int_idSolicitudes,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      return error;
    }
  };
  const handleSubmitConfirmarSolicitud = async (
    idInterlocutorCliAsociado,
    str_CodSolicitudes: string | null | any
  ) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
        str_CodSolicitudes: sinCodigo
          ? str_CodSolicitudes
          : formDataSolicitud.str_CodSolicitudes,
      };
      const response = await axios.post(
        API_GESTOR["CrearAdenda"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        return response.data;
      } else {
        return null;
      }
    } catch (error) {
      return error;
    }
  };
  //------------------------------------CREAR SOLICITUD--------------------------------------------
  const handleSubmitCrearSolicitud = async (idInterlocutorCliAsociado) => {
    try {
      const updatedFormData = {
        ...formDataSolicitud,
        int_idClienteAsociado: idInterlocutorCliAsociado,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
        return response.data;
      } else {
        return null;
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitFiles = async (Solicitud) => {
    await validateToken();
    try {
      for (const file of files) {
        const formDataSolicitud = new FormData();

        formDataSolicitud.append("archivo", file);

        formDataSolicitud.append("str_idSuscriptor", Suscripcion);
        formDataSolicitud.append(
          "str_CodSolicitudes",
          Solicitud.str_CodSolicitudes
        );
        formDataSolicitud.append(
          "int_idSolicitudes",
          Solicitud.int_idSolicitudes
        );
        if(file.tipo_adjunto) {
          formDataSolicitud.append("tipo_adjunto", file.tipo_adjunto);
        }
        formDataSolicitud.append("str_CodTipoDocumento", "DOAD");
        formDataSolicitud.append("int_idUsuarioCreacion", UsuarioId);

        const response = await axios.post(
          API_GESTOR["UploadArchivos"](),
          formDataSolicitud,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el archivo");
        }
      }
    } catch (error) {}
  };

  const handleSubmitInterlocutores = async () => {
    await validateToken();
    try {
      if (interlocutorCliAsociadoEncontrado) {
        const formDataModificadaComprador = {
          ...formDataClienteAsociado,
          int_idUsuarioModificacion: UsuarioId,
        };
        delete formDataModificadaComprador.int_idUsuarioCreacion;

        const response = await axios.put(
          API_GESTOR["ActualizarInterlocutor"](idInterlocutorCliAsociado),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 200) {
          throw new Error("No se pudo actualizar el interlocutor");
        }

        return idInterlocutorCliAsociado;
      } else {
        const response = await axios.post(
          API_GESTOR["AgregarInterlocutor"](),
          formDataClienteAsociado,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status !== 201) {
          throw new Error("No se pudo ingresar el interlocutor");
        }

        return response.data.int_idInterlocutor;
      }
    } catch (error) {
      console.error("Error al gestionar el interlocutor:", error);
    }
  };
  const handleSubmitContenidoSolicitud = async (solicitud) => {
    await validateToken();
    try {
      const updatedFormData = {
        ...formDataContenido,
        int_idSolicitudes: solicitud.int_idSolicitudes,
      };
      const response = await axios.post(
        API_GESTOR["InsertarContenidoSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data.id;
    } catch (error) {
      return error;
    }
  };
  const handleSubmitAsociados = async (idContenidoSolicitud) => {
    await validateToken();
    const responseeliminar = await axios.delete(
      API_SOLICITANTE["EliminarAsociados"](idContenidoSolicitud),
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (responseeliminar.status >= 200 && responseeliminar.status < 300) {
      try {
        for (const asociado of asociados) {
          console.log(asociado.int_idInterlocutor);

          if (asociado.int_idInterlocutor) {
            const formDataModificado = {
              ...asociado,
              int_idUsuarioModificacion: UsuarioId,
            };
            delete formDataModificado.int_idUsuarioCreacion;

            const response = await axios.put(
              API_SOLICITANTE["ActualizarInterlocutor"](
                asociado.int_idInterlocutor
              ),
              formDataModificado,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.status !== 200) {
              throw new Error("No se pudo actualizar el interlocutor");
            }
            await handleCrearConsorcio(
              asociado.int_idInterlocutor,
              asociado.str_ValorAporte,
              asociado.str_PorcentajeAporte,
              asociado.str_ValorServicios,
              asociado.str_ValorHonorarios,
              asociado.str_Obligacion,
              idContenidoSolicitud
            );
          } else {
            const formDataNuevo = {
              ...asociado,
              int_idUsuarioCreacion: UsuarioId,
              str_idSuscripcion: Suscripcion,
            };

            const response = await axios.post(
              API_SOLICITANTE["AgregarInterlocutor"](),
              formDataNuevo,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            await handleCrearConsorcio(
              asociado.int_idInterlocutor,
              asociado.str_ValorAporte,
              asociado.str_PorcentajeAporte,
              asociado.str_ValorServicios,
              asociado.str_ValorHonorarios,
              asociado.str_Obligacion,
              idContenidoSolicitud
            );
            if (response.status !== 201) {
              throw new Error("No se pudo ingresar el interlocutor");
            }
            asociado.int_idInterlocutor = response.data.int_idInterlocutor;
          }
        }
      } catch (error) {}
    }
  };

  const handleCrearConsorcio = async (
    idAsociado: number | string,
    str_ValorAporte: string,
    str_PorcentajeAporte: string,
    str_ValorServicios: string,
    str_ValorHonorarios: string,
    str_Obligacion: string,
    idContenidoSolicitud: number
  ) => {
    try {
      const updatedFormData = {
        int_idInterlocutor: idAsociado,
        str_ValorAporte: str_ValorAporte,
        str_PorcentajeAporte: str_PorcentajeAporte,
        str_ValorServicios: str_ValorServicios,
        str_ValorHonorarios: str_ValorHonorarios,
        int_idUsuarioCreacion: UsuarioId,
        str_Obligacion: str_Obligacion,
        int_idSolicitudCont: idContenidoSolicitud,
      };

      const responseListConsorcio = await axios.post(
        API_GESTOR["AgregarConsorcio"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (
        responseListConsorcio.status >= 200 &&
        responseListConsorcio.status < 300
      ) {
        handlerConsorcioSolicitud(
          responseListConsorcio.data.id,
          idContenidoSolicitud
        );
      } else {
        console.log(responseListConsorcio);
      }
    } catch (error) {
      return error;
    }
  };
  const handlerConsorcioSolicitud = async (
    idConsorcio: number,
    idContenidoSolicitud: number
  ) => {
    await validateToken();
    try {
      const updatedFormData = {
        int_idConsorcio: idConsorcio,
        int_idSolicitudCont: idContenidoSolicitud,
        int_idUsuarioCreacion: UsuarioId,
      };
      const response = await axios.post(
        API_SOLICITANTE["AgregarConsorcioSolicitud"](),
        updatedFormData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status >= 200 && response.status < 300) {
      }
    } catch (error) {
      return error;
    }
  };
  const isInvalidLength = (type, value, requiredLength) =>
    type === "RUC" && value.length < requiredLength && value.length > 0;

  const isInvalidDNILength = (type, value, requiredLength) =>
    type === "DNI" && value.length < requiredLength && value.length > 0;

  const obtenerGestorConMenorSolicitudes = async () => {
    try {
      const response = await axios.get(
        API_SOLICITANTE["ObtenerGestoresTipoSolicitud"](
          Suscripcion,
          selectedSolicitud.int_idTipoSol,
          selectedSolicitud.int_idEmpresa
        ),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const gestores = response.data;

      if (!gestores || gestores.length === 0) {
        return null;
      }

      // Obtener el número de solicitudes activas para cada gestor
      const solicitudesPorGestor = await Promise.all(
        gestores.map(async (gestor) => {
          try {
            const contadorResponse = await axios.get(
              API_SOLICITANTE["ContadorPorGestor"](
                gestor.int_idGestor,
                Suscripcion
              ),
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            const solicitudes_Activas = contadorResponse.data.total_no_firmado;

            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas,
            };
          } catch (error) {
            console.error(
              `Error al contar solicitudes para el gestor ${gestor.int_idGestor}:`,
              error
            );
            return {
              int_idGestor: gestor.int_idGestor,
              solicitudes_Activas: Infinity, // Asignar un valor alto para evitar seleccionarlo
            };
          }
        })
      );

      // Encontrar el gestor con menor número de solicitudes activas
      const gestorConMenorSolicitudes = solicitudesPorGestor.reduce(
        (minGestor, currentGestor) => {
          return currentGestor.solicitudes_Activas <
            minGestor.solicitudes_Activas
            ? currentGestor
            : minGestor;
        },
        { int_idGestor: null, solicitudes_Activas: Infinity }
      );

      return gestorConMenorSolicitudes.int_idGestor;
    } catch (error) {
      return null;
    }
  };
  const handleAsignarGestor = async (idSolicitud, gestorSeleccionado) => {
    await validateToken();
    try {
      const response = await axios.put(
        API_SOLICITANTE["AsignarGestor"](),
        {
          int_idSolicitudes: idSolicitud,
          int_idGestor: gestorSeleccionado,
          int_idUsuarioModificacion: UsuarioId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        console.error(`Gestor Asignado Correctamente ${response}`);
      } else {
        console.error(`Error: código de estado ${response.status}`);
      }
    } catch (error) {
      return error;
    }
  };
  const handleSubmitSolicitudConfirmar = async () => {
    if (asociados.length < 2) {
      Swal.fire(
        "",
        "No pueden haber menos de 2 asociados en la solicitud",
        "error"
      );
      return;
    }
    await validateToken();
    if (totalAporte !== 100) {
      Swal.fire(
        "",
        "La suma de los porcentajes de aporte debe ser 100%",
        "error"
      );
      return;
    }
    if (!validateForm()) {
      const errorMessages = Object.values(errors).pop();
      Swal.fire({
        html: errorMessages || "Faltan rellenar campos",
        icon: "error",
      });
      return;
    }

    let success = true;
    let errorMessages = [];

    try {
      if (sinCodigo) {
        const idInterlocutor = await handleSubmitInterlocutores();
        const solicitudCreada = await handleSubmitCrearSolicitud(
          idInterlocutor.response1
        ).catch((err) => {
          success = false;
          errorMessages.push("Error al crear solicitud: " + err.message);
        });
        const idGestorSeleccionado = await obtenerGestorConMenorSolicitudes();

        await handleAsignarGestor(
          solicitudCreada.int_idSolicitudes,
          gestor ? UsuarioId : idGestorSeleccionado
        );
        const idContenidoSolicitud = await handleSubmitContenidoSolicitud(
          solicitudCreada
        ).catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
        await handleSubmitAsociados(idContenidoSolicitud);
        for (const clausulaId of clausulasSeleccionadas) {
          try {
            await axios.post(
              API_GESTOR["ActivarClausulas"](),
              {
                str_idSuscripcion: Suscripcion,
                int_idSolicitudes: solicitudCreada.int_idSolicitudes,
                int_idClausulaIncluida: clausulaId,
                int_idUsuarioCreacion: UsuarioId,
              },
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
          } catch (e) {
            success = false;
            errorMessages.push(`Error al activar cláusula ${clausulaId}: ${e}`);
          }
        }
        await CambiarEstado(solicitudCreada, "Firmado");

        const idSolicitud = await handleSubmitConfirmarSolicitud(
          idInterlocutor.response1,
          solicitudCreada.str_CodSolicitudes
        ).catch((err) => {
          success = false;
          errorMessages.push("Error al confirmar solicitud: " + err.message);
        });

        if (files.length > 0) {
          await handleSubmitFiles(idSolicitud).catch((err) => {
            success = false;
            errorMessages.push("Error al subir archivos: " + err.message);
          });
        }

        await handleAsignarGestor(
          idSolicitud.int_idSolicitudes,
          gestor ? UsuarioId : idGestorSeleccionado
        );
        await CambiarEstado(idSolicitud, "Nuevo");
        await CambiarEstado(idSolicitud, "Asignado");

        if (gestor) {
          await CambiarEstado(idSolicitud, "En Proceso");
          //await CambiarEstado(idSolicitud,"En Validación")
          //await CambiarEstado(idSolicitud,"Aceptado")
        }
        const idContenidoSolicitudAdenda = await handleSubmitContenidoSolicitud(
          idSolicitud
        ).catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
        await handleSubmitAsociados(idContenidoSolicitudAdenda);
        for (const clausulaId of clausulasSeleccionadas) {
          try {
            await axios.post(
              API_GESTOR["ActivarClausulas"](),
              {
                str_idSuscripcion: Suscripcion,
                int_idSolicitudes: idSolicitud.int_idSolicitudes,
                int_idClausulaIncluida: clausulaId,
                int_idUsuarioCreacion: UsuarioId,
              },
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              }
            );
          } catch (e) {
            success = false;
            errorMessages.push(`Error al activar cláusula ${clausulaId}: ${e}`);
          }
        }
        if (success) {
          Swal.fire({
            title: "",
            text: "Adenda Confirmada.",
            icon: "success",
          }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
          await getLogs(
            JSON.stringify(formDataSolicitud),
            JSON.stringify(selectedSolicitud),
            selectedSolicitud.int_idSolicitudes || null,
            "Solicitudes",
            "Adendas",
            "Crear Adenda",
            "Contratos",
            "POST"
          );
        } else {
          Swal.fire({
            title: "Errores encontrados",
            text: errorMessages.join("\n"),
            icon: "error",
          });
        }
      } else {
        const idInterlocutor = await handleSubmitInterlocutores();
        let idSolicitud = null;
        if (!idInterlocutor) {
          idSolicitud = await handleSubmitConfirmarSolicitud(null, null).catch(
            (err) => {
              success = false;
              errorMessages.push(
                "Error al confirmar solicitud: " + err.message
              );
            }
          );
        } else {
          idSolicitud = await handleSubmitConfirmarSolicitud(
            idInterlocutor,
            null
          ).catch((err) => {
            success = false;
            errorMessages.push("Error al confirmar solicitud: " + err.message);
          });
        }
        const idContenidoSolicitud = await handleSubmitContenidoSolicitud(
          idSolicitud
        ).catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
        await handleSubmitAsociados(idContenidoSolicitud);
        await handleSubmitFiles(idSolicitud).catch((err) => {
          success = false;
          errorMessages.push("Error al subir archivos: " + err.message);
        });
        const idGestorSeleccionado = await obtenerGestorConMenorSolicitudes();

        await handleAsignarGestor(
          idSolicitud.int_idSolicitudes,
          gestor ? UsuarioId : idGestorSeleccionado
        );
        await CambiarEstado(idSolicitud, "Nuevo");
        await CambiarEstado(idSolicitud, "Asignado");

        if (gestor) {
          await CambiarEstado(idSolicitud, "En Proceso");
          //await CambiarEstado(idSolicitud,"En Validación")
          //await CambiarEstado(idSolicitud,"Aceptado")
        }
        if (success) {
          Swal.fire({
            title: "",
            text: "Solicitud Confirmada.",
            icon: "success",
          }).then(() => navigate(RoutesPrivate.INICIOGESTOR));
          await getLogs(
            JSON.stringify(formDataSolicitud),
            JSON.stringify(selectedSolicitud),
            selectedSolicitud.int_idSolicitudes || null,
            "Solicitudes",
            "Adendas",
            "Crear Adenda",
            "Contratos",
            "POST"
          );
        } else {
          Swal.fire({
            title: "Errores encontrados",
            text: errorMessages.join("\n"),
            icon: "error",
          });
        }
      }
    } catch (error) {
      Swal.fire({
        title: "Error inesperado",
        text: "Ocurrió un error inesperado: " + error.message,
        icon: "error",
      });
    }
  };

  return (
    <div className="div-container-tabla-inicio-solicitante">
      <div className="div-contenido-crear-solicitud">
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} onClick={() => handleStepChange(index)}>
                <StepLabel className="nombres-stepper">{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        <div className="container-acordion-crear-solicitud comfortaa-font">
          <div className="accordion" id="accordionPanelsStayOpenExample">
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingOne">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseOne"
                  aria-expanded="true"
                  aria-controls="panelsStayOpen-collapseOne"
                  onClick={() => handleStepChange(0)}
                >
                  Datos generales
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseOne"
                className="accordion-collapse collapse show"
                aria-labelledby="panelsStayOpen-headingOne"
              >
                <div className="accordion-body">
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Registrado Por:</label>
                      <input
                        type="text"
                        className="form-control"
                        value={Nombres + " " + Apellidos}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">
                        Empresa(Razón Social):
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_NombreEmpresa"
                        value={selectedSolicitud.Empresa_nombre}
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Unidad de Negocio:</label>
                      <input
                        type="text"
                        className="form-control"
                        name="str_Descripcion_UnidadNegocio"
                        value={selectedSolicitud.UN_nombre}
                        readOnly
                      />
                    </div>
                    <div className="div-input-crear-solicitud">
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha de Registro:{" "}
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={getLocalDate()}
                          className="form-control"
                          value={getLocalDate()}
                          name="dt_FechaRegistro"
                          readOnly
                        />
                      </div>
                      <div className="div-input-fecha-solicitud">
                        <label className="form-label">
                          Fecha Esperada de entrega:
                        </label>
                        <input
                          type="date"
                          onKeyDown={(e) => e.preventDefault()}
                          min={FechaMinima}
                          className="form-control"
                          value={
                            formDataSolicitud.dt_FechaEsperada
                              ? formDataSolicitud.dt_FechaEsperada.split("T")[0]
                              : ""
                          }
                          onChange={handleInputChange}
                          name="dt_FechaEsperada"
                        />
                      </div>
                    </div>
                  </div>
                  {asociados.map((asociado, index) => (
                    <div key={index}>
                      <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                        <h5>Datos del Asociado {index + 1}</h5>
                        <div className="div-eliminarAsociado">
                          <div
                            className="eliminar-asociado"
                            onClick={() => handleRemoveAsociado(index)}
                          >
                            <IconoBasureroEliminar
                              size={"1rem"}
                              color={"#294FCF"}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Tipo de Documento:
                          </label>
                          <select
                            name={`str_TipoDoc_${index}`}
                            id="tipoDocumento"
                            className="form-select"
                            onChange={(event) =>
                              handleInputChangeAsociados(index, event)
                            }
                            value={asociado.str_TipoDoc || ""}
                          >
                            <option value="Documento de identidad personal">
                              Documento de identidad personal
                            </option>
                            <option value="Documento de identidad de empresa">
                              Documento de identidad de empresa
                            </option>
                            <option value="Pasaporte">Pasaporte</option>
                            <option value="Carnet de Extranjería">
                              Carnet de Extranjería
                            </option>
                          </select>
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Tipo Documento Representante:
                          </label>

                          <select
                            name={`str_RLTipoDocumento${index}`}
                            id="tipoDocumento"
                            className="form-select"
                            onChange={(event) =>
                              handleInputChangeAsociados(index, event)
                            }
                            value={asociado.str_RLTipoDocumento || ""}
                          >
                            <option value="Documento de identidad personal">
                              Documento de identidad personal
                            </option>
                            <option value="Documento de identidad de empresa">
                              Documento de identidad de empresa
                            </option>
                            <option value="Pasaporte">Pasaporte</option>
                            <option value="Carnet de Extranjería">
                              Carnet de Extranjería
                            </option>
                          </select>
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            {" "}
                            Documento del Asociado:
                          </label>

                          <input
                            type="number"
                            onKeyDown={(e) => {
                              if (["e", "E", "+", "-", "."].includes(e.key)) {
                                e.preventDefault();
                              }
                            }}
                            onWheel={(e) => e.target.blur()}
                            className="form-control"
                            name="str_Documento"
                            value={asociado.str_Documento}
                            onChange={(e) => {
                              const maxLength = 15;
                              const value = e.target.value;

                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index, e);

                                if (value.length <= 15 && value.length >= 8) {
                                  buscarInterlocutorAsociado(value, index);
                                }
                              }
                            }}
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            {" "}
                            Documento del Representante:
                          </label>

                          <input
                            type="number"
                            onKeyDown={(e) => {
                              if (["e", "E", "+", "-", "."].includes(e.key)) {
                                e.preventDefault();
                              }
                            }}
                            onWheel={(e) => e.target.blur()}
                            name="str_RLDocumento"
                            className="form-control"
                            value={asociado.str_RLDocumento}
                            placeholder=""
                            onChange={(e) => {
                              const maxLength = 15;
                              const value = e.target.value;

                              if (value.length <= maxLength) {
                                handleInputChangeAsociados(index, e);
                              }
                            }}
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Asociado {index + 1}:
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="str_Interlocutor"
                            value={asociado.str_Interlocutor}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Representante Legal:
                          </label>
                          <input
                            type="text"
                            name="str_RepLegal"
                            className="form-control"
                            value={asociado.str_RepLegal}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">Domicilio:</label>
                          <input
                            type="text"
                            className="form-control"
                            name="str_Domicilio"
                            value={asociado.str_Domicilio}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>

                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Partida Registral :{" "}
                          </label>
                          <input
                            type="text"
                            name="int_RLPartida"
                            className="form-control"
                            value={asociado.int_RLPartida}
                            placeholder=""
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          />
                        </div>
                      </div>
                      <div className="inputs-crear-solicitud">
                        <div className="div-input-crear-solicitud">
                          <label className="form-label">
                            Obligaciones del Asociado {index + 1}
                          </label>
                          <textarea
                            className="form-control"
                            id=""
                            value={asociado.str_Obligacion}
                            name="str_Obligacion"
                            rows={3}
                            onChange={(e) =>
                              handleInputChangeAsociados(index, e)
                            }
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="boton-crear-asociado">
                    <button
                      type="button"
                      className="btn-asignar"
                      onClick={handleAddAsociado}
                    >
                      Agregar Asociado
                    </button>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Moneda de Contrato</h5>
                  </div>
                  <div className="div-input-crear-solicitud">
                    <div className="radio-inputs-crear-solicitud">
                      {TiposMoneda.str_Moneda !== "Dolares" && (
                        <div
                          className={`check-group form-check-inline ${
                            selectedTipoMoneda === "dolares"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="inlineRadioOptions"
                            id="monedaDolares"
                            value="dolares"
                            checked={selectedTipoMoneda === "dolares"}
                            onChange={handleChangeTipoMoneda}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="monedaDolares"
                          >
                            <IconoDolares size={"1.5rem"} color={"#156CFF"} />{" "}
                            Dólares
                          </label>
                        </div>
                      )}

                      {Object.keys(TiposMoneda).length > 0 && (
                        <div
                          className={`check-group form-check-inline ${
                            selectedTipoMoneda === "empresa"
                              ? "check-selected"
                              : ""
                          }`}
                        >
                          <input
                            className="form-check-input"
                            type="radio"
                            name="tipoMoneda"
                            id="tipoMoneda"
                            value={"empresa"}
                            checked={selectedTipoMoneda === "empresa"}
                            onChange={handleChangeTipoMoneda}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="tipoMoneda"
                          >
                            <IconoMoneda size={"1.5rem"} color={"#156CFF"} />{" "}
                            {TiposMoneda.str_Moneda}
                          </label>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Valoración de aportes del consorcio </h5>
                  </div>
                  {asociados.map((asociado, index) => (
                    <div className="inputs-crear-solicitud" key={index}>
                      <div className="div-input-crear-solicitud">
                        <label className="form-label">
                          Aporte Asociado {index + 1} (Valor):
                        </label>
                        <input
                          type="number"
                          onKeyDown={(e) => {
                            if (["e", "E", "+", "-", "."].includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          onWheel={(e) => e.target.blur()}
                          className="form-control"
                          name="str_ValorAporte"
                          value={asociado.str_ValorAporte}
                          onChange={(e) => handleInputChangeAsociados(index, e)}
                        />
                      </div>
                      <div className="div-input-crear-solicitud">
                        <label className="form-label">
                          Aporte Asociado {index + 1} (%):
                        </label>
                        <input
                          type="number"
                          onKeyDown={(e) => {
                            if (["e", "E", "+", "-", "."].includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          onWheel={(e) => e.target.blur()}
                          className="form-control"
                          name="str_PorcentajeAporte"
                          value={asociado.str_PorcentajeAporte}
                          onChange={(e) => handleInputChangeAsociados(index, e)}
                        />
                      </div>
                    </div>
                  ))}
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Valoración de Servicios contratados via consorcio </h5>
                  </div>
                  {asociados
                    .reduce((rows, asociado, index) => {
                      if (index % 2 === 0) {
                        rows.push([]);
                      }
                      rows[rows.length - 1].push(asociado);
                      return rows;
                    }, [])
                    .map((fila, filaIndex) => (
                      <div
                        className="row"
                        key={filaIndex}
                        style={{
                          display: "flex",
                          justifyContent: "left",
                          gap: "80px",
                        }}
                      >
                        {fila.map((asociado, inputIndex) => (
                          <div
                            className="div-input-crear-solicitud"
                            key={inputIndex}
                          >
                            <label className="form-label">
                              Servicios Asociado{" "}
                              {filaIndex * 2 + inputIndex + 1} :
                            </label>
                            <input
                              type="text"
                              className="form-control"
                              name="str_ValorServicios"
                              value={asociado.str_ValorServicios}
                              onChange={(e) =>
                                handleInputChangeAsociados(
                                  filaIndex * 2 + inputIndex,
                                  e
                                )
                              }
                            />
                          </div>
                        ))}
                      </div>
                    ))}

                  {asociados
                    .reduce((rows, asociado, index) => {
                      if (index % 2 === 0) {
                        rows.push([]);
                      }
                      rows[rows.length - 1].push(asociado);
                      return rows;
                    }, [])
                    .map((fila, filaIndex) => (
                      <div
                        className="row"
                        key={filaIndex}
                        style={{
                          display: "flex",
                          justifyContent: "left",
                          gap: "80px",
                        }}
                      >
                        {fila.map((asociado, inputIndex) => (
                          <div
                            className="div-input-crear-solicitud"
                            key={inputIndex}
                          >
                            <label className="form-label">
                              Honorarios Asociado{" "}
                              {filaIndex * 2 + inputIndex + 1} (Valor):
                            </label>
                            <input
                              type="number"
                              onKeyDown={(e) => {
                                if (["e", "E", "+", "-", "."].includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                              onWheel={(e) => e.target.blur()}
                              className="form-control"
                              name="str_ValorHonorarios"
                              value={asociado.str_ValorHonorarios}
                              onChange={(e) =>
                                handleInputChangeAsociados(
                                  filaIndex * 2 + inputIndex,
                                  e
                                )
                              }
                            />
                          </div>
                        ))}
                      </div>
                    ))}
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Inclusión de Clausulas</h5>
                  </div>
                  {renderClausulas()}
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header" id="panelsStayOpen-headingThree">
                <button
                  className={`accordion-button montserrat-font ${
                    activeStep === 0 ? "" : "collapsed"
                  }`}
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#panelsStayOpen-collapseThree"
                  aria-expanded="false"
                  aria-controls="panelsStayOpen-collapseThree"
                  onClick={() => handleStepChange(1)}
                >
                  Condiciones del Servicio
                </button>
              </h2>
              <div
                id="panelsStayOpen-collapseThree"
                className="accordion-collapse collapse"
                aria-labelledby="panelsStayOpen-headingThree"
              >
                <div className="accordion-body">
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    Asociación a cuenta del cliente
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Cliente:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Interlocutor"
                        value={formDataClienteAsociado.str_Interlocutor}
                        onChange={handleInputChange}
                      />
                      {errors.str_Interlocutor && (
                        <span className="error-message">
                          {errors.str_Interlocutor}
                        </span>
                      )}
                    </div>
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Número de Documento:</label>
                      <input
                        type="number"
                        onKeyDown={(e) => {
                          if (["e", "E", "+", "-", "."].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        onWheel={(e) => e.target.blur()}
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_Documento"
                        value={formDataClienteAsociado.str_Documento}
                        onChange={(e) => {
                          const maxLength = 15;
                          const value = e.target.value;

                          if (value.length <= maxLength) {
                            handleInputChange(e);

                            if (value.length <= 15 && value.length >= 8) {
                              buscarInterlocutor(value);
                            }
                          }
                        }}
                      />
                      {errors.str_Documento && (
                        <span className="error-message">
                          {errors.str_Documento}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Razón Social:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="CliAsociado_str_RazonSocial"
                        value={formDataClienteAsociado.str_RazonSocial}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div className="titulo-solicitud-interlocutores comfortaa-font-600">
                    <h5>Asociación a contrato del cliente</h5>
                  </div>
                  <div className="inputs-crear-solicitud">
                    <div className="div-input-crear-solicitud">
                      <label className="form-label">Codigo de Contrato:</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder=""
                        name="str_InfoCompartida"
                        value={formDataContenido.str_InfoCompartida}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BarraLateralCrearAdenda
        files={files}
        handleFileChange={handleFileChange}
        handleFileRemove={handleFileRemove}
        handleSubmitSolicitudConfirmar={handleSubmitSolicitudConfirmar}
        NomTipoSolicitud={NomTipoSolicitud }
      />
    </div>
  );
};

export default Consorcio;
