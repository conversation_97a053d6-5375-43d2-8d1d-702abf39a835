// import React, { useEffect, useState } from "react";
// import { RoutesPrivate } from "../../../../../Security/Routes/ProtectedRoute";
// import { useNavigate } from "react-router-dom";
// import API_GESTOR from "../../../../../assets/Api/ApisGestor";
// import Cookies from "js-cookie";
// import axios from "axios";
// import Select from "react-select";
// import Grafico1 from "./Grafico1";
// import Grafico2 from "./Grafico2";
// import Grafico4 from "./Grafico4";
// import Grafico3 from "./Grafico3";
// const anioActual = new Date().getFullYear();

// const anios = Array.from({ length: 21 }, (_, i) => 2010 + i);

// const GraficosInicio = ({ idUsuario, Suscripcion }) => {
//   const navigate = useNavigate();
//   const token = Cookies.get("Token");
//   const Suscriptor = Cookies.get("suscriptor");
//   const idAplicacion = Cookies.get("idAplicacion");
//   const [empresasFiltro, setEmpresasFiltro] = useState([]);
//   const [selectedEmpresaFiltro, setSelectedEmpresaFiltro] = useState("");
//   const [aniosSeleccionados, setAniosSeleccionados] = useState(anioActual);
//   const [tipografico, setTipografico] = useState(1);
//   console.log(tipografico)
//   const handleChangeEmpresaFiltro = (
//     selectedOption: React.SetStateAction<null>
//   ) => {
//     setSelectedEmpresaFiltro(selectedOption);
//     
//   };

//   const handleAnioChange = (e) => {
    
//     setAniosSeleccionados(e.target.value);
//   };
//   const handlechangeTipoGrafico = (e) => {
//     setTipografico(Number(e.target.value));
//   };
//   useEffect(() => {
//     const fetchEmpresasFiltro = async () => {
//       try {
//         const response = await axios.get(
//           API_GESTOR["ObtenerEmpresas"](idAplicacion, Suscriptor),
//           {
//             headers: {
//               "Content-Type": "application/json",
//               Authorization: `Bearer ${token}`,
//             },
//           }
//         );
//         const opciones = response.data.map(
//           (tipo: { int_idEmpresa: any; str_NombreEmpresa: any }) => ({
//             value: tipo.int_idEmpresa,
//             label: tipo.str_NombreEmpresa,
//           })
//         );
//         setEmpresasFiltro(opciones);
//         if (opciones.length > 0) {
//           setSelectedEmpresaFiltro(opciones[0]);
//         }
//       } catch (error) {
//         console.error("Error al obtener tipos de solicitud:", error);
//         setEmpresasFiltro([])
//       }
//     };

//     fetchEmpresasFiltro();
//   }, []);
//   const Reportes = () => {
//     navigate(RoutesPrivate.REPORTESGESTOR, {
//       state: { idUsuario, Suscripcion },
//     });
//   };

//   return (
//     <div className="Grafico-Inicio-Gestor">
//       <div className="header-estadisticas-inicio-gestor" onClick={Reportes}>
//         <div className="titulo-estadisticas-inicio-gestor">Graficos</div>
//         <i className="fa-solid fa-caret-right"></i>
//       </div>
//       <div className="container-Grafico-Inicio-Gestor">
//         <div className="div-select-tipo-grafico">
//           <select
//             name=""
//             id=""
//             onChange={handlechangeTipoGrafico}
//             className="form-select"
//           >
//             <option value="1">
//               Solicitudes pendientes por unidad de negocio
//             </option>
//             <option value="2">Top 10 presupuestos por unidad de negocio</option>
//             <option value="3">
//               Comparación de Solicitudes: Solicitudes creadas vs Firmadas
//             </option>
//             <option value="4">
//               Top 10 Horas trabajadas por unidad de negocio
//             </option>
//           </select>
//         </div>
//         <div className="div-select-empresa-anio-inicio-gestor">
//           <Select
//             options={empresasFiltro}
//             value={empresasFiltro.find(
//               (option) => option === selectedEmpresaFiltro
//             )}
//             onChange={handleChangeEmpresaFiltro}
//             placeholder="Empresa"
//           />
//           <select
//             className="form-select"
//             aria-label="Default select example"
//             value={aniosSeleccionados}
//             onChange={handleAnioChange}
//             style={{ width: "20%", height: "100%" }}
//           >
//             <option disabled>Año</option>
//             {anios.map((anio) => (
//               <option key={anio} value={anio}>
//                 {anio}
//               </option>
//             ))}
//           </select>
//         </div>

//         {tipografico === 1 ? (
//   <Grafico1
//     idUsuario={idUsuario}
//     Suscripcion={Suscripcion}
//     aniosSeleccionados={aniosSeleccionados}
//     selectedEmpresaFiltro={selectedEmpresaFiltro}
//     tipografico={tipografico}
//   />
// ) : tipografico === 2 ? (
//   <Grafico2
//     idUsuario={idUsuario}
//     Suscripcion={Suscripcion}
//     aniosSeleccionados={aniosSeleccionados}
//     selectedEmpresaFiltro={selectedEmpresaFiltro}
//     tipografico={tipografico}
//   />
// ) : tipografico === 3 ? (
//   <Grafico3
//     idUsuario={idUsuario}
//     Suscripcion={Suscripcion}
//     aniosSeleccionados={aniosSeleccionados}
//     selectedEmpresaFiltro={selectedEmpresaFiltro}
//     tipografico={tipografico}
//   />
// ) : (
//   <Grafico4
//     idUsuario={idUsuario}
//     Suscripcion={Suscripcion}
//     aniosSeleccionados={aniosSeleccionados}
//     selectedEmpresaFiltro={selectedEmpresaFiltro}
//     tipografico={tipografico}
//   />
// )}
//       </div>
//     </div>
//   );
// };

// export default GraficosInicio;
